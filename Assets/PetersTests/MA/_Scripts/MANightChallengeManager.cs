using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.CompilerServices;
using NUnit.Framework;
using TMPro;
using UnityEngine;
using UnityEngine.Serialization;


public class MANightChallengeManager : MonoSingleton<MANightChallengeManager>
{
    public enum NightTypes
    {
        None = 0,
        Endless,
        TimedWaves,
        Count,
        Escort,
        BeaconAttack,
        BeaconAttackCrypt,
        ResourceAttack
    }
    public class NightTracker
    {
        public enum TrackerType
        {
            Survive, //Survive the night
            CreatureKills, //Kill <N> creatures
            NumWorkerDeaths, //Tracks worker deaths
            Time, //Complete in <N> Seconds
            NumResorcesTaken, //Tracks resources taken
            NumHeroDeaths, //Tracks hero deaths
        }

        public NightTracker(TrackerType _trackerType, string _description, float _count, List<string> _rewards, TMP_Text _textLine)
        {
            m_trackerType = _trackerType;
            m_description = $"> {_description}";
            m_originalDescription = $"> {_description}";
            m_count = _count;
            m_originalCount = _count;
            m_rewards = _rewards;
            m_textLine = _textLine;
            m_timeStarted = Time.time;
        }
        public TrackerType m_trackerType;
        public string m_description;
        public string m_originalDescription;
        public float m_count;
        public float m_originalCount;
        public List<string> m_rewards;
        public bool m_achived = false;
        public bool m_failed = false;
        public TMP_Text m_textLine;
        public float m_timeStarted;
    }

    public class NightChallengeInfo
    {
        public bool isCompleted = false;
        public bool isFailed = false;
        public NightTypes type = NightTypes.None;
        public int day = 0;

        public int killedZombies = 0;
        public int killedUndead = 0;
        public int killedBandits = 0;
        public int killedHeros = 0;
        public int killedWorkers = 0;
        public int killedWorshippers = 0;
        public int killedEscortees = 0;

        public int creaturesKilledByHero = 0;
        public int creaturesKilledByHand = 0;

        public int stolenOre = 0;
        public int stolenWheat = 0;
        public int stolenCotton = 0;

        public int savedEscortees = 0;
        public int spawnedEscortees = 0;

        public int enemiesToKill = 0;
        public int resourcesToSave = 0;
        public int escorteeDeadMax = 0;

        public float m_nightTypeValue;

        public bool HasStarted
        {
            get { return (day > 0) && (type != NightTypes.None); }
        }
        
        public int NightCreaturesKilled
        {
            get { return killedZombies + killedUndead; }
        }

        public int StolenResources
        {
            get { return stolenOre + stolenWheat + stolenCotton; }
        }

        public void Initialize(MACalenderInfo calenderInfo)
        {
            isCompleted = false;
            isFailed = false;
            type = Enum.Parse<NightTypes>(calenderInfo.m_nightType);
            float.TryParse(calenderInfo.m_nightTypeData, out m_nightTypeValue);
            day = calenderInfo.m_dayNumber;

            killedZombies = 0;
            killedUndead = 0;
            killedBandits = 0;
            killedHeros = 0;
            killedWorkers = 0;
            killedWorshippers = 0;
            killedEscortees = 0;

            creaturesKilledByHero = 0;
            creaturesKilledByHand = 0;

            stolenOre = 0;
            stolenWheat = 0;
            stolenCotton = 0;

            savedEscortees = 0;
            spawnedEscortees = 0;

            enemiesToKill = 0;
            resourcesToSave = 0;
            escorteeDeadMax = 0;

            ParseNightData(calenderInfo.m_nightTypeData);
        }

        private void ParseNightData(string nightData)
        {
            switch (type)
            {
                case NightTypes.Count:
                    enemiesToKill = int.Parse(nightData);
                    break;
                case NightTypes.ResourceAttack:
                    resourcesToSave = int.Parse(nightData);
                    break;
                case NightTypes.Escort:
                    escorteeDeadMax = int.Parse(nightData);
                    break;
            }
        }

        public void Reset()
        {
            day = 0;
            type = NightTypes.None;
        }
    }

    public Animator m_anim;
    public TMP_Text m_titleText;
    public GameObject m_background;
    [FormerlySerializedAs("m_challlengeTextHolder")] public Transform m_rewardTextHolder;
    public GameObject m_challengeTextPrefab;
    public List<NightTracker> m_nightTracker = new(); //List of all tracked challenges
    public MACalenderInfo m_calenderInfo = null; // reference to calender info

    private NightChallengeInfo nightChallengeInfo = new NightChallengeInfo();
    private int lastDayLoaded = 0;

    public bool HasStarted
    {
        get { return nightChallengeInfo.HasStarted; }
    }

    public bool IsCompletedOrFailed
    {
        get { return nightChallengeInfo.isCompleted || nightChallengeInfo.isFailed; }
    }

    public bool IsNightOver
    {
        get { return !DayNight.Me.m_isFullNight && (DayNight.Me.m_day > nightChallengeInfo.day); }
    }

    void Update()
    {
        m_background.SetActive(GameManager.Me.m_state.m_nightChallegeDialogActive);
        if (!GameManager.Me.LoadComplete || GameManager.Me.m_state.m_nightChallegeDialogActive == false)
            return;
        
        
        int day = DayNight.Me.CurrentWorkingDay;
        if (lastDayLoaded < day)
        {
            lastDayLoaded = day;
            m_calenderInfo = MACalenderInfo.GetInfo(day);
            SetupNewNightRewards();
        }
        if (!string.IsNullOrEmpty(m_calenderInfo.m_nightType) && (day == m_calenderInfo.m_dayNumber)
                && DayNight.Me.m_isFullNight && !nightChallengeInfo.HasStarted)
        {
            nightChallengeInfo.Initialize(m_calenderInfo);
            PrepareNightRewards();
            InitSpawnedCreatures();
        }

        if (nightChallengeInfo.HasStarted)
        {
            TrackNightRewards(false);
            DisplayNightRewards();
        }
    }
    
    void DisplayNightRewards()
    {
        //Check main title text
        switch (nightChallengeInfo.type)
        {
            case NightTypes.Count:
                var remainingValue = nightChallengeInfo.m_nightTypeValue-(nightChallengeInfo.killedZombies+ nightChallengeInfo.killedUndead+ nightChallengeInfo.killedBandits);
                if(remainingValue > 0)
                    m_titleText.text= m_calenderInfo.m_nightShortText.Replace("[Value]", $"{remainingValue}");
                else
                    m_titleText.text = "Use speed up to Daytime";
                break;
            default:
                m_titleText.text= m_calenderInfo.m_nightShortText;
                break;
        }

        foreach (var nt in m_nightTracker)
        {
            if (nt.m_textLine == null) continue;
     //       nt.m_textLine.fontStyle &= ~TMPro.FontStyles.Strikethrough; 
            var tick =  "";
            var end = "";
            if(nt.m_achived)
                tick = "<sprite=6>";
            else if (nt.m_failed)
            {
                tick = "<sprite=5><s>";
                end="</s>";
       //         nt.m_textLine.fontStyle |= TMPro.FontStyles.Strikethrough;
            }
            nt.m_textLine.text = $"{tick}{nt.m_description}{end}";
        }

    }
    void TrackNightRewards(bool _isLastUpdate)
    {
        m_rewardTextHolder.gameObject.SetActive(true);
        foreach (var nt in m_nightTracker)
        {
            nt.m_description = nt.m_originalDescription.Replace("[Value]", nt.m_count.ToString());
            switch (nt.m_trackerType)
            {
                case NightTracker.TrackerType.Survive:
                    nt.m_achived = _isLastUpdate;
                    break;
                case NightTracker.TrackerType.CreatureKills:
                    var deathCount = nt.m_originalCount-(nightChallengeInfo.killedZombies+ nightChallengeInfo.killedUndead+ nightChallengeInfo.killedBandits);
                    nt.m_count= Mathf.Max(0, deathCount);
                    nt.m_achived = nt.m_count <= 0;
                    if (nt.m_achived)
                    {
                        nt.m_description = "Killed all required creatures.";
                    }
                    break;
                case NightTracker.TrackerType.NumHeroDeaths:
                   //nt.m_achived = nightChallengeInfo.killedHeros < nt.m_count;
                   nt.m_failed = nightChallengeInfo.killedHeros >= nt.m_count;
                   nt.m_achived = _isLastUpdate;
                   break;
                case NightTracker.TrackerType.NumWorkerDeaths:
                    nt.m_count= Mathf.Max(0, nt.m_originalCount - nightChallengeInfo.killedWorkers );
                    //nt.m_achived = nightChallengeInfo.killedWorkers < nt.m_originalCount;
                    nt.m_failed = nightChallengeInfo.killedWorkers >= nt.m_originalCount;
                    nt.m_achived = _isLastUpdate;
                    break;
                case NightTracker.TrackerType.Time:
                    var timeTaken = (Time.time - nt.m_timeStarted);
                    nt.m_failed = timeTaken >= nt.m_count;
                    if (nt.m_failed)
                        nt.m_description = "Run out of time.";
                    else
                    {
                        var timeRemaining = (nt.m_count - timeTaken).ToHMS();
                        nt.m_description = $"{nt.m_originalDescription.Replace("[Value]", timeRemaining)}";
                    }

                    nt.m_achived = _isLastUpdate;
                    break;
                case NightTracker.TrackerType.NumResorcesTaken:
                    var resourcesGone = nightChallengeInfo.stolenCotton+nightChallengeInfo.stolenCotton+nightChallengeInfo.stolenWheat;
                    nt.m_failed = resourcesGone >= nt.m_count;
                    nt.m_achived = _isLastUpdate;
                    break;
            }
        }
    }
    public void ResolveNighttimeRewards()
    {
        TrackNightRewards(true);
        foreach (var nt in m_nightTracker)
        {
            if(nt.m_failed == true) continue;
            if (nt.m_achived == false) continue;
            foreach (var reward in nt.m_rewards)
            {
                var rewardSplit = reward.Split('=');
                if (rewardSplit.Length < 2) continue;
                var rewardType = rewardSplit[0].ToLower().Trim();
                float.TryParse(rewardSplit[1], out float rewardValue);
                switch (rewardType)
                {
                    case "peoplesfavour":
                        MAParser.GiveGift("NightRewardPeoplessFavourGift", rewardValue);
                        break;
                    case "lordsfavour":
                        MAParser.GiveGift("NightRewardLordsFavourGift", rewardValue);
                        break;
                    case "royalfavour":
                        MAParser.GiveGift("NightRewardLRoyalFavourGift", rewardValue);
                        break;
                    case "mysticfavour":
                        MAParser.GiveGift("NightRewardMysticFavourGift", rewardValue);
                        break;
                    case "cash":
                        MAParser.GiveGift("NightRewardCashGift", rewardValue);
                        break;
                    case "gift":
                        MAParser.GiveGift(rewardSplit[1]);
                        break;
                    case "best":
                        var bestGift = GetIdealReward(true);
                        MAParser.GiveGift(bestGift.giftName, rewardValue* bestGift.multiplier);
                        break;
                    case "worst":
                        var worstGift = GetIdealReward(false);
                        MAParser.GiveGift(worstGift.giftName, rewardValue* worstGift.multiplier);
                        break;
                }
                
            }
        }

        ResetNightGUI();
        
    }

    void ResetNightGUI()
    {
        m_rewardTextHolder.gameObject.SetActive(false);
        m_titleText.text = $"Day {DayNight.Me.CurrentWorkingDay}:Night Challenge";
    }
    class IdealReward
    {
        public string name;
        public string giftName;
        public float value;
        public float multiplier;
    }

    private static List<IdealReward> m_idealRewards = new()
    {
        new IdealReward {name = "cash", giftName = "NightRewardCashGift", multiplier = 1.0f},
        new IdealReward {name = "PeoplesFavour", giftName = "NightRewardPeoplesFavourGift", multiplier = 3.0f},
        new IdealReward {name = "LordsFavour", giftName = "NightRewardLordsFavourGift", multiplier = 6.0f},
        new IdealReward {name = "RoyalFavour", giftName = "NightRewardRoyalFavourGift", multiplier = 8.0f},
        new IdealReward {name = "MysticFavour", giftName = "NightRewardMysticFavourGift", multiplier = 2.0f},
    };
    public static (string giftName, float multiplier) GetIdealReward(bool _isBest)
    {
        IdealReward idealReward = null;
        float idealValue = (_isBest ? float.MaxValue : float.MinValue);
        foreach (var ir in m_idealRewards)
        {
            var value = NGPlayer.Me.GetCurrency(ir.name) * ir.multiplier;
            if(_isBest && value < idealValue)
            {
                idealValue = value;
                idealReward = ir;
            }
            else if(_isBest == false && value > idealValue)
            {
                idealValue = value;
                idealReward = ir;
            }
        }

        if (idealReward != null)
        {
            return (idealReward.giftName, idealReward.multiplier);
        }

        return (null, -1f);
    }

    private void InitSpawnedCreatures()
    {
        nightChallengeInfo.spawnedEscortees = 0;
        foreach (var creature in NGManager.Me.m_MACreatureList)
        {
            if (creature.IsEscortedCreataure)
                ++nightChallengeInfo.spawnedEscortees;
        }
    }

    public void Enable(bool _enable)
    {
        GameManager.Me.m_state.m_nightChallegeDialogActive = _enable;
        m_background.SetActive(_enable);
        if (_enable)
        {
            MAParser.ShowHelperGUI("MANightChallengeManager", "NightChallenge", "<LMB> for details", -1, -260, -2, "TopMiddle", "Right");
            m_anim.SetTrigger("Appear");
            m_titleText.text = $"Day {DayNight.Me.CurrentWorkingDay}:Night Challenge";
        }
    }

    
    public void ClickedChallenge()
    {
        var info = MACalenderInfo.GetInfo(DayNight.Me.CurrentWorkingDay);
        MANightChallengeDialog.Create(info);
    }
    void PrepareNightRewards()
    {
        foreach (var nr in m_nightTracker)
        {
            if (nr == null) continue;
            nr.m_timeStarted = Time.time;
        }
    }

    
    public void SetupNewNightRewards()
    {
        m_nightTracker.Clear();
        m_rewardTextHolder.DestroyChildren();
        var info = MACalenderInfo.GetInfo(DayNight.Me.CurrentWorkingDay);
        var nightRewards = info.m_nightReward.Split('\n');
        foreach (var nr in nightRewards)
        {
            if (nr.IsNullOrWhiteSpace()) continue;
            MAParserSupport.TryParse(nr, out var result);
        }
        m_titleText.text = $"Day {DayNight.Me.CurrentWorkingDay}:Night Challenge";
    }
    public  void AddTrackedReward(string _type, string _challenge, float _data, string _reward)
    {
        _challenge = _challenge.Trim(' ', '"', '\u201C', '\u201D');
        _reward = _reward.Trim(' ', '"', '\u201C', '\u201D');
        var rewardSplit = _reward.Split(';', '|');
        if (Enum.TryParse(_type, out MANightChallengeManager.NightTracker.TrackerType trackerType) == false)
        {
            MAParser.ParserError($"no tracker type found for {_type} in {nameof(MANightChallengeManager)}");
        }
        var prefab = Instantiate(m_challengeTextPrefab, m_rewardTextHolder);
        prefab.gameObject.SetActive(true);

        m_nightTracker.Add(new MANightChallengeManager.NightTracker(trackerType, _challenge, _data,rewardSplit.ToList(), prefab.GetComponent<TMP_Text>()));
       
    }

    public void CharacterSpawned(MACharacterBase _character)
    {
        if (!nightChallengeInfo.HasStarted)
            return;

        if (_character.IsEscortedCreataure)
            ++nightChallengeInfo.spawnedEscortees;
    }

    public void CharacterDead(IDamageReceiver.DamageSource _source, MACharacterBase _character)
    {
        if (!nightChallengeInfo.HasStarted)
            return;

        if (_character is MAZombie)
            ++nightChallengeInfo.killedZombies;
        else if (_character is MAUndead)
            ++nightChallengeInfo.killedUndead;
        else if ((_character is MABanditMelee) || (_character is MABanditRanged))
            ++nightChallengeInfo.killedBandits;
        else if (_character is MAHeroBase)
            ++nightChallengeInfo.killedHeros;
        else if (_character is MAWorker)
            ++nightChallengeInfo.killedWorkers;
        // else if (_character is MAWorshippers)
        //     ++nightChallengeInfo.killedWorshippers;
        else if (_character.IsEscortedCreataure)
            ++nightChallengeInfo.killedEscortees;

        if ((_character is MAZombie) || (_character is MAUndead))
        {
            if (_source.HasFlag(IDamageReceiver.DamageSource.Hero))
                ++nightChallengeInfo.creaturesKilledByHero;
            else if (_source.HasFlag(IDamageReceiver.DamageSource.HandPower) || _source.HasFlag(IDamageReceiver.DamageSource.ThrownObject))
                ++nightChallengeInfo.creaturesKilledByHand;
        }
    }

    public void ResourceStolen(NGCarriableResource resource)
    {
        if (!nightChallengeInfo.HasStarted)
            return;

        if (resource.IsOre)
            ++nightChallengeInfo.stolenOre;
        else if (resource.IsWheat)
            ++nightChallengeInfo.stolenWheat;
        else if (resource.IsCotton)
            ++nightChallengeInfo.stolenCotton;
    }

    public void EscorteeSaved(MACharacterBase character)
    {
        if (!nightChallengeInfo.HasStarted)
            return;
        
        if (character.IsEscortedCreataure)
            ++nightChallengeInfo.savedEscortees;
    }

    public bool IsChallengeCompleted()
    {
        if (!nightChallengeInfo.HasStarted)
            return false;

        switch (nightChallengeInfo.type)
        {
            case MANightChallengeManager.NightTypes.TimedWaves:
            case MANightChallengeManager.NightTypes.BeaconAttack:
            case MANightChallengeManager.NightTypes.BeaconAttackCrypt:
            case MANightChallengeManager.NightTypes.ResourceAttack:
            case MANightChallengeManager.NightTypes.Endless:
                if (IsNightOver)
                    return true;
                break;
            case MANightChallengeManager.NightTypes.Escort:
                if (((nightChallengeInfo.spawnedEscortees > 0) && (nightChallengeInfo.savedEscortees + nightChallengeInfo.killedEscortees >= nightChallengeInfo.spawnedEscortees)) || IsNightOver)
                    return true;
                break;
            case MANightChallengeManager.NightTypes.Count:
                if (nightChallengeInfo.NightCreaturesKilled >= nightChallengeInfo.enemiesToKill)
                    return true;
                break;
        }

        return false;
    }

    public bool IsChallengeFailed()
    {
        if (!nightChallengeInfo.HasStarted)
            return false;
        
        switch (nightChallengeInfo.type)
        {
            case MANightChallengeManager.NightTypes.ResourceAttack:
                if (nightChallengeInfo.StolenResources >= nightChallengeInfo.resourcesToSave)
                    return true;
                break;
            case MANightChallengeManager.NightTypes.Escort:
                if ((nightChallengeInfo.spawnedEscortees > 0) && (nightChallengeInfo.killedEscortees >= nightChallengeInfo.escorteeDeadMax))
                    return true;
                break;
        }

        return false;
    }

    public void OnChallengeCompleted()
    {
        if (!nightChallengeInfo.HasStarted)
            return;

        nightChallengeInfo.isCompleted = true;
        nightChallengeInfo.isFailed = false;

        CalendarUIManager.Me.IsReadyForEndOfNight = DayNight.Me.m_isFullNight;
    }

    public void OnChallengeFailed()
    {
        if (!nightChallengeInfo.HasStarted)
            return;
        
        nightChallengeInfo.isFailed = true;
        nightChallengeInfo.isCompleted = false;
        ResetNightGUI();
    }

    public void OnChallengeOver()
    {
        if (!nightChallengeInfo.HasStarted)
            return;

        CalendarUIManager.Me.IsReadyForEndOfNight = false;
        if (nightChallengeInfo.isCompleted)
        {
            ResolveNighttimeRewards();
        }
        SetupNewNightRewards();
        nightChallengeInfo.Reset();
       // ToggleRewardTextAndTitle(false);
        //m_rewardTextHolder.gameObject.SetActive(false);
        
        foreach (var creature in NGManager.Me.m_MACreatureList)
        {
            if ((creature != null) && creature.IsEscortedCreataure
                && !creature.InState(CharacterStates.Despawn) && !creature.InState(CharacterStates.Dead))
                MACharacterStateFactory.ApplyCharacterState(CharacterStates.Despawn, creature);
        }
        ResetNightGUI();
    }
}

