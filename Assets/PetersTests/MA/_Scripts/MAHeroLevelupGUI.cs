using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class MAHeroLevelupGUI : MonoSingleton<MAHeroLevelupGUI>
{
    public TMP_Text m_heroTitle;
    public TMP_Text m_heroDescription;
    public Image m_heroIcon;
    public Transform m_levelupHolder;
    public TMP_Text m_levelupText;
    public Image m_experienceBar; 
    MAHeroBase m_hero;
    MACreatureInfo m_creatureInfo;
    List<MAHeroLevelInfo> m_levelInfos;
    public bool m_changedLevelup = false;
    public void DestroyMe()
    {
        Destroy(gameObject);
    }
    public void ClickedClose()
    {
        if (m_changedLevelup)
        {
            Utility.ShowDialog("Confirm Level Up Choice", "Are you sure this choice is correct? Once you click 'Yes', you cannot change your mind.", false, "Yes", "No", ClickedCloseCallback);
        }
        else
        {
            ClickedCloseCallback(0);
        }
    }
    
    public void ClickedCloseCallback(int _mode)
    {
        if(_mode == 0) // Yes
        {
            AudioClipManager.Me.PlaySound("PlaySound_Arcadium_ITEMINFO_CLOSE", GameManager.Me.gameObject);
            DestroyHeroLevelupIcon(m_hero);
            Destroy(gameObject);
        }
        else if (_mode == 1) // No
        {
        }
    }
    public void Activate(MAHeroBase _hero)
    {
        AudioClipManager.Me.PlaySound("PlaySound_Arcadium_ITEMINFO_OPEN", GameManager.Me.gameObject);
        m_hero = _hero;
        m_creatureInfo = _hero.CreatureInfo;
        if (m_hero.HeroGameState == null)
        {
            Debug.LogError("MAHeroLevelupGUI: GameState_Hero is null");
            return;
        }

        m_heroTitle.text = m_creatureInfo.m_displayName;
        m_heroDescription.text = m_creatureInfo.m_description;
        m_levelupText.text = m_hero.GetCharacterLevelName();
        m_experienceBar.fillAmount = m_hero.Experience/ m_hero.GetExperienceRequiredForNextLevel();
        string path = Utility.PathSimplify($"_Art/Sprites/BusinessRewards/{m_creatureInfo.m_spritePath}");
        var sprite = ResManager.Load<Sprite>(path);
        if (sprite != null)
            m_heroIcon.sprite = sprite;
        m_levelInfos = MAHeroLevelInfo.GetInfoList(m_creatureInfo.m_name);
        DisplayLevels();
    }

    void DisplayLevels()
    {
        var level = m_hero.HeroGameState.m_characterExperience.m_level;
        m_levelupHolder.DestroyChildren();
        for(int i = 0; i < m_levelInfos.Count; i++)
        {
            var levelInfo = m_levelInfos[i];
            MAHeroLevelupItemGUI.ItemType itemType;
            if (levelInfo.m_level < level)
                itemType = MAHeroLevelupItemGUI.ItemType.Past;
            else if (levelInfo.m_level == level)
                itemType = MAHeroLevelupItemGUI.ItemType.Current;
            else
                itemType = MAHeroLevelupItemGUI.ItemType.Future;
            MAHeroLevelupItemGUI.Create(m_hero, itemType, levelInfo, i, m_levelupHolder);
        }
    }

    public static void CreateHeroLevelupIcon(MAHeroBase _hero)
    {
        if (_hero.HeroGameState.m_characterExperience.CanLevelup())
        {
            AudioClipManager.Me.SetHumanoidType(_hero, _hero.HumanoidType, _hero.HeroGameState.m_characterExperience.m_level);
            var prefab = Resources.Load<Canvas>("_Prefabs/UI/HeroLevelUpIcon");
            var i = Instantiate(prefab, _hero.transform);
        }
    }
    public static void DestroyHeroLevelupIcon(MAHeroBase _hero)
    {
        if (_hero.HeroGameState.m_characterExperience.CanLevelup() == false)
        {
            var hlu = _hero.transform.Find("HeroLevelUpIcon(Clone)");
            if(hlu != null)
                Destroy(hlu.gameObject);    
        }
    }
    public static MAHeroLevelupGUI Create(MAHeroBase _hero)
    {
        var prefab = Resources.Load<MAHeroLevelupGUI>("_Prefabs/Dialogs/MAHeroLevelupGUI");
        var instance = Instantiate(prefab, NGManager.Me.m_centreScreenHolder);
        instance.Activate(_hero);
        return instance;
    }
}
