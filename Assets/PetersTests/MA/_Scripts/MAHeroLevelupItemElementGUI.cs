using System;
using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.Serialization;
using UnityEngine.UI;

public class MAHeroLevelupItemElementGUI : <PERSON>o<PERSON>ehaviour, IPointerEnterHandler, IPointerExitHandler
{
    [System.Serializable]
    public class LevelupIcons
    {
        public string m_name;
        public Sprite m_icon;
    }
    public List<LevelupIcons> m_icons;
    public Image m_icon;
    public Image m_iconSelected;
    public TMP_Text m_title;
    public GameObject m_helperHolder;
    public TMP_Text m_helperText;
    private MAHeroBase m_hero;
    private MAHeroLevelupItemGUI.ItemType m_type;
    private string m_reward;
    private string m_rewardDescription;
    private int m_levelupLevel;
    private MAHeroLevelupItemGUI m_calledFrom;
    private Coroutine showPromptCoroutine;

    public void ClickedMe()
    {
        AudioClipManager.Me.PlaySound("PlaySound_Arcadium_ITEMINFO_SELECT", GameManager.Me.gameObject);
        LevelupHero();
        MAHeroLevelupGUI.Me.m_changedLevelup = true;
        m_hero.HeroGameState.m_characterExperience.AssignReward(m_levelupLevel, m_reward);
        m_calledFrom.ResetAndSetOneShowing(this);
    }

    void LevelupHero()
    {
        var alreadySelected = m_calledFrom.GetSelectedElement(this);
        if (alreadySelected != null)
        {
            alreadySelected.m_iconSelected.enabled = false;
            BuffHero(alreadySelected.m_reward, false); // Must debuff alreadySelected.m_reward
        }
        BuffHero(m_reward, true);
        //Levelup the hero based on m_reward
    }

    void BuffHero(string _reward, bool _isBuff)
    {
        var rSplit = _reward.Split(':','=','+','*');
        if (float.TryParse(rSplit[1], out float value) == false)
        {
            Debug.LogError("Invalid reward format: " + _reward);
            return;
        }

        switch (rSplit[0].ToLower().Trim())
        {
            case "tag":
                if (_isBuff)
                {
                    m_hero.HeroGameState.m_maxTags+= value;
                    MAUnlocks.Me.m_possessedCanTagWorkers = true;
                    MAUnlocks.Me.m_possessedCanTagTourists = true;
                    MAUnlocks.Me.m_possessedCanTagHeroes = true;
                    MAUnlocks.Me.m_possessedCanTagAnimals = true;
                    MAUnlocks.Me.m_possessedCanTagQuests = true;
                }
                else
                    m_hero.HeroGameState.m_maxTags-= value;
                break;
            case "health":
                if (_isBuff)
                    m_hero.HeroGameState.m_baseHealth*= value;
                else
                    m_hero.HeroGameState.m_baseHealth/= value;
                break;
            case "experience":
                m_hero.HeroGameState.m_characterExperience.m_experienceMultiplier *= value;
                break;
            case "damage":
            case "strength":
                if (_isBuff)
                    m_hero.HeroGameState.m_baseAttack*= value;
                else
                    m_hero.HeroGameState.m_baseAttack/= value;
                break;
            case "speed":
                if (_isBuff)
                {
                    m_hero.HeroGameState.m_walkSpeed*= value;
                    m_hero.HeroGameState.m_attackSpeed*= value;
                }
                else
                {
                    m_hero.HeroGameState.m_walkSpeed/= value;
                    m_hero.HeroGameState.m_attackSpeed/= value;
                }
                break;
            case "defence":
                if (_isBuff)
                    m_hero.HeroGameState.m_baseDefence*= value;
                else
                    m_hero.HeroGameState.m_baseDefence/= value;
                break;
            case "sword":
            case "hammer":
            case "mace":
            case "axe":
                break;
            default:
                Debug.LogError($"Unknown reward type: {rSplit[0]}");
                break;
        }
    
    }
    /*void UpdateLevels()
    {
        if (m_hero.HeroGameState.m_characterExperience.m_rewardsGained.Count > m_levelupLevel)
        {
            m_hero.HeroGameState.m_characterExperience.m_rewardsGained[m_levelupLevel] = m_reward;
        } 
        else
        {
            m_hero.HeroGameState.m_characterExperience.m_rewardsGained.Add(m_reward);
        }
        
        m_calledFrom.ResetAndSetOneShowing(this);    
    }*/
    
    public void Activate(MAHeroBase _hero, MAHeroLevelupItemGUI.ItemType _type, string _reward, string _rewardDescription, int _levelupLevel, MAHeroLevelupItemGUI _calledFrom)
    {
        m_hero = _hero;
        m_type = _type;
        m_levelupLevel = _levelupLevel;
        m_reward = _reward;
        m_calledFrom = _calledFrom;
        m_rewardDescription = _rewardDescription;
        m_helperText.text = _rewardDescription;
        m_helperHolder.SetActive(false);
        var rSplit = _reward.Split(':','=','+','*');
        var icon = m_icons.Find(o=>o.m_name.ToLower() == rSplit[0].ToLower().Trim());
        m_icon.sprite = icon != null ? icon.m_icon : null;
        m_title.text = $"{_reward}";
        var cg = GetComponentInChildren<CanvasGroup>();
        
        ShowHighlighted(m_hero.HeroGameState.m_characterExperience.RewardMatches(m_levelupLevel, _reward));

        switch (m_type)
        {
            case MAHeroLevelupItemGUI.ItemType.Past:
            case MAHeroLevelupItemGUI.ItemType.Current:
                if (m_hero.HeroGameState.m_characterExperience.HasReward(m_levelupLevel) == false)
                {
                    cg.alpha = 1f;
                    cg.interactable = true;
                }
                else
                {
                    cg.alpha = .75f;
                    cg.interactable = false;
                }
                break;
            case MAHeroLevelupItemGUI.ItemType.Future:
                cg.alpha = .25f;
                cg.interactable = false;
                break;
        }
    }
    public void ShowHighlighted(bool _show)
    {
        m_iconSelected.enabled = _show;
    }

    public void OnPointerEnter(PointerEventData eventData)
    {
        if (m_rewardDescription.IsNullOrWhiteSpace())
            return;
        showPromptCoroutine = StartCoroutine(ShowPromptAfterDelay());
    }

    public void OnPointerExit(PointerEventData eventData)
    {
        if (showPromptCoroutine != null)
        {
            StopCoroutine(showPromptCoroutine);
            showPromptCoroutine = null;
        }
        m_helperHolder.SetActive(false);
    }

    IEnumerator ShowPromptAfterDelay()
    {
        yield return new WaitForSeconds(2f);
        m_helperHolder.SetActive(true);
    }
    public static MAHeroLevelupItemElementGUI Create(MAHeroBase _hero, MAHeroLevelupItemGUI.ItemType _type, string _reward, string _rewardDescription, int _levelupLevel, Transform _holder, MAHeroLevelupItemGUI _calledFrom)
    {
        var prefab = Resources.Load<MAHeroLevelupItemElementGUI>("_Prefabs/Dialogs/MAHeroLevelupItemElementGUI");
        var instance = Instantiate(prefab, _holder);
        instance.Activate(_hero, _type, _reward, _rewardDescription, _levelupLevel, _calledFrom);
        return instance;
    }
}
