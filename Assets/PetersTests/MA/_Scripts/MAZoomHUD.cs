using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class MAZoomHUD : MonoBehaviour
{
    public GameObject m_dotPrefab;
    public GameObject m_LinePrefab;
    public GameObject m_MapUI;
    public RectTransform m_detailsHolder;
    public bool IsShowing = false;
    public float m_showForTime = 2f;
    private float m_showForTimer = 0f;
    public float m_currentChange = 0f;
    public int m_currentStep = -1;
    public bool m_createdVisuals = false;
    public int m_shownStep = -1;
    public List<MAZoomHUDLine> m_HUDLines = new ();
    public void Update()
    {
        if (GameManager.Me.LoadComplete == false || MACameraControl.s_all.Count == 0)
        {
            m_detailsHolder.gameObject.SetActive(false);
            return;
        }

        if (m_createdVisuals == false)
        {
            CreateVisuals();
        }
        m_currentChange = GameManager.Me.CurrentZoomChange;
        m_currentStep = (int)GameManager.Me.m_state.m_cameraControlStage;
        if (GameManager.Me.CurrentZoomChange != 0)
        {
            if(IsShowing == false)
            {
                IsShowing = true;
            }
            m_detailsHolder.gameObject.SetActive(true);
            m_showForTimer = m_showForTime + Time.time;
        }
        if (IsShowing == false) return;
        
        if (Time.time > m_showForTimer || m_MapUI.activeSelf)
        {
            IsShowing = false;
            m_detailsHolder.gameObject.SetActive(false);
            return;
        }
        if (m_shownStep != m_currentStep)
        {
            if(m_shownStep != -1)
                m_HUDLines[m_shownStep].HighLight(false);
            m_shownStep = (int)m_currentStep;
            m_HUDLines[m_shownStep].HighLight(true);
        }
    }
    public void CreateVisuals()
    {
        m_detailsHolder.transform.DestroyChildren();
        m_createdVisuals = true;
        m_HUDLines.Clear();
        for (var i = 0; i < MACameraControl.s_all.Count; i++)
        {
            var cc = MACameraControl.s_all[i];
            var line = Instantiate(m_LinePrefab, m_detailsHolder);
            var zLine = line.GetComponent<MAZoomHUDLine>();
            zLine.Activate(cc.m_displayName);
            m_HUDLines.Add(zLine);
            if (i < MACameraControl.s_all.Count - 1)
            {
                for (int j = 0; j < cc.m_steps; j++)
                {
                    var dot = Instantiate(m_dotPrefab, m_detailsHolder);
                }
            }
        }
    }
}

//GameManager.Me.CurrentZoomChange