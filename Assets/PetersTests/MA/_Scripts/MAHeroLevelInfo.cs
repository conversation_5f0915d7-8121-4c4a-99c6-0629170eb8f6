using System;
using System.Collections.Generic;
using UnityEngine;
[Serializable]
public class MAHeroLevelInfo
{
    public static List<MAHeroLevelInfo> s_heroLevelInfoList = new();
    public static Dictionary<string,List<MAHeroLevelInfo>> s_heroLevelMap = new();
    public static List<MAHeroLevelInfo> GetList=>s_heroLevelInfoList;
    public static Dictionary<string,List<MAHeroLevelInfo>> LevelMap=>s_heroLevelMap;
    public string DebugDisplayName => $"{m_heroName}:{m_level}";
    public bool m_debugChanged;
    public string id;

    public string m_indexer;
    public string m_heroName;
    public string m_index;
    public int m_level;
    public string m_levelName;
    public int m_experienceRequired;
    public string m_reward1;
    public string m_reward2;
    public string m_reward3;
    public string m_levelDescription;
    public string m_reward1Description;
    public string m_reward2Description;
    public string m_reward3Description;
    public static MAHeroLevelInfo GetInfo(string _name) => s_heroLevelInfoList.Find(o=> o.m_indexer == _name);
    public static List<MAHeroLevelInfo> GetInfoList(string _name)
    {
        if(s_heroLevelMap.TryGetValue(_name, out List<MAHeroLevelInfo> infoList))
            return infoList;
        return new List<MAHeroLevelInfo>();
    }

    public static bool PostImport(MAHeroLevelInfo _what)
    {
        s_heroLevelMap.TryGetValue(_what.m_heroName, out var infoList);
        if(infoList == null)
        {
            infoList = new();
            s_heroLevelMap.Add(_what.m_heroName, infoList);
        }
        
        infoList.Add(_what);
        
        return true;
    }

    
    public static List<MAHeroLevelInfo> LoadInfo()
    { 
        s_heroLevelMap.Clear();
        s_heroLevelInfoList = NGKnack.ImportKnackInto<MAHeroLevelInfo>(PostImport);
        return s_heroLevelInfoList;
    }
}
