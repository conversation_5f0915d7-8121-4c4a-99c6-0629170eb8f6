using System.Collections;
using System.Collections.Generic;
using System.Net;
using LMiniJSON;
using LMNT;
using UnityEngine;
using UnityEngine.Networking;

public class DebugVoice : MonoBehaviour
{
    public string m_characterName;
    public bool m_talkNow;
    public string m_text;
    private float m_lastVolume = -1;
    private bool m_voiceActive = false; public bool VoiceActive => m_voiceActive;
    
    private AudioSource m_audioSource;
    private string m_apiKey;
    private System.Collections.Generic.List<Voice> m_voiceList;
    private DownloadHandlerAudioClip m_handler;
    private string m_voice;

    public AudioSource GetAudioSource() { return m_audioSource; }
    
    private string LookupByName(string name)
    {
        const string c_fallbackVoiceID = "sophie";
        if (m_voiceList == null || m_voiceList.Count == 0) return c_fallbackVoiceID;
        var voice = m_voiceList.Find(v => v.name == name);
        if (voice == null)
        {
            Debug.LogError($"Couldn't find voice {name} in list of {m_voiceList.Count} voices");
            return c_fallbackVoiceID;
        }
        return voice.id;
    }
    
    void Start()
    {
        var lmnt = GetComponent<LMNTSpeech>();
        m_voice = lmnt.voice;
        m_audioSource = gameObject.GetComponent<AudioSource>();
        if (m_audioSource == null) m_audioSource = gameObject.AddComponent<AudioSource>();
        m_apiKey = LMNTLoader.LoadApiKey();
        m_voiceList = LMNTLoader.LoadVoices();
        if (m_voiceList == null || m_voiceList.Count == 0) Debug.LogError($"Error reading list of LMNT voices");
    }

    void Update()
    {
        if (m_talkNow)
        {
            m_talkNow = false;
            StartCoroutine(GetAndPlaySpeech());
        }
        var volume = AudioClipManager.Me.GetOverallVOVolume();
        if (volume.Nearly(m_lastVolume) == false)
        {
            m_lastVolume = volume;
            m_audioSource.volume = volume;
        }
    }
    public void SetSpeech(string _text)
    {
        m_text = _text.Replace(@"\n", " ");
        m_talkNow = true;
    }

    private static string s_accountInfo = null;
    
    private IEnumerator GetAndPlaySpeech()
    {
        m_voiceActive = true;
        
        var cacheName = $"{m_characterName}_{m_text}".SanitiseFilename();
        if (cacheName.Length > 32) cacheName = cacheName[..32];
        cacheName = $"DebugAudioCache/{cacheName}_{m_text.GetHashCode():x}";
        var clip = Resources.Load<AudioClip>(cacheName);
        if (clip != null)
        {
            m_audioSource.clip = clip;
            m_audioSource.Play();
        }
        else
        {
            string voiceParam = UnityWebRequest.EscapeURL(LookupByName(m_voice));
            string textParam = UnityWebRequest.EscapeURL(m_text);
            string url = $"{Constants.LMNT_SYNTHESIZE_URL}?voice={voiceParam}&text={textParam}&format=wav";

            using (var request = UnityWebRequest.Get(url))
            {
                m_handler = new DownloadHandlerAudioClip(url, AudioType.WAV);
                request.SetRequestHeader("X-API-Key", m_apiKey);
                // TODO: do not hard-code; find a clean way to get package version at runtime
                request.SetRequestHeader("X-Client", "unity/0.1.0");
                request.downloadHandler = m_handler;

                yield return request.SendWebRequest();
                if (request.responseCode > 299)
                {
                    m_audioSource.clip = null;
                    if (s_accountInfo == null)
                    {
                        var req = UnityWebRequest.Get("https://api.lmnt.com/v1/account");
                        req.SetRequestHeader("X-API-Key", m_apiKey);
                        req.SetRequestHeader("X-Client", "unity/0.1.0");
                        yield return req.SendWebRequest();
                        var result = req.downloadHandler.text;
                        var info = LMiniJson.JsonDecode(result) as Dictionary<string, object>;
                        var usageInfo = info["usage"] as Dictionary<string, object>;
                        var planInfo = info["plan"] as Dictionary<string, object>;
                        var periodEnd = (long) usageInfo["period_end"];
                        var epochTicks = new System.DateTime(1970, 1, 1, 0, 0, 0, 0, System.DateTimeKind.Utc).Ticks;
                        var periodEndTicks = periodEnd * ******** + epochTicks;
                        var periodEndDate = System.DateTime.FromBinary(periodEndTicks);
                        s_accountInfo = $"characters used {usageInfo["characters"]} / {planInfo["character_limit"]} - resets at {periodEndDate}\n{result}";
                    }
                    if (request.responseCode == 429)
                        Debug.LogError($"LMNT Error [{cacheName}]: too many LMNT requests genering - {s_accountInfo}");
                    else
                        Debug.LogError($"LMNT Error [{cacheName}]: {request.error}\n\n{s_accountInfo}");
                }
                else
                {
                    m_audioSource.clip = m_handler.audioClip;
                    m_audioSource.Play();
#if UNITY_EDITOR
                    //System.IO.File.WriteAllBytes($"Assets/Resources/{cacheName}.wav", m_handler.data);
                    AudioSaver.SaveWav($"Assets/Resources/{cacheName}.wav", m_audioSource.clip);
                    //UnityEditor.AssetDatabase.Refresh();
                    Debug.LogError($"Audio clip {cacheName} generated, this should be pushed to Git as soon as possible.");
#endif
                }
            }
            if (m_audioSource.clip == null)
            {
                var timeEstimate = MAMessage.GetTimeToRead(m_text);
                yield return WaitWithSkip(timeEstimate);
                m_voiceActive = false;
                yield break;
            }
        }
        bool abandon = false;
        while (m_audioSource.isPlaying)
        {
            if (Input.GetKeyDown(KeyCode.Tab))
            {
                m_audioSource.Stop();
                abandon = true;
                break;
            }
            yield return null;
        }
        if (abandon == false)
            yield return WaitWithSkip(.5f);
        m_voiceActive = false;
    }

    IEnumerator WaitWithSkip(float _time)
    {
        for (float t = 0; t < _time; t += Time.deltaTime)
        {
            if (Input.GetKeyDown(KeyCode.Tab)) break;
            yield return null;
        }
    }
}
