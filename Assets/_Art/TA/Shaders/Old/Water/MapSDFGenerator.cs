using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using UnityEngine;

public class MapSDFGenerator : MonoBehaviour
{
    [SerializeField] private float seaLevel;
    [SerializeField] private Vector2Int textureResolution;

    private Texture2D sdf;

    private void Start()
    {
        sdf = CreateTexture();
        StartCoroutine(GenerateSDF());
    }

    Texture2D CreateTexture()
    {
        Texture2D sdf = new Texture2D(textureResolution.x, textureResolution.y);
        sdf.filterMode = FilterMode.Bilinear;
        return sdf;
    }

    public LayerMask mask;

    IEnumerator GenerateSDF()
    {
        int c = 0;

        for (int i = 0; i < textureResolution.x; i++)
        {
            for (int j = 0; j < textureResolution.x; j++)
            {
                float dist = float.MaxValue;
                Vector3 position = new Vector3(i*.5f - 512, seaLevel, j*.5f - 512);
                Vector3 lastDir = Vector3.zero;

                RaycastHit hit;
                if (Physics.Raycast(new Vector3(position.x, 200, position.z), Vector3.down, out hit,
                        Mathf.Infinity, mask.value))
                {
                    if (hit.point.y < seaLevel)
                    {
                        for (int k = 0; k < 45; k++)
                        {
                            Vector3 dir = Quaternion.Euler(0, k * 8, 0) * Vector3.right;

                            if (Physics.Raycast(position, dir, out hit,
                                    Mathf.Infinity, mask.value))
                            {
                                if (hit.distance < dist)
                                {
                                    dist = hit.distance;
                                    lastDir = dir;
                                }
                            }
                        }

                        float d = dist / 100;
                        sdf.SetPixel(i, j, new Color(d, d, d));
                            Debug.DrawLine(position, position + lastDir * dist, new Color(d, d, d));
                    }
                    else
                    {
                        sdf.SetPixel(i, j, Color.black);
                    }
                }
            }

            Debug.Log($"SDF Generation + {i /1024.0f *100}%");
            yield return null;
        }

        byte[] bytes = sdf.EncodeToPNG();
        var dirPath = Application.dataPath + "/../MapSDF/";
        if (!Directory.Exists(dirPath))
        {
            Directory.CreateDirectory(dirPath);
        }

        File.WriteAllBytes(dirPath + "SeaLevelSDF" + ".png", bytes);
        Debug.Log("Map sdf computed successfully.");
        Debug.Log($"{dirPath} \\SeaLevelSDF.png");
    }

    void Update()
    {
    }
}