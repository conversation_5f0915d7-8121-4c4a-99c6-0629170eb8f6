using UnityEngine;

public abstract class HUDElement<T> : MonoMe<T>, IHUDElement where T : HUDElement<T>
{
	[Header("HUDElement")]
    [SerializeField] GameObject m_closed;
    [SerializeField] GameObject m_open;
    [SerializeField] protected Animator m_tabAnimator;
	[SerializeField] protected string m_openSound;
	[SerializeField] protected string m_closeSound;

	[SerializeField] protected Animator m_closedAnimator;

	[SerializeField] GameObject m_visualsGO;

	public abstract HUDVisibilityControl.HUDElementType Type { get; }
	public virtual bool IsVisible
	{
		get { 
			if (m_visualsGO != null)
			{
				return m_visualsGO.activeInHierarchy;
			}
			return gameObject.activeInHierarchy;
		}
	}
	public bool IsExpanded => m_isTabOpen;

    protected bool m_isTabOpen;

	public void ToggleOpenClose()
    {
        if (m_isTabOpen)
            CloseTab();
        else
            OpenTab();
    }

	public virtual void SetVisibility(bool _visible)
	{
		if (_visible == IsVisible)
			return;

		if (_visible)
			Show();
		else
			Hide();
	}

    public virtual void OpenTab()
    {
		HUDVisibilityControl.OnTabVisibilityChange(Type, true);
//		if(!m_isTabOpen)
//			AudioClipManager.Me.PlaySound(m_openSound, transform);

        m_isTabOpen = true;
		if (m_tabAnimator != null)
			m_tabAnimator.SetBool("Open", true);

        OnBeginTabOpen();
    }

    public virtual void CloseTab()
    {
		HUDVisibilityControl.OnTabVisibilityChange(Type, false);
//		if (m_isTabOpen)
//			AudioClipManager.Me.PlaySound(m_closeSound, transform);

		m_isTabOpen = false;
		if(m_tabAnimator != null)
			m_tabAnimator.SetBool("Open", false);

		OnTabClosed();
    }

	public virtual void Show()
	{
		if (m_visualsGO != null)
		{
			m_visualsGO.SetActive(true);
		}
		else
		{
			gameObject.SetActive(true);
		}
	}

	public virtual void Hide()
	{
		if (m_visualsGO != null)
		{
			m_visualsGO.SetActive(false);
		}
		else
		{
			gameObject.SetActive(false);
		}
	}

    public virtual void OnTabOpened() {}

    public virtual void OnTabClosed()
	{
	}

    protected virtual void OnBeginTabOpen()
	{
	}

    protected override void _OnEnable()
    {
        if (m_isTabOpen)
        {
            CloseTab();
        }
    }

	public Transform ClosedTransform()
	{
		return m_closed.transform;
	}

	public virtual void BubbleArrived(ReactItemInfo.ItemInfoBase _item)
	{
	}
	public void SetBubblesInFlight(bool _inFlight)
	{
		m_closedAnimator.SetBool("Collect", _inFlight);
	}
}

public interface IHUDElement
{
	HUDVisibilityControl.HUDElementType Type { get; }
	bool IsVisible { get; }
	bool IsExpanded { get; }
	void SetVisibility(bool _visible);
	void OpenTab();
    void CloseTab();
	void Show();
	void Hide();
	void BubbleArrived(ReactItemInfo.ItemInfoBase _item);
	Transform ClosedTransform();
	void SetBubblesInFlight(bool _inFlight);
}