using System.Collections.Generic;
using UnityEngine;

public class PredefinedList_v0 : UIComponentWithId {

	[SerializeField]
	private Cell_v0[] predefinedCells;

	private Dictionary<int, Cell_v0> cellById = new Dictionary<int, Cell_v0>();

	public IEnumerable<Cell_v0> cells => cellById.Values;

	protected override void OnInitialize() {
		base.OnInitialize();

		foreach(Cell_v0 cell in predefinedCells) {
			cell.gameObject.SetActive(true);
			cell.OnCloneComplete();
			cellById.Add(cell.GetInstanceID(), cell);
		}


	}

}
