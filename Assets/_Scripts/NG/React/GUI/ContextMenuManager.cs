using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class ContextMenuManager : Mono<PERSON>ingleton<ContextMenuManager>
{
	[SerializeField] ContextMenu m_contextMenuPrefab;

	[SerializeField] float m_minDistFromCamera = 25.0f;
	[Range(0, 1)]
	[SerializeField] float m_leftScreenBound = 0.25f;
	[Range(0, 1)]
	[SerializeField] float m_rightScreenBound = 0.72f;
	[Range(0, 1)]
	[SerializeField] float m_topScreenBound = 0.6f;
	[Range(0, 1)]
	[SerializeField] float m_bottomScreenBound = 0.2f;

	[SerializeField] float m_adjustCameraDistance = 40f;

	ContextMenu m_currentContextMenu;
	Coroutine m_zoomCoroutine;

	public bool ContextMenuOpen { get { return m_currentContextMenu != null; } }
	public ContextMenu CurrentContextMenu { get { return m_currentContextMenu; } }
	public object CurrentOwnerObject { get { return m_currentContextMenu == null ? null : m_currentContextMenu.m_owner; } }
	public static event System.Action<object> OnContextMenuShown;
	public static event System.Action OnContextMenuHidden;
	public static event System.Action OnContextMenuOffClick;
	private bool m_blockFactoryContextMenu;
	private bool m_blockAllContextMenu;

	public bool LockFactoryContextMenu { get { return m_blockFactoryContextMenu; } set { m_blockFactoryContextMenu = value;} }
	public bool LockAllContextMenus { get { return m_blockAllContextMenu; } set { m_blockAllContextMenu = value;} }
	public bool LockBuildingSiteContextMenu {get; set;}
	// Vars for testing
	public bool m_adjustCamera = false; 

	//---------------------------------------------------------------------------------------------------------
	public void ShowContextMenu(ContextMenuData _data, Vector3 _position, object _owner)
	{
		if (GameManager.TownStandardInteractionsActive == false) return;
		if(m_blockAllContextMenu)//TODO - these conditionals should be done in individual data providers || (_owner is ReactFactory && LockFactoryContextMenu) || (_owner is ReactBuildingSite && LockBuildingSiteContextMenu))
			return;

		if (m_zoomCoroutine != null)
			StopCoroutine(m_zoomCoroutine);

		if (m_currentContextMenu != null)
			RemoveCurrentMenu();

		if(!_data.IsValid())
		{
			Debug.LogError("Invalid Context Menu data - there should be a title and 1-6 options");
			return;
		}

		if (m_adjustCamera)
			GameManager.Me.ForceCameraStageAtLeast(1);

		m_currentContextMenu = Instantiate(m_contextMenuPrefab, GameManager.Me.m_mainGameUI.transform, false);
		m_currentContextMenu.Initialise(_data, _owner);

		if (GameManager.Me.IsOKToPlayUISound())
			AudioClipManager.Me.PlaySound("PlaySound_BuildingContextMenu_Open", GameManager.Me.transform);

		OnContextMenuShown?.Invoke(_owner);
	}

	public void ReplaceContextMenu(ContextMenuData _data, Vector3 _position, object _owner)
	{
		// Show a new context menu in the same place as the old one
		Vector3 currPosition = m_currentContextMenu.transform.position;
		RemoveCurrentMenu();
		ShowContextMenu(_data, _position, _owner);
		m_currentContextMenu.transform.position = currPosition;
	}

	//---------------------------------------------------------------------------------------------------------
	public void UpdateMenuWithDrag(float _distance)
	{
		if(m_currentContextMenu != null)
		{
			m_currentContextMenu.HandleFlickGesture(_distance);
		}
	}

	//---------------------------------------------------------------------------------------------------------

	public void RemoveCurrentMenu(bool _offClick = false)
	{
		if (m_currentContextMenu == null)
			return;

		Destroy(m_currentContextMenu.gameObject);
		m_currentContextMenu = null;
		if(_offClick) {
			OnContextMenuOffClick?.Invoke();
		} else {
			OnContextMenuHidden?.Invoke ();
		}

		if (_offClick && GameManager.Me.IsOKToPlayUISound())
			AudioClipManager.Me.PlaySoundOld("PlaySound_RightClick_Off_Building", GameManager.Me.transform);

		if (NGBusinessDecisionManager.Me)
			NGBusinessDecisionManager.Me.AnOkayClicked(m_currentContextMenu);
	}

	//---------------------------------------------------------------------------------------------------------
	
	public void CreateTestMenu(int _numOptions)
	{
		if (m_currentContextMenu != null)
			RemoveCurrentMenu();

		if (_numOptions < 1 || _numOptions > 5)
		{
			Debug.LogError("Context menu currently only handles 1 - 5 options");
			return;
		}

		ContextMenuData data = new ContextMenuData();
		data.m_title = "Building";
		data.m_buttonDataList = new List<ContextMenuData.ButtonData>();
		ContextMenuData.ButtonData buttonData;

		for(int i = 0; i < _numOptions; i++)
		{
			buttonData = new ContextMenuData.ButtonData();
			buttonData.m_label = "Option " + (i +1);
			data.m_buttonDataList.Add(buttonData);
			buttonData.m_buttonType = ContextMenuData.ButtonType.BUILDING_FUNCTIONALITY;
		}

		m_currentContextMenu = Instantiate(m_contextMenuPrefab, GameManager.Me.m_mainGameUI.transform, false);
		m_currentContextMenu.Initialise(data, null);
		m_currentContextMenu.transform.position = new Vector3(500, 500, 0);
	}

	//---------------------------------------------------------------------------------------------------------
	IEnumerator WaitForCamera()
	{
		//Wait for the camera to zoom out & pan
		yield return new WaitForSecondsRealtime(0.5f);

		//TODO CameraMovement.Me.FinishZoomMaintainPosition();
		m_zoomCoroutine = null;
	}

	//---------------------------------------------------------------------------------------------------------
	float CameraDistanceToBuilding(Vector3 _target)
	{
		return Vector3.Dot(_target - Camera.main.transform.position, Camera.main.transform.forward);
	}

	//---------------------------------------------------------------------------------------------------------
	bool IsWithinBounds(Vector3 _worldPos)
	{
		Vector2 viewportPoint = Camera.main.WorldToViewportPoint(_worldPos);

		if (viewportPoint.x > m_rightScreenBound || viewportPoint.x < m_leftScreenBound)
			return false;

		if (viewportPoint.y > m_topScreenBound || viewportPoint.y < m_bottomScreenBound)
			return false;

		return true;
	}

	//---------------------------------------------------------------------------------------------------------
	public Vector3 GetContextMenuButtonPosition(ContextMenuData.ButtonType _buttonTypeToGetPosition)
	{
		Vector3 buttonPosition = Vector3.zero;
		for(int i = 0; i< m_currentContextMenu.Buttons.Count; i++)
		{
			if(m_currentContextMenu.Buttons[i].ButtonType == _buttonTypeToGetPosition)
			{
				buttonPosition = m_currentContextMenu.Buttons[i].transform.position;
				return buttonPosition;
			}
		}
		return buttonPosition;
	}

	public void GetContextMenuButton(ContextMenuData.ButtonType _buttonType, System.Action _myCallback){
		Debug.Assert(_myCallback != null, "ERROR: - My callback has not been assert");
		StartCoroutine(ContextButtonReady(_buttonType, _myCallback));
	}
	private IEnumerator ContextButtonReady(ContextMenuData.ButtonType _buttonType, System.Action _myCallback){
		WaitForSeconds waitTime = new WaitForSeconds(.1f);
		bool isChecking = true;
		while(isChecking){
			yield return waitTime;
			if(m_currentContextMenu == null || m_currentContextMenu.Buttons == null)
				continue;

			for(int i = 0; i< m_currentContextMenu.Buttons.Count; i++)
			{
				if(m_currentContextMenu.Buttons[i].ButtonType != _buttonType)
					continue;

				_myCallback();
				isChecking = false;
			}
		}
	}
}
