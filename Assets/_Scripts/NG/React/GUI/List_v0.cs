using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class List_v0 : UIComponentWithId {

	[SerializeField]
	private Cell_v0 cloneCell;

	public UnityEngine.UI.Button cloneButton; 
	
	private Dictionary<int, Cell_v0> clonedCellById = new Dictionary<int, Cell_v0>();

	private int spawnIndex = 0;

	protected override void OnInitialize() {
		base.OnInitialize();
		if(cloneCell == null) {
			//Debug.LogError("Clone cell is null on element: " + name);
			spawnIndex = cloneButton.transform.GetSiblingIndex() + 1;
			cloneButton.gameObject.SetActive(false);
			return;
		}

		spawnIndex = cloneCell.transform.GetSiblingIndex() + 1;
		cloneCell.gameObject.SetActive(false);
	}

	public UnityEngine.UI.Button CloneButton()
	{
		var newButton = Instantiate<UnityEngine.UI.Button>(cloneButton, transform, false);
		newButton.transform.SetSiblingIndex(spawnIndex++);
		newButton.gameObject.SetActive(true);
		return newButton;
	}
	
	public Cell_v0 CloneCell() {
		Cell_v0 newCell = GameObject.Instantiate<Cell_v0>(cloneCell, transform, false);
		newCell.transform.SetSiblingIndex(spawnIndex++);

		newCell.gameObject.SetActive(true);

		newCell.OnCloneComplete();

		clonedCellById.Add(newCell.GetInstanceID(), newCell);
		


		return newCell;
	}


	public void DestroyCell(Cell_v0 cell) {
		int instanceId = cell.GetInstanceID();
		bool wasRemoved = clonedCellById.Remove(instanceId);
		if(wasRemoved) {
			GameObject.Destroy(cell.gameObject);
		} else {
			throw new ArgumentException("Cell was not created through this list. InstanceId: " + instanceId + " Name: " + cell.name);
		}
	}

	public void ClearCells() {
		foreach(var cell in clonedCellById) {
			GameObject.Destroy(cell.Value.gameObject);
		}
		clonedCellById.Clear();
	}

}
