using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.EventSystems;
[Serializable]
public class InfographicItem {
	public string m_title;
	public GameObject m_content;
	public Animator m_anim;
}
public class InfographicUIController : BaseClosableUIController {

	/*
		Example To call:
		InfographicUIController infoController = InfoPlaqueManager.Me.LoadUI<InfographicUIController>();
		infoController.Setup(InfographicUIController.InfographicType.COMPLETING_ORDERS);
		infoController.m_okButton.onButtonClick += (data) => {
			infoController.Close();
		};
		infoController.Show();
	*/
	public enum InfographicType {
		COMPLETING_ORDERS = 0,
		EMPLOYING_WORKERS,
		MAKING_PRODUCTS,
		STARTING_CONSTRUCTION,
		REWARDS,
		BUYING_PRODUCTS,
		TOWN_HAPPINESS,
		GARAGE,
		WORKING_HOURS,
		TOWN_OVERVIEW,
		GETTING_RESOURCES,
		PICKING_UP_PEOPLE,
		RESOURCES,
		MAKING_MULTIPLE_PRODUCTS,
		LONG_TERM_PLAN,
		LENGTH
	}
	public UITextPro m_titleText;
	[SerializeField]private Animator m_popupAnimator;
	public UIButton m_okButton;
	[SerializeField]private InfographicItem[] m_items;
	private InfographicItem m_infoGraphic = null;
	public Animator LoopingAnim => m_infoGraphic.m_anim;
	private Coroutine m_routine = null;
	public static event Action OnInfographicClosed; 
	public void Setup(InfographicType _infoType){
		if(m_items.Length > (int)InfographicType.LENGTH){
			Debug.LogError("ISSUE:- The InfographicType enum is shorter than the number of infographics, please update the InfographicType enum.");
			return;
		}
		m_infoGraphic = GetInfoGraphic(_infoType);
		if(m_infoGraphic == null || m_infoGraphic.m_content == null || m_infoGraphic.m_anim == null){
			Debug.LogError("ISSUE:- You are missing an infographic content, please check and update the items array.");
			return;
		}
		if(string.IsNullOrEmpty(m_infoGraphic.m_title)){
			Debug.LogError("ISSUE:- You are missing an infographic title string, please check and update the items array.");
			return;
		}

		m_titleText.text = m_infoGraphic.m_title;

		if(m_routine != null)
		{
			StopCoroutine(m_routine);
		}
		m_okButton.interactable = false;
	}

	protected override void Start() {
		base.Start();
		m_routine = StartCoroutine(ContentTrigger());
	}

	protected override void Close_Internal(bool _playCloseSound = true)
	{
		OnInfographicClosed?.Invoke();
		base.Close_Internal(_playCloseSound);
	}

	private InfographicItem GetInfoGraphic(InfographicType _infoType){
		return m_items[(int)_infoType];
	}

	private IEnumerator ContentTrigger(){
		while(m_popupAnimator.GetCurrentAnimatorStateInfo(0).normalizedTime < 1.0f){
			yield return null;
		}
		m_routine = null;
		m_infoGraphic.m_content.SetActive(true);// should only happen once popupanimator has finished
		m_infoGraphic.m_anim.SetTrigger("infographic_start");
		Utility.DoNextFrame(StartButtonCoRoutine);
	}
	private void StartButtonCoRoutine(){
		m_routine = StartCoroutine(ButtonActivateDelay());
	}
	private IEnumerator ButtonActivateDelay(){
		yield return new WaitForSecondsRealtime(1f);
		/*while(!m_infoGraphic.m_anim.GetCurrentAnimatorStateInfo(0).loop)
			yield return null;
		while(m_infoGraphic.m_anim.GetCurrentAnimatorStateInfo(0).normalizedTime < 1.0f){
			yield return null;
		}*/
		m_routine = null;
		m_okButton.interactable = true;
	}
}
