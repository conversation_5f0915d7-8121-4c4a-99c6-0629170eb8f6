using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class ReactPickupProduct : ReactPickupPersistent
{
	public static ReactPickupProduct Create(Transform _holder, Texture2D _productDesign, int _materialIndex)
	{
		var go = Instantiate(NGManager.Me.m_productPrefab, _holder);
		go.transform.localPosition = Vector3.zero;
		var rp = go.GetComponent<ReactPickupProduct>();
		if(_productDesign && rp.m_productPanel)
		{
			//TODO rp.m_productPanel.material.SetMainTex(_productDesign);
			//rp.m_productPanel.material.SetupMaterialBlendMode(MatExtensions.Blending.Transparent);
		}
		rp.SetBodyColor(_materialIndex);
		Vector3 force = new Vector3(Random.Range(.1f, .4f), Random.Range(.1f, .2f), Random.Range(.1f, .2f));
		force.Normalize();
		var rb = go.GetComponent<Rigidbody>();
		rb.AddForce(force, ForceMode.Force);
		return rp;
	}
}