using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.EventSystems;

// A ReactPickupPersistent can be interacted with by the player and can exist on its own in the world 
// when not being carried. This is in contrast to other types of ReactPickup (e.g. water buckets) which disappear 
// when released by the character carrying them.

public class ReactPickupPersistent : ReactPickup, IPickupBehaviour {
	public	float	m_pickupThrowThreshold = .5f;
	GameState_Pickup m_pickupData = null;
	public GameState_Pickup PickupData { get { return m_pickupData; } }
	private bool m_isBeingDragged;
	private bool m_isInTransit;
	private bool m_isOnTrain;
	private MACharacterBase m_beingCollectedBy;
	public override bool IsBeingDragged { get { return m_isBeingDragged; } set { m_isBeingDragged = value; } }
	public bool Held => m_isBeingDragged;
	public Vector3 m_targetPosForSave;

	public bool IsReservedBy(MACharacterBase character)
	{
		return character == m_beingCollectedBy;
	}
	
	public bool TryReserve(MACharacterBase _worker)
	{
		bool canReserve = false;
		
		if(m_beingCollectedBy == null)
			canReserve = true;
		else if(m_beingCollectedBy.PeepAction != PeepActions.CollectPickup)
		{
			if ((m_beingCollectedBy.Carrying != this) && !m_beingCollectedBy.InState(CharacterStates.CreatureHarvesting))
				canReserve = true;
		}
		
		if(canReserve) m_beingCollectedBy = _worker;
		return canReserve;
	}
	
	void Register() {
		if (m_pickupData != null) return;
		if (m_isOnTrain) return;
		m_pickupData = new GameState_Pickup();
		var product = (this as NGReactPickupAny)?.m_product;
		if (product != null) {
			m_pickupData.m_data = GameManager.Me.m_state.m_products.IndexOf(product);
		}
		m_pickupData.m_type = Contents.Name;
		m_pickupData.Pickup = this;
		m_pickupData.m_spawnedFrom = m_spawnedFrom?.m_linkUID ?? -1;
		m_pickupData.m_quantity = (int)m_quantity;
		if(GameManager.Me)
			GameManager.Me.m_state.m_pickups.Add(m_pickupData);
	}
	void Deregister() {
		if (GameManager.Me != null) // shutting down
			GameManager.Me.m_state.m_pickups.Remove(m_pickupData);
    }
	void Start() {
		Register();
		RecalculateScale();
	}
	virtual public bool DropNoBezier()
	{
		return false;
	}

	public bool AllowPutDown => true;
	public float PutDownReleaseHeight => .5f;

	protected virtual void OnUpdate()
    {
        
    }

	override public void DestroyMe()
	{
		Destroy(gameObject);
	}



    
	protected override void Update()
	{
		if (GameManager.Me == null) return;
		OnUpdate();
		base.Update();
		
		#if UNITY_EDITOR
		if(m_holder != null && !name.Contains($"[{m_holder.GameState.m_id}]"))
		{
			name += $" / [{m_holder.GameState.m_id}]";
		}
		#endif
	}
	
	public bool IsOnTrain { get { return m_isOnTrain; } }
	/*public void SetOnTrain(bool _onTrain)
    {
		m_isOnTrain = _onTrain;

		if (_onTrain)
		{
			Deregister();
		}
		else
		{
			Register();
		}
	}*/
	virtual public void OnCreate() { IsBeingDragged = false; }
	virtual public void OnRemove() { Deregister(); }
	
	public static bool HaveServerPickups { get; set; } = false;

	public static void CreateSavedPickups(Transform _parent)
	{
		CreateSavedPickups(GameManager.Me.m_state.m_pickups, _parent);
		HaveServerPickups = true;
	}
	
	public static void CreateSavedPickups(List<GameState_Pickup> _list, Transform _parent)//remove return ref
	{
		var cullList = new List<GameState_Pickup>(); 
		foreach (var p in _list) {
			ReactPickupPersistent pickup;
			
			if (p == null || p.Pickup != null || (p.Pickup != null && p.Pickup.IsBeingDragged))
				continue;
			if (string.IsNullOrEmpty(p.m_type) || p.m_quantity == 0)
			{
				cullList.Add(p);
				continue;
			}

			float pickupQuantity = p.m_quantity;
            Vector3 throwVelocity = Vector3.zero;
            if (p.m_position.x < -9999)
            {
	            // stolen! throw from camera
	            const float c_stealThrowSpeed = 30;
	            const float c_stealThrowRandomSpeed = 1;
	            var camXform = Camera.main.transform;
	            p.m_position = camXform.position;
	            var side = Random.Range(-1f, 1f) * c_stealThrowRandomSpeed;
	            var up = Random.Range(-1f, 1f) * c_stealThrowRandomSpeed;
	            throwVelocity = camXform.forward * c_stealThrowSpeed + camXform.right * side + camXform.up * up;
            }
            else if (p.m_position.x < GlobalData.c_terrainMin.x || p.m_position.x > GlobalData.c_terrainMax.x || p.m_position.z < GlobalData.c_terrainMin.z || p.m_position.z > GlobalData.c_terrainMax.z || p.m_position.y < -1000f)
			{
				// out of bounds, destroy
				cullList.Add(p);
				continue;
			}
            p.m_wasEverInOwnedDistrict |= DistrictManager.Me.IsWithinDistrictBounds(p.m_position, true);
            var res = NGCarriableResource.GetInfo(p.m_type);
			if (res.IsProduct) {
				if (GameManager.IsVisitingInProgress) continue;
				if (res.GetProduct() == null)
				{
					cullList.Add(p);
					continue;
				}

				PickupSetup pickupSetup = new PickupSetup()
				{
					m_quantity = pickupQuantity,
					m_holder = _parent,
					m_killPickup = false,
					m_onComplete = _o =>
					{
						_o.transform.position = p.m_position;
						_o.GetComponent<NGMovingObject>().SetCollisionStyle(COLLISIONSTYLE.DEFAULT);
						var pickupCmp = _o.AddComponent<Pickup>();
						_o.GetComponent<ReactPickupPersistent>().FinishLoading(p);
						if(throwVelocity.sqrMagnitude > 0)
							_o.GetComponent<ReactPickupPersistent>().ThrownByPlayer(throwVelocity);
						if (p.m_wasEverInOwnedDistrict)
						{
							pickupCmp.SetOverrideDistrictFilter(true);
							_o.IgnoreDistrictFilter();
						}
					}
				};
				
				pickup = NGReactPickupAny.Create(null, res,  pickupSetup);
				if (pickup == null)
				{
					cullList.Add(p);
					continue;
				}
				pickup.name = "Product";
			} else {
				if (NGCarriableResource.GetPrefabForResource(res) == null)
				{
					cullList.Add(p);
					continue;
				}
				pickup = Create(null, res, pickupQuantity, _parent, false, _o => {
					_o.transform.position = p.m_position;
					_o.GetComponent<NGMovingObject>().SetCollisionStyle(COLLISIONSTYLE.DEFAULT);
					var pickupCmp = _o.AddComponent<Pickup>();
					_o.GetComponent<ReactPickupPersistent>().FinishLoading(p);
					if (throwVelocity.sqrMagnitude > 0) _o.GetComponent<ReactPickupPersistent>().ThrownByPlayer(throwVelocity);
					if (p.m_wasEverInOwnedDistrict)
					{
						pickupCmp.SetOverrideDistrictFilter(true);
						_o.IgnoreDistrictFilter();
					}
				});
				pickup.name = p.m_type;
			}
			pickup.m_pickupData = p;
			p.Pickup = pickup;
		} 
		var pickupList = GameManager.Me.m_state.m_pickups;
		foreach (var p in cullList)
		{
			pickupList.Remove(p);
		}

		PickupsLoaded = true;
	}
	
	public static bool PickupsLoaded { get; set; }
	public void FinishLoading(GameState_Pickup _p) {
		m_pickupData = _p;
		m_spawnedFrom = (_p.m_spawnedFrom == -1) ? null : GameManager.Me.GetNGCommander<NGCommanderBase>(_p.m_spawnedFrom);
	}
	
	public static ReactPickupPersistent CreateResearchObject(string _block, Transform _parentTransform)
    {
		var temp = NGCarriableResource.GetInfo("Research");
		var go = Instantiate(NGCarriableResource.GetPrefabForResource(temp), _parentTransform);
		var rprwc = go.GetComponent<ReactPickupPersistent>();

		return rprwc;
	}

	//TS - Watch out, This is a strange one - this is called to set pickup up for worker to deliver it. Although it is parented to global first. then manually to the worker
	public static ReactPickupPersistent CreateItem(NGCommanderBase _spawnedFrom, NGCarriableResource _resource, int _quantity, NGCommanderBase _forBuilding)
	{
		var item= Create(_spawnedFrom, _resource, _quantity, GlobalData.Me.m_pickupsHolder);
		if (item)
		{
			item.m_intendedDestination = _forBuilding;
		}

		return item;
	}
	public static ReactPickupPersistent Create(NGCommanderBase _spawnedFrom, NGCarriableResource _contents, float _quantity, Transform _parentTransform, bool _killPickup = false, System.Action<GameObject> _cb = null)
	{
		if (_contents == null || _contents.Name.Equals("None")) return null;
		var res = NGCarriableResource.GetPrefabForResource(_contents);
        if (res == null) return null; 
        var go = Instantiate(res, _parentTransform);
		var rprwc = go.GetComponent<ReactPickupPersistent>();
		rprwc.Initialise(_spawnedFrom, _contents, _quantity);
		if (_cb != null) _cb(go);
		var rb = go.GetComponent<Rigidbody>();
		/*TODO - check if held  if (rb && ReactInput.Me.m_heldObjects.Find(o => o.m_object == rprwc) != null)
		{
			rb.isKinematic = true;
			go.transform.localPosition = Vector3.zero;
		}*/
		if (_killPickup)
		{
			rprwc.enabled = false;
			rprwc.OnRemove();
			Destroy(go.GetComponent<Rigidbody>());
		}
		else
			rprwc.OnCreate();
		return rprwc;
	}

	//==== IPickupBehaviour
	public void OnPickup()
	{
		IsBeingDragged = true;
		PrepareForHolding();
	}
	public virtual void OnDrop(Vector3 _pos, GameObject _target, GameObject _source, Vector3 _smoothedDragMove, bool _undo, SpecialHandlingAction _action)
	{
		m_isChaining = false;
		if (m_nextInChain != null) (m_nextInChain as ReactPickupPersistent)?.OnDrop(_pos, _target, m_nextInChain.gameObject, _smoothedDragMove, _undo, _action);
		
		IsBeingDragged = false;
		foreach(Transform child in transform)
			child.GetComponentInChildren<NGPickupThing>()?.DestroyMe();

		if(_undo)
		{
			ShakenFromHand();
//			return;
		}
		else if (_target != null) {
			var sourceBuilding = m_spawnedFrom;
			var destBuilding = _target.GetComponentInChildren<NGCommanderBase>();
			if (destBuilding == null) {
				/*if (_target == GameManager.Me.m_stealHolderRef.gameObject) {
					if (GameManager.Me.m_stealHolderRef.StealPickup(_source)) {
						Destroy(_source.gameObject);
						return;
					}
				}*/
			} else {
				/*if ((Input.GetKey(KeyCode.LeftShift) || Input.GetKey(KeyCode.RightShift)) && sourceBuilding && NGUnlocks.MultiDrop)
				{
					var fillBy = Mathf.Min(sourceBuilding.NumOutputs,  destBuilding.MaxInputs - (destBuilding.LowestStock + 1));
					
					sourceBuilding.NumOutputs-= fillBy;
					destBuilding.AddCarriedResource(m_contents, (int)fillBy);
				}*/
				PlayDropAudio();
				DroppedByPlayerAt(destBuilding);
				destBuilding.DroppedFromHand(this, Vector3.zero);
				return;
			}
		}

		PlayDropAudio();

		if (!Contents.IsNone) {
			_source.GetComponent<NGMovingObject>().SetCollisionStyle(NGMovingObject.COLLISIONSTYLE.DEFAULT);
			RagdollHelper.ThrowObject(_source, Vector3.zero, null, null, (_interrupted)=>{}, false);
		}
	}

	private void PlayDropAudio()
	{
		if (m_isPrimary)
			AudioClipManager.Me.PlaySound("PlaySound_HandThrowItem", transform);
	}

	virtual public GameObject GetBestTarget(int _inputId, GameObject _obj, Vector3 _pos, out SpecialHandlingAction _action) {
		/*if (GameManager.Me.m_stealHolderRef.CheckPickupSteal(_inputId, _obj)) {
			return GameManager.Me.m_stealHolderRef.gameObject;
		}*/
		return InputUtilities.NGFindBestCollider(_inputId, _obj.GetComponentInChildren<NGMovingObject>(), out _action);
	}
	public GameObject GetSource()
	{
		NGPickupThing.Create(gameObject);
		return gameObject;
	}
	public Color BezierColour(GameObject _o, GameObject _t) {
		return m_contents.BezierColour();
	}

	override public void AssignToCarrier(NGMovingObject _carrier, bool _setCarriedObj = true, bool _savePickup = false) {
		if(_savePickup)
		{
			Register();
			m_pickupData.m_holderObject = _carrier.m_ID;
			m_pickupData.m_holderDest = -1;//(_object as NGWorker)?.m_controlledByBuilding?.m_linkUID ?? -1;
		}
		base.AssignToCarrier(_carrier, _setCarriedObj, _savePickup);
	}
	
	public virtual void Consume() {
		m_pickupData.m_holderObject = -1;
		m_pickupData.m_holderDest = -1;
		base.Consume();
	}
	
	public override void OnBeginDrag(PointerEventData _eventData)
	{
		//If being held by a worker, we'd want to click on that instead so as not to steal their product
		if (m_holder)
			m_holder.OnBeginDrag(_eventData);
		else
			Internal_BeginDrag(_eventData, true);
	}
	public override bool IsOkayToGrab()
	{
		return true;
	}

    public override void OnPickUp()
    {
		base.OnPickUp();
		transform.RunNextFrame(() => PrepareGraphicsForPickup());
    }

	public	override float PickupThrowThresholdMultiplier()
	{
		return	m_pickupThrowThreshold;
	}
	protected override void OnDestroy()
	{
        OnRemove();
        base.OnDestroy();
	}
	public override void ThrownByPlayer(Vector3 _throwVelocity)
	{
		SetCollisionStyle(COLLISIONSTYLE.DEFAULT);
		var rb = GetComponent<Rigidbody>();
		if (rb) rb.AddForce(_throwVelocity, ForceMode.VelocityChange);
		// Create ragdoll clone

		//AudioClipManager.Me.PlaySound(m_throwPersonSound, transform);
	}

	override public void ShakenFromHand(Vector3 _returnPos) 
	{
		if (m_spawnedFrom)
			m_spawnedFrom.ShakenFromHand(this);
		else
		{
			Destroy(gameObject);
		}

		base.ShakenFromHand(_returnPos);
	}

	override public float GetDropScore(string _name) { return 1f; }
}