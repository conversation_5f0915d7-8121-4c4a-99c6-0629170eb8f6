using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class ReactPickupRawMaterialCrate : ReactPickupPersistent
{
	public static ReactPickupRawMaterialCrate Create(NGCarriableResource _contents, float _quantity, Transform _parentTransform)
	{
		var go = Instantiate(NGCarriableResource.GetPrefabForResource(_contents), _parentTransform);
		var rprwc = go.GetComponent<ReactPickupRawMaterialCrate>();
		rprwc.Initialise(null, _contents, _quantity);
		return rprwc;
	}

	
	
	// If interrupted while carrying raw materials, send the stock back to the nearest warehouse/harvester
	public override void Interrupted(NGMovingObject _object)
	{
		float closestDistSqd = float.MaxValue;
		/*if(IsRawMaterial)
		{
			NGWarehouse closestWarehouse = null;
			foreach(var f in NGManager.Me.FactoryList)
			{
				var wh = f as NGWarehouse;
				if (wh == null) continue;
				var dist = Vector3.SqrMagnitude(transform.position - wh.transform.position);
				if (dist < closestDistSqd)
				{
					closestDistSqd = dist;
					closestWarehouse = wh;
				}
			}

			if (closestWarehouse != null)
			{
				closestWarehouse.AddStock(ContentsAsMaterial, m_quantity);
				if(_object.MyJob is NGHarvester)
					_object.MyJob.RemoveStock(ContentsAsMaterial, m_quantity);
			}
		}
		else
		{
			NGHarvester closestHarvester = null;
			foreach(var f in NGManager.Me.FactoryList)
			{
				var harvester = f as NGHarvester;
				if(harvester == null)
					continue;
				//TODO if(harvester.ResourceType != ContentsAsMaterial)
				//	continue;

				var dist = Vector3.SqrMagnitude(transform.position - harvester.transform.position);
				if (dist < closestDistSqd)
				{
					closestDistSqd = dist;
					closestHarvester = harvester;
				}
			}

			if (closestHarvester != null)
				closestHarvester.AddStock(ContentsAsMaterial, m_quantity);
		}*/

		_object.GetComponentInChildren<Animator>().SetBool(GlobalData.CarryProductAnim, false);
	}

	public override void AssignToCarrier(NGMovingObject _carrier, bool _setCarriedObj = true, bool _savePickup = true) {
		base.AssignToCarrier(_carrier, _setCarriedObj, _savePickup);
//		AudioClipManager.Me.PlaySound( "PickUpResource", this.gameObject );
	}

	public override void Consume()
	{
//		AudioClipManager.Me.PlaySoundAndForget( "DropResource", this.gameObject );
	}
}