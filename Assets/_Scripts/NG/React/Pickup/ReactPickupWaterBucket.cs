using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class ReactPickupWaterBucket : ReactPickup
{
	public static ReactPickupWaterBucket Create(float _quantity, Transform _parentTransform)
	{
		var go = Instantiate(NGManager.Me.m_waterBucketPrefab, _parentTransform);
		var rpwb = go.GetComponent<ReactPickupWaterBucket>();
		rpwb.Initialise(_quantity);
		return rpwb;
	}

	void Initialise(float _quantity)
	{
		m_quantity = _quantity;
	}

	public override void AssignToCarrier(NGMovingObject _carrier, bool _setCarriedObj = true, bool _savePickup = true) {
		base.AssignToCarrier(_carrier, _setCarriedObj, _savePickup);
		_carrier.GetComponentInChildren<Animator>().SetBool("CarryBucket", true);
	}

	public override void Interrupted(NGMovingObject _object) {
		_object.GetComponentInChildren<Animator>().SetBool("CarryBucket", false);
	}
}
