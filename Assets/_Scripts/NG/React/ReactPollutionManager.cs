using System;
using System.Collections;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;


public class ReactPollutionManager : MonoSingleton<ReactPollutionManager>
{
	[System.Serializable]
	public class ShaderCurve
	{
		public string m_variableName;
		public AnimationCurve m_curve = AnimationCurve.EaseInOut( 0f, 0f, 1f, 1f );
		
		public bool IsValid() { return m_curve.keys.Length > 0 && !string.IsNullOrEmpty(m_variableName); }
		public float GetValue(float _pollutionLevel)
		{
			return m_curve.Evaluate(_pollutionLevel);
		}
	}

	// -- static --
	private readonly int 						SHADER_COLOR_PARAM_NAME 				= Shader.PropertyToID("_fog_basecolor");
	private const int 							TEMP_GRID_ROWS 							= 1000;
	
	// -- public 
	[Header("System Parameters")] 
	public	float								m_pollutionDecayPerDay 					= 0.0f;
	public	float								m_maxPollution 							= 10f;
	public	bool								m_pollutionUnlock						= false;
	public	bool								PollutionUnlock							{ get { return m_pollutionUnlock; } set { m_pollutionUnlock = value; } }
	
	Dictionary<int, PollutionCell> m_cells = new Dictionary<int, PollutionCell>();

	[Header("Fog Plane Parameters")]
	public Material 							m_fogPlaneMaterial;
	public float 								m_fogPlaneOverlapDistance				= 0;
	public int	 								m_fogPlaneAlphaFadeSteps 				= 0;
	public int									m_heightfieldToPlaneVertexResolution 	= 5; // Number of heightfield steps to jump
	public float								m_fogPlaneSize 							= 50f;
	public ShaderCurve[]						m_shaderCurves;
	
	[Header("Debug Polluters")]
	public bool m_debugDrawPolluters;
	[Range(0f, 1f)]
	public float m_debugDrawPollutersOpacity = 0.4f;
	
	[Header("Debug Pollution Cells")]
	public 	float								m_debugVFXStartLevel					= 0;
	public bool m_debugDrawCells;
	[Range(0f, 3f)]
	public float m_debugDrawScale;
	public float m_debugDrawYOffset;
	public bool m_debugDrawVertically;
	public bool m_debugDrawCellBorders;
	
	// -- private --
	bool m_selectCells;
	bool m_createOnSelection;
	PollutionCell m_selectedCell;
	
	private Dictionary<int, MeshRenderer> 		m_fogPlanes 							= new Dictionary<int, MeshRenderer>();
	private float								m_pollutionTimer;

	bool m_initialized = false;

	private MaterialPropertyBlock m_propBlock = null;

	static int s_gridDimensions;
	static Vector3 s_originOffset;

	const float c_intensityNodeRadius = 0.3f;
	const float c_intensityNodeSep = 0.1f;
	const int c_intensityNodeCount = 10;

	//-------------------------------------------------------------------------------------------
	[Serializable]
	public struct SVector2Int {
		public int x, y;
		public SVector2Int(int _x, int _y) { x = _x; y = _y; }
		public static SVector2Int operator +(SVector2Int a, SVector2Int b) => new SVector2Int(a.x + b.x, a.y + b.y);
	}
	[Serializable]
	public class PollutionCell
	{
		[SerializeField] Color m_data;
		int		m_changeCount = 0;
		public Color Data { get { return m_data; } }
		public SVector2Int m_coords;
		
		// all this complexity is to protect against updating the intensity of pollution in a cell when you actually just want to change the colour
		public float Intensity
        {
            get { return m_data.a; }
            set { m_data.a = Mathf.Clamp01(value); OnPollutionIntensityChange(); }
        }
		public Color Colour
		{
			get { return new Color(m_data.r, m_data.g, m_data.b);  } 
			set { m_data.r = value.r; m_data.g = value.g; m_data.b = value.b; }
		}
		public	int	ChangeCount	
		{
			get { return m_changeCount; }
			set { m_changeCount = value; }
		}
		
		//TODO: Consider (not necessarily a requirement) how we deal with the maximum polluter redcuing its intensity and how we determine what colour takes over - needs design input
		[HideInInspector] public float m_largestContribution;
		
		public PollutionCell(SVector2Int _coords)
		{
			m_coords = _coords;
		}

		public PollutionCell(Vector2 _coords, SaveContainers.Color _data)
		{
			m_coords = new SVector2Int((int) _coords.x, (int) _coords.y);
			m_data = _data;
		}

		//TODO List<CountrysideManager.QuadTreeObject> m_cachedQuadHits = new List<CountrysideManager.QuadTreeObject>();
		void OnPollutionIntensityChange()
		{
			// -- Update Pollution Level for all environment elements -- //
			/*TODO if (HardwareLevels.Settings != null && HardwareLevels.ApplyPollutionToTrees) {
				var currentcellCenter = Me.GetCellCentre(m_coords);
				var cellSize = new Rect(currentcellCenter.x - Me.m_fogPlaneSize / 2 - 1, currentcellCenter.z - Me.m_fogPlaneSize / 2 - 1, Me.m_fogPlaneSize + 1, Me.m_fogPlaneSize + 1); // extrude by 1
				var envinAOE = m_cachedQuadHits; envinAOE.Clear();
				CountrysideManager.environmentArchive.RetrieveObjectsInArea(cellSize, ref envinAOE); // get objects within layer
				foreach (var x in envinAOE)
					x.GetInstance().Pollution = Intensity;
				m_changeCount++;
			}*/
		}

		public override string ToString()
		{
			return string.Format("[{0} - <{1}, {2}, {3}> @ ({4}, {5})]", Intensity, Colour.r, Colour.g, Colour.b, m_coords.x, m_coords.y);
		}

		protected bool Equals(PollutionCell other)
		{
			return m_coords.Equals(other.m_coords);
		}

		public override bool Equals(object obj)
		{
			if (ReferenceEquals(null, obj)) return false;
			if (ReferenceEquals(this, obj)) return true;
			if (obj.GetType() != this.GetType()) return false;
			return Equals((PollutionCell) obj);
		}

		public override int GetHashCode()
		{
			return m_coords.GetHashCode();
		}

		public SaveContainers.SavePollutionCell GetProto()
		{
			var proto = new SaveContainers.SavePollutionCell();
			var protoCoords = new SaveContainers.Vector2();
			protoCoords.x = m_coords.x;
			protoCoords.y = m_coords.y;
			proto.m_coords = protoCoords;
			proto.m_data = m_data;

			return proto;
		}
	}
	
	//-------------------------------------------------------------------------------------------
	void Start()
	{
		Init();
	}

	void Init()
	{
		if (m_initialized) return;

		m_propBlock = new MaterialPropertyBlock();
	
		//Assume square grid, m_gridDimensions is min grid width to cover whole map
		s_originOffset = new Vector3(512, 0, 512);
		s_gridDimensions = Mathf.CeilToInt(2f * s_originOffset.x / m_fogPlaneSize);
	}
		
	//-------------------------------------------------------------------------------------------
	public void Load(Dictionary<int, PollutionCell> _pollutionCells)
	{
		m_cells = _pollutionCells;
		UpdateAllFogPlanes();
	}
	
	//-------------------------------------------------------------------------------------------
	public Dictionary<int, PollutionCell> GetProto()
	{
		return m_cells;
	}

	//-------------------------------------------------------------------------------------------
	private void Update()
	{
		if( !m_pollutionUnlock ) {
			return;
		}

		float time = Time.time;

		if( time> m_pollutionTimer )
		{
			m_pollutionTimer = time + (NGManager.Me.m_dayNightCycleLength / 24f);
			foreach(var cell in m_cells.Values)
			{
				cell.Intensity += m_pollutionDecayPerDay / 24f;
			}
		}
	}

	//-------------------------------------------------------------------------------------------
	void DebugDestroyPlanes()
	{
		foreach (var plane in m_fogPlanes.Values)
		{
			Destroy(plane.gameObject);
		}

		m_fogPlanes.Clear();
	}
	
	//-------------------------------------------------------------------------------------------
	void DebugClearPollution()
	{
		DebugDestroyPlanes();
		m_cells.Clear();
	}
	
	//-------------------------------------------------------------------------------------------
	HashSet<PollutionCell> CellsInRadius(Vector3 _position, float _radius, bool _createCells=false)
	{
		var cells = new HashSet<PollutionCell>();
		var cellPos = GetCellCoords(_position);
		var radCells = Mathf.CeilToInt(_radius / m_fogPlaneSize);
		for (int y = -radCells; y <= radCells; y++) {
			for (int x = -radCells; x <= radCells; x++) {
				if (x * x + y * y <= radCells * radCells) {
					cells.Add(GetCell(new SVector2Int(cellPos.x + x, cellPos.y + y), _createCells));
				}
			}
		}
		return cells;
		/*
		// BO - 06/07/2018 - you might not be convinced all 8 directions are necessary, I orignially didn't think they were.
		// Get a pen and some paper and you'll see why.
		if (_radius > m_fogPlaneSize / 2f) Debug.LogError("Attempting to find pollution cells in too large a radius. Cells will be missed.");
		
		var cells = new HashSet<PollutionCell>();


		var t = Vector3.forward;
		var b = Vector3.back;
		var l = Vector3.left;
		var r = Vector3.right;
		var tl = (t + l).normalized;
		var tr = (t + r).normalized;
		var bl = (b + l).normalized;
		var br = (b + r).normalized;

		cells.Add(GetCell(_position + _radius * t, _createCell: _createCells));
		cells.Add(GetCell(_position + _radius * b, _createCell: _createCells));
		cells.Add(GetCell(_position + _radius * l, _createCell: _createCells));
		cells.Add(GetCell(_position + _radius * r, _createCell: _createCells));
		cells.Add(GetCell(_position + _radius * tl, _createCell: _createCells));
		cells.Add(GetCell(_position + _radius * tr, _createCell: _createCells));
		cells.Add(GetCell(_position + _radius * bl, _createCell: _createCells));
		cells.Add(GetCell(_position + _radius * br, _createCell: _createCells));
		
		return cells;*/
	}

	//-------------------------------------------------------------------------------------------
	public void AddPollution(Vector3 _position, float _intensity, float _radius)
	{
		if(!PollutionUnlock)
		{
			return;
		}

		var cells = CellsInRadius(_position, _radius, _createCells:true);

		foreach (var cell in cells)
		{
			cell.Intensity = cell.Intensity + _intensity;
			UpdateFogPlane(cell);
		}
	}

	//-------------------------------------------------------------------------------------------
	public	void	BlowPollution(Vector3 _position, float _intensity, Quaternion _angle)
	{
		var		cells = CellsInRadius(_position, 0.1f, _createCells:true);

		SVector2Int		send_to_cell_add = new SVector2Int(0, 0);
		int				direction = (int)(_angle.eulerAngles.y/90f)%4;
		switch(direction)
		{
			case	0:		
				send_to_cell_add = new SVector2Int(0, 1);
				break;
			case	1:
				send_to_cell_add = new SVector2Int(1, 0);
				break;
			case	2:
				send_to_cell_add = new SVector2Int(0, -1);
				break;
			case	3:
				send_to_cell_add = new SVector2Int(-1, 0);
				break;
		}
		foreach(var cell in cells)
		{
			var send_to_cell = cell.m_coords + send_to_cell_add;
			float	intensity = (Mathf.Abs(_intensity) > cell.Intensity) ? -cell.Intensity : _intensity;
			if(intensity < 0f)
			{
				if(send_to_cell.x >= 0 && send_to_cell.y >=0)
				{
					var send_cell = GetCell(send_to_cell, true);
					if(send_cell != null)
						send_cell.Intensity = send_cell.Intensity +(-intensity);
				}
				cell.Intensity = cell.Intensity + intensity;
			}
		}
	}
	//-------------------------------------------------------------------------------------------
	/// <summary>
	/// Gets the highest pollution cell value.
	/// </summary>
	/// <returns>The highest pollution cell value.</returns>
	public	float	GetHighestPollutionCellIntensity()
	{
		int		highest_change_count = int.MinValue;
		float	highest_change_intensity = 0f;
		foreach(var cell in m_cells)
		{
			if(cell.Value.ChangeCount >= highest_change_count)
			{
				highest_change_count = cell.Value.ChangeCount;
				highest_change_intensity = cell.Value.Intensity;
			}

		}
		return	highest_change_intensity;
	}


	//-------------------------------------------------------------------------------------------
	/// <summary>
	/// Returns the pollution intensity for the cell at the given world position. Returns 0 if the cell doesn't exist.
	/// </summary>
	/// <param name="_position"></param>
	/// <returns></returns>
	public float GetPollution(Vector3 _position)
	{
		// not using GetCell as we don't care if the cell doesn't exist
		var key = CellKey(_position);
		PollutionCell cell;
		if (m_cells.TryGetValue(key, out cell)) return cell.Intensity;
		return 0;
	}
	
	//-------------------------------------------------------------------------------------------
	public void ClearContributionData()
	{
		foreach(var cell in m_cells)
		{
			cell.Value.m_largestContribution = -1f;
		}
	}
	
	//-------------------------------------------------------------------------------------------
	/// <summary>
	/// Updates the colour of the pollution fog at the given position if the inensity passed is the greatest contributor to the cell.
	/// </summary>
	/// <param name="_position"></param>
	/// <param
	///     name="_radius">
	/// </param>
	/// <param name="_intensity"></param>
	/// <param name="_colour"></param>
	public void SetColour(Vector3 _position, float _radius, float _intensity, Color _colour)
	{
		foreach (var cell in CellsInRadius(_position, _radius, _createCells:true)) // we should only be setting the colour of cells that will be used eventually
		{
			if (!(_intensity > cell.m_largestContribution))
				continue;

			cell.m_largestContribution = _intensity;
			cell.Colour = _colour;
			UpdateFogPlane(cell);
		}
	}	
	
	//-------------------------------------------------------------------------------------------
	public PollutionCell GetCell(Vector3 _position, bool _createCell = false)
	{
		return GetCell(GetCellCoords(_position), _createCell);
	}

	PollutionCell GetCell(SVector2Int _coords, bool _createCell = false)
	{
		var key = CellKey(_coords);
		if (!m_cells.ContainsKey(key))
		{
			if (!_createCell)
			{
				Debug.LogErrorFormat("Trying to access non existent polltuion cell at {0}", _coords);
				return null;
			}
			
			var cell = new PollutionCell(_coords);
			m_cells.Add(key, cell);
			return cell;
		}

		return m_cells[key];
	}

	//-------------------------------------------------------------------------------------------
	static int CellKey(Vector3 _v)
	{
		// GL - 070420 - optimise by using direct operations, about 10x faster
		float div = Me.m_fogPlaneSize;
		int x = (int)((_v.x + s_originOffset.x) / div);
		int y = (int)((_v.z + s_originOffset.z) / div);
		return x + y * s_gridDimensions;
		//return CellKey(GetCellCoords(_v));
	}
	
	static int CellKey(SVector2Int _v)
	{
		return _v.x + _v.y * s_gridDimensions;
	}

	//-------------------------------------------------------------------------------------------
	static SVector2Int GetCellCoords(Vector3 _pos)
	{
		_pos += s_originOffset;
		return new SVector2Int((int) (_pos.x / Me.m_fogPlaneSize), (int) (_pos.z / Me.m_fogPlaneSize));
	}

	//-------------------------------------------------------------------------------------------
	private Vector3 GetCellCentre(SVector2Int _v)
	{
		float hSize = m_fogPlaneSize/2f;
		return new Vector3(_v.x * m_fogPlaneSize + hSize, 0, _v.y * m_fogPlaneSize + hSize) - s_originOffset; 
	}
		
	//-------------------------------------------------------------------------------------------
	public void UpdateFogPlane(PollutionCell _cell)
	{
		MeshRenderer meshRenderer = null;
		
		if(!m_fogPlanes.TryGetValue(CellKey(_cell.m_coords), out meshRenderer))
		{
			float halfSizePlusOverlap = m_fogPlaneSize/2f + m_fogPlaneOverlapDistance;
			
			// Create object
			GameObject go = new GameObject(string.Format("Patch-{0}-{1}", (int)_cell.m_coords.x, (int)_cell.m_coords.y));
			go.transform.parent = this.transform;
			go.transform.localPosition = Vector3.zero;
			
			// Assign material
			meshRenderer = go.AddComponent<MeshRenderer>();
			meshRenderer.sharedMaterial = m_fogPlaneMaterial;
			meshRenderer.shadowCastingMode = UnityEngine.Rendering.ShadowCastingMode.Off;
			
			// Assign mesh
			var meshFilter = go.AddComponent<MeshFilter>();
			meshFilter.sharedMesh = TerrainManager.Me.GetLandscapeMesh(GetCellCentre(_cell.m_coords), halfSizePlusOverlap, halfSizePlusOverlap, 1f, 1f, m_heightfieldToPlaneVertexResolution, 0, TerrainManager.MeshBlend.None, m_fogPlaneAlphaFadeSteps, true, true);
						
			// Add to dictionary
			m_fogPlanes.Add(CellKey(_cell.m_coords), meshRenderer);
		}		
		
		m_propBlock.Clear();

		// Update color
		m_propBlock.SetColor( SHADER_COLOR_PARAM_NAME, _cell.Data );

		// Update shader curves
		foreach(var curve in m_shaderCurves)
		{
			if(!curve.IsValid())
				continue;
			m_propBlock.SetFloat(curve.m_variableName, curve.GetValue(_cell.Intensity));
		}

		meshRenderer.SetPropertyBlock( m_propBlock );
		meshRenderer.enabled = _cell.Intensity > 0.0f;
	}
	
	//-------------------------------------------------------------------------------------------	
	public void UpdateAllFogPlanes()
	{
		foreach (var cell in m_cells.Values)
			UpdateFogPlane(cell);
	}

	//-------------------------------------------------------------------------------------------
	void OnDrawGizmos()
	{
		if (!m_debugDrawCells) return;

		var radius = c_intensityNodeRadius * m_debugDrawScale;
		var sep = c_intensityNodeSep * m_debugDrawScale;

		foreach (var cell in m_cells.Values)
		{
			var c = cell.Colour;
			var invc = new Color(1 - c.r, 1 - c.g, 1 - c.b);
			var t = cell.Intensity * c_intensityNodeCount;
			var cellPos = GetCellCentre(cell.m_coords);
			var nodePos = cellPos.NewY(m_debugDrawYOffset + c_intensityNodeRadius / 2f);
			for (int i = 0; i < c_intensityNodeCount; i++)
			{
				Gizmos.color = Color.Lerp(invc, c, t);
				Gizmos.DrawSphere(nodePos, radius);
				t -= 1;
				nodePos = m_debugDrawVertically ? nodePos.NewY(nodePos.y + 2 * radius + sep) : nodePos.NewZ(nodePos.z + 2 * radius + sep);					
			}

			if (m_debugDrawCellBorders)
			{
				Gizmos.color = Color.white;
				var bl = new Vector3(cellPos.x - m_fogPlaneSize / 2, m_debugDrawYOffset, cellPos.z - m_fogPlaneSize / 2);
				var tl = bl + new Vector3(0, 0, m_fogPlaneSize);
				var br = bl + new Vector3(m_fogPlaneSize, 0, 0);
				var tr = tl + new Vector3(m_fogPlaneSize, 0, 0);
		
				Gizmos.DrawLine(bl, br);
				Gizmos.DrawLine(br, tr);
				Gizmos.DrawLine(tr, tl);
				Gizmos.DrawLine(tl, bl);
			}
		}
	}
	
	static DebugConsole.Command s_pollute = new DebugConsole.Command("pollute", _s => {
		Me.PollutionUnlock = true;
		foreach (var f in NGManager.Me.FactoryList) {
			float intensity = 1;
			float radius = 25;
			Me.AddPollution(f.transform.position, intensity, radius);
		}
	});

	#if UNITY_EDITOR
	[CustomEditor(typeof(ReactPollutionManager))]
	public class ReactPollutionManagerEditor : Editor
	{
		ReactPollutionManager This { get { return (ReactPollutionManager) target; } }
		public override void OnInspectorGUI()
		{
			base.OnInspectorGUI();
			
			if (!Application.isPlaying)
				return;

			EditorGUILayout.Space();

			if (GUILayout.Button("Clear Pollution"))
			{
				This.m_selectedCell = null;
				This.DebugClearPollution();
			}
			
			if (GUILayout.Button("Destroy Planes"))
			{
				This.DebugDestroyPlanes();
			}
		
			if (GUILayout.Button("Rebuild Planes"))
			{
				This.DebugDestroyPlanes();
				This.UpdateAllFogPlanes();
			}

			EditorGUILayout.Space();
			
			This.m_selectCells = EditorGUILayout.Toggle("Select Cells", This.m_selectCells);
			if (!This.m_selectCells) This.m_selectedCell = null;

			if (This.m_selectCells)
			{
				This.m_createOnSelection= EditorGUILayout.Toggle("Create On Selection", This.m_createOnSelection);				
			}

			if (This.m_selectedCell == null) return;

			EditorGUILayout.Space();
			
			EditorGUILayout.LabelField(string.Format("Selected Cell: {0}", This.m_selectedCell.m_coords));
			
			EditorGUI.BeginChangeCheck();
			
			This.m_selectedCell.Intensity = EditorGUILayout.Slider("Pollution Level", This.m_selectedCell.Intensity, 0, 1);
			This.m_selectedCell.Colour = EditorGUILayout.ColorField("Colour", This.m_selectedCell.Colour);

			if (EditorGUI.EndChangeCheck())
			{
				This.UpdateFogPlane(This.m_selectedCell);
			}
		}

		void OnSceneGUI()
		{
			if (!Application.isPlaying || !This.m_selectCells) 
				return;
			
			if (This.m_selectedCell != null)
				DoIntensityHandle();
			
			var e = Event.current;
			
			if (e.type == EventType.MouseDown && e.button == 0) // left click
			{
				var mouseRay = HandleUtility.GUIPointToWorldRay(e.mousePosition);
				PickCell(mouseRay);
				
				Repaint();
				
				e.Use();
			}
			else if (e.type == EventType.Layout)
			{
				HandleUtility.AddDefaultControl(GUIUtility.GetControlID(GetHashCode(), FocusType.Keyboard));
			}
		}

		void DoIntensityHandle()
		{
			EditorGUI.BeginChangeCheck();

			Handles.color = This.m_selectedCell.Colour;
			var scale = c_intensityNodeCount * (2 * c_intensityNodeRadius + c_intensityNodeSep) * This.m_debugDrawScale;
			
			var start = This.GetCellCentre(This.m_selectedCell.m_coords).NewY(This.m_debugDrawYOffset);
			var dir = This.m_debugDrawVertically ? Vector3.up : Vector3.forward;
			var point = start + dir * This.m_selectedCell.Intensity * scale;
			point = Handles.Slider(point, dir, HandleUtility.GetHandleSize(point), Handles.ArrowHandleCap, 0f);
			var diff = This.m_debugDrawVertically ? point.y - start.y : point.z - start.z;
			This.m_selectedCell.Intensity = Mathf.Clamp01(diff / scale);

			if (EditorGUI.EndChangeCheck())
			{
				This.UpdateFogPlane(This.m_selectedCell);
				Repaint();
			}
		}


		void PickCell(Ray _mouseRay)
		{
			var roadGridPlane = new Plane(Vector3.up, This.transform.position.NewY(This.m_debugDrawYOffset));
			float intersectDistance;
			if (roadGridPlane.Raycast(_mouseRay, out intersectDistance))
			{
				var intersect = _mouseRay.GetPoint(intersectDistance);
				This.m_selectedCell = This.GetCell(intersect, _createCell:This.m_createOnSelection);
			}
		}
	}
#endif
}
