using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Analytics;
using RewardTable = System.Collections.Generic.Dictionary<string, System.Collections.Generic.List<string>>;

public static class ResearchLabRewardsController
{
	// Not using right now but maybe needed for future tasks
	public class ProductPartsData {
		public string m_productLine;
		public ResearchLabRewardsController.Rarity m_rarity;
		public string[] m_parts;
		public ProductPartsData(string _productLine, ResearchLabRewardsController.Rarity _rarity, string[] _parts){
			m_productLine = _productLine;
			m_rarity = _rarity;
			m_parts = _parts;
		}
	}
	public class FitnessData {
		public ResearchLabRewardsController.Rarity m_rarity;
		public float m_maxFitnessBonus;
		public float m_minFitnessBonus;
		public int m_partLifetime;
		public float m_duplicateAgeRecovery;
		public string m_rarityDisplayKey;
		public FitnessData(ResearchLabRewardsController.Rarity _rarity, float _maxFitnessBonus, float _minFitnessBonus, int _partLifetime, float _duplicateAgeRecovery, string _displayKey){
			m_rarity = _rarity;
			m_maxFitnessBonus = _maxFitnessBonus;
			m_minFitnessBonus = _minFitnessBonus;
			m_partLifetime = _partLifetime;
			m_duplicateAgeRecovery = _duplicateAgeRecovery;
			m_rarityDisplayKey = _displayKey;
		}
	}

	public static ReactItemInfo.ItemInfoBase GetBuyItemByNameOrPrefab(string _part){
		var item = ItemDetailsHelper.GetBuyItemByName(_part);
		if (item != null) return item;
		return ItemDetailsHelper.GetBuyItemByPrefabName(_part);
	}

	// ----------------------- Data for GUI Ref ------------------------ //
	public class RewardData {
		public string m_id;
		public string m_name;
		public string m_productLine;
		public ResearchLabRewardsController.Rarity m_rarity;
		public ResearchLabRewardsController.Category m_category;
		public RewardData(string _id, string _productLine, ResearchLabRewardsController.Category _category, ResearchLabRewardsController.Rarity _rarity = Rarity.Common){
			m_id = _id;
			m_name = "";

			var data = NGBlockInfo.GetInfo(_id);
			if (data != null) m_name = data.m_prefabName;
			
			m_productLine = _productLine;
			m_rarity = _rarity;
			m_category = _category;
		}
	}
	// ----------------------- Data for random reward selection ------------------------ //
	public class DropChanceData {
		
		public ResearchLabRewardsController.Rarity m_rarity;
		public int m_dropChance = 100;
		public int m_pityTimer = -1;

		public DropChanceData(ResearchLabRewardsController.Rarity _rarity, int _dropChance, int _pityTimer = -1){
			m_rarity = _rarity;
			m_dropChance = _dropChance;
			m_pityTimer = _pityTimer;
		}
		public static List<DropChanceData> LoadInfo()
		{
			//This will download (or read from cache) all records on Knack NB can be called without the PostImportAReacord.
			s_dropChange = NGKnack.ImportKnackInto<DropChanceData>();
			return s_dropChange;
		}

		private static List<DropChanceData> s_dropChange;
	}
	// ----------------------- Pity timer and counters ------------------------ //
	public class PityTimer {
		public int m_sinceRareCounter = 0;
		public int m_sinceEpicCounter = 0;
		public int m_sinceLegendaryCounter = 0;
		public void UpdateCounters(Rarity _rarity){
			switch(_rarity){
				case Rarity.Rare:
					m_sinceRareCounter = 0;
					m_sinceEpicCounter++;
					m_sinceLegendaryCounter++;
					break;
				case Rarity.Epic:
					m_sinceEpicCounter = 0;
					m_sinceRareCounter++;
					m_sinceLegendaryCounter++;
					break;
				case Rarity.Legendary:
					m_sinceLegendaryCounter = 0;
					m_sinceRareCounter++;
					m_sinceEpicCounter++;
					break;
				default:
					m_sinceRareCounter++;
					m_sinceEpicCounter++;
					m_sinceLegendaryCounter++;
					break;
			
			}
		}
	}
	static Dictionary<string, PityTimer> m_pityTimers = new Dictionary<string, PityTimer>();


	public enum Rarity{
		Common = 0,
		Rare,
		Epic,
		Legendary,
		Premium,
		Length
	}

	static DropChanceData[] m_dropChanceDatas = new DropChanceData[System.Enum.GetValues(typeof(Rarity)).Length];
	static FitnessData[] m_fitnessDatas = new FitnessData[System.Enum.GetValues(typeof(Rarity)).Length];


	public enum Category {
		PRODUCT_PART,
		PRODUCT_DECO,
		PRODUCT_LINE,
		AVATAR_PART,
		TOWN_DECO
	}
	// product part storage productline, ids
	static RewardTable[] m_productParts = new RewardTable[]
	{
		new RewardTable(), // common
		new RewardTable(), // rare
		new RewardTable(), // epic 
		new RewardTable()// legendary
	};
	static RewardTable[] m_decoParts = new RewardTable[]
	{
		new RewardTable(), // common
		new RewardTable(), // rare
		new RewardTable(), // epic 
		new RewardTable()// legendary
	};

	static RewardTable[] m_townDecoParts = new RewardTable[]
	{
		new RewardTable(), // common
		new RewardTable(), // rare
		new RewardTable(), // epic 
		new RewardTable()// legendary
	};
	
	static RewardTable[] m_buildingParts = new RewardTable[]
	{
		new RewardTable(), // common
		new RewardTable(), // rare
		new RewardTable(), // epic 
		new RewardTable()// legendary
	};

	static Dictionary<string, ProductPartsData> m_starterProductPartsData = new Dictionary<string, ProductPartsData>();

	// Use for unlocking all the parts when unlocking a product line
	static Dictionary<string, List<string>> m_starterProductLineParts = new Dictionary<string, List<string>>();
	
	// Product Pack
	static Dictionary<string, List<string>> m_starterPackDecorations = new Dictionary<string, List<string>>();
	

	public static event System.Action OnProductLineAwarded;

	// ----------------------- csv imports ------------------------ //
	public static void AddToRewardDropTable(string _rarity, int _dropChance, int _pityTimer = -1){
		// Research-DropTable
		if(System.Enum.TryParse<Rarity>(_rarity, out var rarity)){
			m_dropChanceDatas[(int)rarity] = new DropChanceData(rarity, _dropChance, _pityTimer);
		} else {
			Debug.LogError("AddToRewardDropTable - Research-DropTable rarity not matched to ResearchLabRewardsController.Rarity - " + _rarity);
		}
	}

	public static void AddToFitnessTable(string _rarity, float _maxFitnessBonus, float _minFitnessBonus, int _partLifetime, float _partLifetimeRecoveredPerDuplicate, string _rarityKey){
		// Research-DropTable
		if(System.Enum.TryParse<Rarity>(_rarity, out var rarity)){
			m_fitnessDatas[(int)rarity] = new FitnessData(rarity, _maxFitnessBonus, _minFitnessBonus, _partLifetime, _partLifetimeRecoveredPerDuplicate, _rarityKey);
		} else {
			Debug.LogError("AddToFitnessTable - Research-DropTable rarity not matched to ResearchLabRewardsController.Rarity - " + _rarity);
		}
	}
	
	// Knack Imported Data from NGBlocks
	public static void AddBlockInfos()
	{
		foreach (var block in NGBlockInfo.s_allBlocks)
		{
			
			if (!System.Enum.TryParse<Rarity>(ConditionRarityString(block.Value.m_rarity), out var rarity))
			{
				Debug.LogError($"Research Lab :: AddBlockInfos - rarity not matched in {block.Value.m_prefabName} to ResearchLabRewardsController.Rarity - {block.Value.m_rarity}");
				continue;
			}
			if(rarity == Rarity.Premium)
				continue;
			
			var lines = block.Value.Lines;
			if (block.Value.Type == NGBlockInfo.BlockType.Product && string.IsNullOrEmpty(lines))
			{
				Debug.LogError($"No product Line assigned to block {block.Value.m_prefabName}");
				continue;
			}
			
			if (block.Value.Type == NGBlockInfo.BlockType.Product)
			{
				// add product line content
				var partsDict = (block.Value.m_starterPack) ? m_starterProductLineParts :  m_productParts[(int)rarity];

				foreach (var line in block.Value.m_productInfos)
				{
					if(!partsDict.TryGetValue(line.m_prefabName, out var linePartsList)){
						linePartsList = new List<string>();
						partsDict[line.m_prefabName] = linePartsList;
					}
					
					linePartsList.Add(block.Value.m_prefabName);
					// add pity timer
					if(!m_pityTimers.ContainsKey(line.m_prefabName))
						m_pityTimers.Add(line.m_prefabName, new PityTimer());
				}

			} 
			/*else if (block.Value.Type == NGBlockInfo.BlockType.Building && block.Value.m_buildingInfos != null)
			{
				//m_buildingParts
				var partsDict = m_buildingParts[(int)rarity];
				foreach (var line in block.Value.m_buildingInfos)
				{
					if(!partsDict.TryGetValue(line.m_prefabName, out var linePartsList)){
						linePartsList = new List<string>();
						partsDict[line.m_prefabName] = linePartsList;
					}
					
					linePartsList.Add(block.Value.m_prefabName);
					// add pity timer
					if(!m_pityTimers.ContainsKey(line.m_prefabName))
						m_pityTimers.Add(line.m_prefabName, new PityTimer());
				}
			}*/
			//TODO
			/*
			else if(block.Value.Type == NGProductManager.NGBlockInfo.BlockType.Decorations)
			{
				 // add decoration content
				 var townDecoPartsDict = m_townDecoParts[(int)rarity];
				 if(!townDecoPartsDict.TryGetValue(line, out var newPartsList)){
					 newPartsList = new List<string>();
					 townDecoPartsDict[line] = newPartsList;
				 }

				 newPartsList.Add(block.Value.m_prefabName);
				 if (block.Value.m_starterPack)
				 {
					 // unlock the town decoration by default
					 GameManager.AddUnlock(block.Value.m_prefabName, -1);
				 }
			}
			*/
			//TODO
			// avatar
		}
		
	}

	private static void AddDecorationPack(ProductPacks _pack)
	{
		
		if (!System.Enum.TryParse<Rarity>(ConditionRarityString(_pack.m_rarity), out var rarity))
		{
			Debug.LogError($"Research Lab :: AddDecorationPack - rarity not matched to ResearchLabRewardsController.Rarity - {_pack.m_rarity}");
			return;
		}
		if(rarity == Rarity.Premium)
			return;
		var lines = _pack.m_productInfos;
		if (lines == null || lines.Count < 1)
		{
			Debug.LogError($"No product Line assigned to ProductPack {_pack.m_packName}");
			return;
		}
		foreach (var line in lines)
		{
			var partsDict = (_pack.m_starterPack) ? m_starterPackDecorations :  m_decoParts[(int)rarity];
			if(!partsDict.TryGetValue(line.m_prefabName, out var linePartsList)){
				linePartsList = new List<string>();
				partsDict[line.m_prefabName] = linePartsList;
			}
			foreach(var paintData in _pack.m_paintDatas)
				linePartsList.Add("paint:" + paintData.m_name);
			foreach(var patternData in _pack.m_patternDatas)
				linePartsList.Add("pattern:" + patternData.m_name);
			foreach(var stickerData in _pack.m_stickerDatas)
				linePartsList.Add("sticker:" + stickerData.m_name);
			// add pity timer
			// if(!m_pityTimers.ContainsKey(line.m_prefabName))
			// 	m_pityTimers.Add(line.m_prefabName, new PityTimer());
		}
	}

	public static void AddPacks()
	{
		foreach (var pack in ProductPacks.s_packs)
		{
			// design table decos
			if (pack.HasDecorations)
			{
				AddDecorationPack(pack);
			}
		}
		
	} 

	private static string ConditionRarityString(string _value)
	{
		if (_value == "None")
			return "Common";
		return _value;
	}

	// OLD CSV WAY
	/*
	public static void AddProductLineContent(string _productLine, string _rarity, string _productParts){
		// example :: ResearchCategoriesContent-Jackets
		bool isProductLine = ProductLineManager.IsAProductLine(_productLine);

		string[] parts = _productParts.Split('|');// need to split _productParts by "|"
		if(System.Enum.TryParse<ResearchLabRewardsController.Rarity>(_rarity, out var rarity)){
			// sort into the rarity dictionaries
			if(isProductLine){
				ProcessProductlineData(_productLine, rarity, parts);
			} else {
				// TownDecoration or SpecialTownDecorations
				ProcessNonProductlineData(_productLine, rarity, parts);
			}
		} else {
			// check if product line - could be DefaultTownDecorations or SpecialTownDecorations
			// we have the starter pack - use when unlocking product lines
			SetupStarterPacks(_productLine, parts);
			m_starterProductPartsData.Add(_productLine, new ProductPartsData(_productLine, 0, parts));
		}

		// add pity timer
		if(!m_pityTimers.ContainsKey(_productLine))
			m_pityTimers.Add(_productLine, new PityTimer());
	}

	
	// OLD CSV WAY
	private static void ProcessNonProductlineData(string _line, ResearchLabRewardsController.Rarity _rarity, string[] _parts){
		// deal with non product line data
		if(_line.Contains("TownDecorations")){
			var townDecoPartsDict = m_townDecoParts[(int)_rarity];
			if(!townDecoPartsDict.TryGetValue(_line, out var newPartsList)){
				newPartsList = new List<string>();
				townDecoPartsDict[_line] = newPartsList;
			}

			foreach(var part in _parts){
				newPartsList.Add(part);
			}
		}
		// else other lines - avatar etc
		
	}
	// OLD CSV WAY
	private static void SetupStarterPacks(string _productLine, string[] _productParts){
		if(!m_starterProductLineParts.TryGetValue(_productLine, out var linePartsList)){
			linePartsList = new List<string>();
			m_starterProductLineParts[_productLine] = linePartsList;
		}

		foreach(var part in _productParts)
			linePartsList.Add(part);
		
	}
	// OLD CSV WAY
	private static void ProcessProductlineData(string _productLine, ResearchLabRewardsController.Rarity _rarity, string[] _parts){
		// add the entries for a product line if not added yet
		var partsDict = m_productParts[(int)_rarity];
		if(!partsDict.TryGetValue(_productLine, out var linePartsList)){
			linePartsList = new List<string>();
			partsDict[_productLine] = linePartsList;
		}
		
		foreach(var part in _parts)
			linePartsList.Add(part);
	}
	*/

	// -------------------- internal drop rate -------------------- //

	/*private static RewardData RewardDropBuildingPart(string _buildingType)
	{
		if (!m_pityTimers.TryGetValue(_buildingType, out var pity))
		{
			Debug.LogError($"Trying to update pity timers Product Line {_buildingType} does not exist");
			return null;
		}
		// process the pity timer functionality
		var rarity = SetRewardRarity(pity);
		pity.UpdateCounters(rarity);
		// select the reward data
		return GetRandomBuidingPartData(rarity, _buildingType);
	}*/

	private static RewardData RewardDropProductPart(string _productLine, params Category[] _categories)
	{
		if (!m_pityTimers.TryGetValue(_productLine, out var pity))
		{
			Debug.LogError($"Trying to update pity timers Product Line {_productLine} does not exist");
			return null;
		}
		// process the pity timer functionality
		var rarity = SetRewardRarity(pity);
		pity.UpdateCounters(rarity);
		// select the reward data
		return GetRandomPartData(rarity, _productLine, _categories);
	}

	struct RewardCategoryRoll {
		public List<string> rewardList;
		public Category category;
		public RewardCategoryRoll(List<string> _name, Category _category){
			rewardList = _name;
			category = _category;
		}
	}

	private static RewardData GetRandomPartData(Rarity _rarity, string _productLine = "locked", params Category[] _categories){
		List<RewardCategoryRoll> rewardCategories = new List<RewardCategoryRoll>();
		if(_productLine == "locked"){
			int index = Mathf.FloorToInt(Random.value * NGProductInfo.s_allProducts.Count);
			List<string> keyList = new List<string>(NGProductInfo.s_allProducts.Keys);
			while (_productLine == "locked"){
				/*if(!NGProductInfo.DoesPlayerOwnProductLine(keyList[index])){
					_productLine = keyList[index];
				}*/
				index++;
				if(index >= NGProductInfo.s_allProducts.Count)
					index = 0;
			}
		}
		foreach(var cat in _categories){
			switch(cat)
			{
				case Category.PRODUCT_DECO:
				if(!m_decoParts[(int)_rarity].TryGetValue(_productLine, out var decoPartsList)){
					Debug.LogError($"Product line not found in the list {_productLine} with rarity {_rarity}");
					continue;
				}
					//rewardsNameList.AddRange(decoPartsList);
					if (decoPartsList != null && decoPartsList.Count > 0)
						rewardCategories.Add(new RewardCategoryRoll(decoPartsList, cat));
					if(_rarity == Rarity.Common && m_starterPackDecorations.ContainsKey(_productLine))
						rewardCategories.Add(new RewardCategoryRoll(m_starterPackDecorations[_productLine], cat));
					break;
				case Category.PRODUCT_PART:
					if(!m_productParts[(int)_rarity].TryGetValue(_productLine, out var linePartsList)){
						Debug.LogError($"Product line not found in the list {_productLine} with rarity {_rarity}");
					}
					//rewardsNameList.AddRange(linePartsList);
					if (linePartsList != null && linePartsList.Count > 0)
						rewardCategories.Add(new RewardCategoryRoll(linePartsList, cat));
					// check for starter parts too
					if(_rarity == Rarity.Common && m_starterProductLineParts.ContainsKey(_productLine))
					{
						rewardCategories.Add(new RewardCategoryRoll(m_starterProductLineParts[_productLine], cat));
					}
					break;
				case Category.TOWN_DECO:
					if(!m_townDecoParts[(int)_rarity].TryGetValue("DefaultTownDecorations", out var townDecoPartsList)){
						Debug.LogError($"Product line not found in the list DefaultTownDecorations");
					}
					//rewardsNameList.AddRange(townDecoPartsList);
					if (townDecoPartsList != null && townDecoPartsList.Count > 0)
						rewardCategories.Add(new RewardCategoryRoll(townDecoPartsList, cat));
					break;
			}
		}

		// randomly grab an item name from the rarity pools
		if (rewardCategories.Count == 0) {
			var cat = _categories.Length == 1 ? _categories[0].ToString() : (_categories.Length == 0 ? "<none supplied>" : _categories[0].ToString() + " and others");
			Debug.LogError($"No rewards found for type {cat} rarity {_rarity}");
			return null;
		}
		int randomCatIndex = Random.Range(0, rewardCategories.Count);
		var rewardCategory = rewardCategories[randomCatIndex];
		int randomRewardIndex = Random.Range(0, rewardCategory.rewardList.Count);
		var reward = rewardCategory.rewardList[randomRewardIndex];
		// set the data using the name string
		RewardData data = new RewardData(reward, _productLine, rewardCategory.category, _rarity);
		if (data.m_name == "")
			return null;

		return data;
	}

	/*public static RewardData GetRandomBuidingPartData(string _buildingType)
	{
		List<RewardCategoryRoll> rewardCategories = new List<RewardCategoryRoll>();
		List<NGBlockInfo> available = new List<NGBlockInfo>();

		foreach (var kvp in NGBlockInfo.s_allBlocks)
		{
			var p = kvp.Key;
			var blk = kvp.Value;
			if (blk.TypeIDs.Contains(_buildingType))
			{
				available.Add(blk);
			}
		}

		// randomly grab an item name from the rarity pools
		if (available.Count == 0)
		{
			Debug.LogError($"No building parts found for type {_buildingType}");
			return null;
		}

		int randomRewardIndex = Random.Range(0, available.Count);
		var reward = available[randomRewardIndex];
		// set the data using the name string
		RewardData data = new RewardData(reward.m_prefabName, _buildingType, Category.PRODUCT_PART, Rarity.Common);
		if (data.m_name == "")
			return null;

		return data;
	}*/

	/*public static RewardData GetRandomBuidingPartData(Rarity _rarity, string _buildingType)
	{
		List<RewardCategoryRoll> rewardCategories = new List<RewardCategoryRoll>();
		List<NGBlockInfo> available = new List<NGBlockInfo>();

		foreach (var kvp in NGBlockInfo.s_allBlocks)
		{
			var p = kvp.Key;
			var blk = kvp.Value;
			if (blk.TypeIDs.Contains(_buildingType))
			{
				if((Rarity)blk.Rarity == _rarity)
					available.Add(blk);
			}
		}

		// randomly grab an item name from the rarity pools
		if (available.Count == 0)
		{
			Debug.LogError($"No building parts found for type {_buildingType} rarity {_rarity}");
			return null;
		}

		int randomRewardIndex = Random.Range(0, available.Count);
		var reward = available[randomRewardIndex];
		// set the data using the name string
		RewardData data = new RewardData(reward.m_prefabName, _buildingType, Category.PRODUCT_PART, _rarity);
		if (data.m_name == "")
			return null;

		return data;
	}*/


	private static float offset = 0f;

	public static void DisableByName(GameObject _o, string _name) {
		var obj = _o.transform.FindChildRecursiveByName(_name);
		if (obj != null) obj.gameObject.SetActive(false);
	}
	public static void GetRewardSprite(string _partID, CaptureObjectImage.Use _use, UIImage uiImage){
		var holder = new GameObject("block_holder_" + _partID);
		holder.transform.position += new Vector3(offset, 0f, 0f);
		//var designPart = new ProductDesignPart (_partID, false);
		GameObject blockGO = null;
		float rotation = 0f;

		if (Block.IsValidUID(_partID)) {
			Block.Create(_partID, holder.transform, null, (_o) => {
				if (_o != null) {
					uiImage.sprite = CaptureObjectImage.Me.Capture(_o, _use);
				}
				GameObject.Destroy(holder);
			});
		} /*TODO else {
			// not a part, town decoration or salesperson
			var bi = GetBuyItemByNameOrPrefab(_partID);

			if(bi.m_category == ReactItemInfo.ItemInfoBase.CATEGORY.TownDecoration)
			{
				var sprite = SpriteAtlasMapLoader.LoadClonedSprite("_GUI/_HUD/Decorations_Icons/Decoration_" + bi.Name.Replace("[DO] ", ""));
				uiImage.sprite = sprite;
			}
			else if( bi.m_category == ReactItemInfo.ItemInfoBase.CATEGORY.Salesmen )
			{
				var sprite = SpriteAtlasMapLoader.LoadClonedSprite("_GUI/_HUD/Salespeople/" + bi.Name );
				uiImage.sprite = sprite;
			} else if (bi.m_category == ReactItemInfo.ItemInfoBase.CATEGORY.Decoration) {

				ReactDecoration rdPrefab = (ReactDecoration)bi.Prefab;
				if(rdPrefab.DecorationType == DecorationType.Sticker) {
					var ids = DecoratingManagerBase.GetAllIDsInPack<StickeringManager.StickerData>(bi.m_name);
					var allStickers = StickeringManager.GetStickerDataFromCSV();
					var stickersInThisPack = allStickers.FindAll(
							(x) => { return ids.Contains(x.ID); }
					);

					int randomStickerIndex = Random.Range(0, stickersInThisPack.Count);
					string stickerPath = stickersInThisPack[randomStickerIndex].StickerPath;

					Sprite sprite = StickerPageScript.LoadStickerFromFile(stickerPath);
					uiImage.sprite = sprite;
				} else {
					var obj = GameObject.Instantiate(bi.Prefab, holder.transform, false);
					IUnlockVisual reactDecoration = obj.GetComponent<IUnlockVisual>();
					reactDecoration.Setup(bi);

					blockGO = obj.gameObject;
				}

			}
			else if (bi != null && bi.m_prefab != null) {
				
				var obj = GameObject.Instantiate(bi.m_prefab, holder.transform);
				if (obj != null) {
					blockGO = obj.gameObject;
					DisableByName(blockGO, "BlobShadow");
					DisableByName(blockGO, "RadiusVisualsQuad");
					DisableByName(blockGO, "ObjectGUI");
					rotation = 135f;
				}
			}
		}*/
	}

	public static int s_pityRewardCount = 0;
	private static Rarity SetRewardRarity(PityTimer _pity){
		// check pity counter first
		if(_pity.m_sinceLegendaryCounter >= m_dropChanceDatas[(int)Rarity.Legendary].m_pityTimer){
			s_pityRewardCount++;
			return Rarity.Legendary;
		}

		if(_pity.m_sinceEpicCounter >= m_dropChanceDatas[(int)Rarity.Epic].m_pityTimer){
			s_pityRewardCount++;
			return Rarity.Epic;
		}

		if(_pity.m_sinceRareCounter >= m_dropChanceDatas[(int)Rarity.Rare].m_pityTimer){
			s_pityRewardCount++;
			return Rarity.Rare;
		}
			
		
		// roll the random chance dice
		int random = Mathf.RoundToInt(Random.value * 100f);
		if(random <= m_dropChanceDatas[(int)Rarity.Legendary].m_dropChance)
			return Rarity.Legendary;
		
		if(random <= m_dropChanceDatas[(int)Rarity.Epic].m_dropChance)
			return Rarity.Epic;
		
		if(random <= m_dropChanceDatas[(int)Rarity.Rare].m_dropChance)
			return Rarity.Rare;

		return Rarity.Common;
	}
	// ---------------------- external calls ---------------------- //
	// Spend Ideas From Unlocked Product Line
	/*public static RewardData[] DoBuildingResearch(int _numIdeas, string _buildingType)
	{
		RewardData[] rewards = new RewardData[_numIdeas];

		int i = 0;
		int error = 0;
		do
		{
			var reward = RewardDropBuildingPart(_buildingType);
			if (reward != null)
			{
				rewards[i] = reward;
				i++;
			}
			error++;
		} while (_numIdeas > i && error < 99);

		return rewards;
	}*/

	// Spend Ideas From Unlocked Product Line
	public static RewardData[] DoProductLineResearch(int _numIdeas, string _productLine)
	{
		s_pityRewardCount = 0;
		RewardData[] rewards = new RewardData[_numIdeas];

		int i = 0;
		int error = 0;
		do
		{
			var reward = RewardDropProductPart(_productLine, Category.PRODUCT_PART, Category.PRODUCT_DECO);
			if (reward != null)
			{
				rewards[i] = reward;
				i++;
			}
			error++;
		} while (_numIdeas > i && error < 99);

		return rewards;
	}
	// Spend Ideas From Fundamental Research Banner
	public static string s_fundamentalResearchProductLine = "DefaultTownDecorations";// this needs to be updatede to handle the different part types
	public static string s_fundamentalResearchProductTitle = "Town";
	public static RewardData[] DoFundamentalResearch(int _numIdeas){
		s_pityRewardCount = 0;
		// NB: pool of all types - so far only town decos in
		RewardData[] rewards = new RewardData[_numIdeas];
		for(int i = 0; i < _numIdeas; i++){
			// set product line
			string line = s_fundamentalResearchProductLine;
			var reward = RewardDropProductPart(line, Category.TOWN_DECO);
			rewards[i] = reward;
		}

		return rewards;
		// return avatar parts
		// town deco
		// dt deco
		// epic product line
		// legendary product line

	}
	// Spend Ideas From Industrial Research Banner
	public static void DoIndustrialResearch(int _numIdeas){
		// improves probability increase - ask miguel
		// has product lines that aren't always available - need to sort - ask miguel
		// return avatar parts
		// town deco
		// dt deco
		// epic product line
		// legendary product line

	}
	// ----------------------------------------------------------- //
	//Collect the rewards clicked - convert reward data into rewards
	public static void CollectRewards(RewardData[] _rewards){
		var batchUnlocks=new Dictionary<string, int>();
		foreach(var reward in _rewards){
			if (reward != null) {
				GiveRewardPart(reward.m_id, reward.m_productLine, batchUnlocks);
			}
		}
		GameManager.AddUnlocks(batchUnlocks); // server optimization
	}

    public static bool IsUnlocked(string _partID)
    {
		var buyItem = GetBuyItemByNameOrPrefab(_partID);
        if (buyItem != null)
        {
            return buyItem.IsActive;
        }

        return false;
    }

	public static void GiveRewardPart(string _partID, string _productLine = "", Dictionary<string, int> batchUnlocks=null){
		var buyItem = GetBuyItemByNameOrPrefab(_partID);
		if(buyItem != null){
			if(buyItem.IsActive){
				// for block
				if(buyItem.m_category == ReactItemInfo.ItemInfoBase.CATEGORY.Decoration)
				{
					DesignFitness.ResetAgeForDecoration(_partID);
				}
				else
				{
					DesignFitness.ResetAgeForBlock(_partID);
				}
				
			} else {
				buyItem.OnUnlock();
				var itemIds = NGPlayer.GetUnlockedBuyItemIds(buyItem);
				NGPlayer.Me.AddUnlockedBuyItem(itemIds, batchUnlocks);
			}
		} else {
			if(batchUnlocks != null)
				batchUnlocks[_partID] = -1;
			else
				GameManager.AddUnlock(_partID);
			// check for product pack
			// var found = ProductPacks.s_packs.Find(o => o.m_packName.Equals(_partID));
			// if (found != null)
			// {
			// 	Debug.Log($"We have a product pack {_partID}"); // not showing?
			// 	if (found.m_paintDatas != null)
			// 	{
			// 		foreach (var paint in found.m_paintDatas)
			// 			GiveRewardPart(paint.m_name);
			// 	}
			//
			// 	if (found.m_stickerDatas != null)
			// 	{
			// 		foreach (var sticker in found.m_stickerDatas)
			// 		{
			// 			GiveRewardPart(sticker.m_name);
			// 		}
			// 	}
			//
			// 	if (found.m_patternDatas != null)
			// 	{
			// 		foreach (var pattern in found.m_patternDatas)
			// 		{
			// 			GiveRewardPart(pattern.m_name);
			// 		}
			// 	}
			//}
			//Debug.LogError($"GiveRewardPart :: {_partID} Is not a buy item. Check part id string is correct.");
			
			
		}
	}

	public static bool IsDuplicate(string _partID){
		var buyItem = GetBuyItemByNameOrPrefab(_partID);
		if(buyItem != null){
			return buyItem.IsActive;
		} else {
			Debug.LogError($"GiveRewardPart :: {_partID} Is not a buy item. Check part id string is correct.");
		}
		return false;
	}
	// After watching an ad you get an extra reward
	public static RewardData GetExtraReward(string _productLine = "unset"){
		// get random product line
		int index = Mathf.FloorToInt(Random.value * NGProductInfo.s_allProducts.Count);
		List<string> keyList = new List<string>(NGProductInfo.s_allProducts.Keys);
		while (_productLine == "unset" || !NGProductInfo.IsAProductLine(_productLine)){
			
			/*if(NGProductInfo.DoesPlayerOwnProductLine(keyList[index])){
				_productLine = keyList[index];
			}*/
			index++;
			if(index >= NGProductInfo.s_allProducts.Count)
				index = 0;
		}
		var reward = DoProductLineResearch(1, _productLine);
		//CollectRewards(reward);
		return reward[0];
	}
	// ----------------------------------------------------------- //
	public static void UnlockProductLine(string _productLine)
	{
		if (_productLine.Equals("Avatars"))
		{
			UnlockAvatarProductLine();
			return;
		}

		if(NGProductInfo.IsAProductLine(_productLine)){
			/*if(!NGProductInfo.DoesPlayerOwnProductLine(_productLine)){
				NGProductInfo.UnlockProductLine(_productLine, false);
				OnProductLineAwarded?.Invoke();
				// unlock all the starter parts
				var batchUnlocks=new Dictionary<string, int>();
				var unlocks = "";
				if (m_starterProductLineParts.ContainsKey(_productLine))
				{
					foreach (var id in m_starterProductLineParts[_productLine])
					{
						GiveRewardPart(id, batchUnlocks:batchUnlocks);
						unlocks += $"{id}+{(GameManager.IsConsumableUnlock(id) ? 1 : -1)}~";
					}
				}
				if (m_starterPackDecorations.ContainsKey(_productLine))
				{
					foreach (var id in m_starterPackDecorations[_productLine])
					{
						var split = id.Split(':');
						GiveRewardPart((split.Length == 2) ? split[1] : id, batchUnlocks:batchUnlocks);
						unlocks += $"{((split.Length == 2) ? split[1] : id)}+{(GameManager.IsConsumableUnlock((split.Length == 2) ? split[1] : id) ? 1 : -1)}~";
					}
				}
				//var m_productLineData = ProductLineManager.GetProductLineData(_productLine);
				GameManager.AddUnlocks(batchUnlocks); // server optimization: send all unlocks in one batch

			} else {
				Debug.LogError($"Player already owns this product line :: {_productLine}");
			}*/
		} else {
			Debug.LogError($"Not a product line - check string for mistakes :: {_productLine}");
		}
	}

	public static RewardData[] ProductLineStarterPackRewards(string _productLine){
		if(m_starterProductLineParts.ContainsKey(_productLine) && NGProductInfo.IsAProductLine(_productLine)){
			var partIDs = m_starterProductLineParts[_productLine];
			RewardData[] rewards = new RewardData[partIDs.Count];
			for(int i = 0; i < rewards.Length; i++){
				var reward = new RewardData(partIDs[i], _productLine, ResearchLabRewardsController.Category.PRODUCT_PART);
				rewards[i] = reward;
			}
			return rewards;
		} else {
			Debug.LogError($"Not a product line - check string for mistakes :: {_productLine}");
		}
		return null;
	}

	public static Color RarityColour(Rarity _rarity){
		return GlobalData.Me.m_rarityColours[(int)_rarity];
	}

	public static void UnlockAvatarProductLine()
	{
		var batchUnlocks=new Dictionary<string, int>();
		var unlocks = "";
		foreach (var id in m_starterProductLineParts["Avatars"]){
			GiveRewardPart(id, batchUnlocks:batchUnlocks);
			unlocks += $"{id}+{-1}~";
		}
		GameManager.AddUnlocks(batchUnlocks); // server optimization: send all unlocks in one batch
	}

	// ---------------------- Fitness calls ---------------------- //
	public static float MaxFitnessBonus(string _id, Category _category){
		Rarity rarity = GetRarity(_id, _category);
		return m_fitnessDatas[(int)rarity].m_maxFitnessBonus;
	}
	public static float MinFitnessBonus(string _id, Category _category){
		Rarity rarity = GetRarity(_id, _category);
		return m_fitnessDatas[(int)rarity].m_minFitnessBonus;
	}
	public static float PartLifetime(string _id, Category _category){
		Rarity rarity = GetRarity(_id, _category);
		return m_fitnessDatas[(int)rarity].m_partLifetime;
	}

	public static float DuplicateAgeRecovery(string _id, Category _category){
		Rarity rarity = GetRarity(_id, _category);
		return m_fitnessDatas[(int)rarity].m_duplicateAgeRecovery;
	}

	public static string RarityDisplayName(string _id, Category _category){
		Rarity rarity = GetRarity(_id, _category);
		return Localizer.GetFromCSV(m_fitnessDatas[(int)rarity].m_rarityDisplayKey);
	}

	public static Rarity GetRarity(string _id, Category _category)
	{
		Rarity rarity = Rarity.Common;
		switch(_category){
			case Category.PRODUCT_PART:
				rarity = GetBlockRarity(_id);
				break;
			case Category.PRODUCT_DECO:
				rarity = GetDecoPackRarity(_id);
				break;
		}
		return rarity;
	}
	
	private static Rarity GetBlockRarity(string _blockID){
		// all premium items are legendary
		if(BlockBalanceManager.GoldCost(_blockID) > 0) return Rarity.Legendary;

		for(int i = 0; i < m_productParts.Length; i++){
			var partDataDict = m_productParts[i];
			foreach(var rarityDict in partDataDict){
				if(rarityDict.Value.Contains(_blockID)){
					return (Rarity)i;
				}
			}
		}
		
		return Rarity.Common;
	}
	private static Rarity GetDecoPackRarity(string _packID){
		// done in packs
		for(int i = 0; i < m_decoParts.Length; i++){
			var partDataDict = m_decoParts[i];
			foreach(var rarityDict in partDataDict){
				if(rarityDict.Value.Contains(_packID)){
					return (Rarity)i;
				}
			}
		}

		#if UNITY_EDITOR
		bool isStarterPart = false;
		foreach(var productLine in m_starterProductPartsData) 
		{
			if(m_starterProductPartsData[productLine.Key].m_parts.Contains(_packID))
			{
				isStarterPart = true;
			}
		}
		if(!isStarterPart)
			Debug.LogError($"GetDecoPackRarity cannot find pack id {_packID}");
		#endif

		return Rarity.Common;
	}
	// ---------------------- Details calls ---------------------- //
	// get all productline items by rarity

	public static Dictionary<string, string> GetAllProductLineItemsByRarity(string _productLine, Rarity _rarity){
		Dictionary<string, string> result = new Dictionary<string, string>();
		if(!m_productParts[(int)_rarity].TryGetValue(_productLine, out var linePartsList)){
			Debug.LogError($"Product line not found in the list {_productLine}");
		}
		
		foreach(var part in linePartsList){
			ReactItemInfo.ItemInfoBase info = GetBuyItemByNameOrPrefab(part);
			if(info != null){
				if(!result.TryGetValue(info.m_popupTitle, out var _)){
					result.Add(info.m_popupTitle, info.m_category.ToString());
				}
			}
		}

		// add starter parts if rarity is common
		if(_rarity == Rarity.Common){
			if(m_starterProductLineParts.TryGetValue(_productLine, out var starterPartsList)){
				foreach(var part in starterPartsList){
					ReactItemInfo.ItemInfoBase info = GetBuyItemByNameOrPrefab(part);
					if(info != null){
						if(!result.TryGetValue(info.m_popupTitle, out var _)){
							result.Add(info.m_popupTitle, info.m_category.ToString());
						}
					}
				}
			}
		}
			
		if(!m_decoParts[(int)_rarity].TryGetValue(_productLine, out var decoPartsList)){
			Debug.LogError($"Product line not found in the list {_productLine}");
		}
		foreach(var deco in decoPartsList){
			ReactItemInfo.ItemInfoBase info = GetBuyItemByNameOrPrefab(deco);
			if(info != null){
				if(!result.TryGetValue(info.m_popupTitle, out var _)){
					result.Add(info.m_popupTitle, info.m_category.ToString());
				}
			}
		};

		return result;

	}

	// get all fundamental research items - town decos atm
	public static Dictionary<string, string> GetAllFundamentalItemsByRarity(Rarity _rarity){
		Dictionary<string, string> result = new Dictionary<string, string>();
		if(!m_townDecoParts[(int)_rarity].TryGetValue("DefaultTownDecorations", out var townDecoPartsList)){
			Debug.LogError($"Product line not found in the list DefaultTownDecorations");
		}

		foreach(var part in townDecoPartsList){
			ReactItemInfo.ItemInfoBase info = GetBuyItemByNameOrPrefab(part);
			if(info != null){
				if(!result.TryGetValue(info.m_popupTitle, out var _)){
					result.Add(info.m_popupTitle, info.m_category.ToString());
				}
			}
		}
		return result;
	}

	public static int GetDropChanceForRarity(Rarity _rarity)
	{
		return m_dropChanceDatas[(int)_rarity].m_dropChance;
	}

	public static int GetPityTimerForRarity(Rarity _rarity)
	{
		return m_dropChanceDatas[(int)_rarity].m_pityTimer;
	}

	public static string GetRarityDisplayText(Rarity _rarity)
	{
		// TODO these should be localised
		switch(_rarity)
		{
			case Rarity.Common: return Rarity.Common.ToString();
			case Rarity.Rare: return Rarity.Rare.ToString();
			case Rarity.Epic: return Rarity.Epic.ToString();
			case Rarity.Legendary: return Rarity.Legendary.ToString();
			default: return "";
		}
	}

	public static float GetDuplicateAgeRecoveryForRarity(Rarity _rarity)
	{
		return m_fitnessDatas[(int)_rarity].m_duplicateAgeRecovery;
	}


	// ---------------------- Spotlight calls ---------------------- //
	public static RewardData GetRandomSpotlightReward(Rarity _rarity, params Category[] _categories){
		return GetRandomPartData(_rarity, "locked", _categories);
	}
	// ---------------------- Debugging Tools ---------------------- //
	static DebugConsole.Command s_giveReward = new DebugConsole.Command("givereward", (_s) => {
		GiveRewardPart(_s);
	});
	
}
