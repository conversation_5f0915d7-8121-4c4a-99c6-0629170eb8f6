using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class BalloonSnapPoint : MonoBehaviour
{
	private NGCommanderBase m_building = null;
	public NGCommanderBase reactBuildingBase { get { return m_building; } }

	// Is the point in the process of being interacted with. Includes not-yet-but-will-be held.
	public bool IsBusy = false;

	void Start ()
	{
		m_building = this.GetComponentInParent<NGCommanderBase>();
	}
}
