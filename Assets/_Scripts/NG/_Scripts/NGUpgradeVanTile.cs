using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using TMPro;
using UnityEngine.UI;
using UnityEngine.EventSystems;

public class NGUpgradeVanTile : NGDirectionCardBase
{
    [System.Serializable]
    public class PowerIcons
    {
        public string m_PropertyName;
        public Sprite m_sprite;
    }
    public TMP_Text m_titleDetails;
    public Transform m_cardBasic;
    public Transform m_cardDetails;
    public Transform m_detailsLines;
    public Transform m_powerIconsHolder;
    public TMP_Text m_detailsLinePrefab;
    public Image m_powerIconImagePrefab;
    
    public List<PowerIcons> m_powerIcons;

    private int increase = 0;

    override public void ShowDetailsCard(NGCommanderBase _building, NGMovingObject _object)
    {
        m_cardBasic.gameObject.SetActive(false);
        m_cardDetails.gameObject.SetActive(true);
        var details = m_gift.GetUpgradeStrings(_building, $"<color=#{ColorUtility.ToHtmlStringRGB(m_positiveColour)}+>");
        m_detailsLines.DestroyChildren();

        var go = Instantiate(m_detailsLinePrefab.gameObject, m_detailsLines);
        var txt = go.GetComponentInChildren<TMP_Text>();
        txt.text = "Vehicle Capacity";
        
        go = Instantiate(m_detailsLinePrefab.gameObject, m_detailsLines);
        txt = go.GetComponentInChildren<TMP_Text>();
        txt.text = "+" + increase.ToString();
    }

    public override NGCardInfoHolder GetDescriptionText()
    {
        return new NGCardInfoHolder(m_gift.m_giftTitle, m_gift.m_description, Cost, 1, "Block");
    }

    override public void ShowBasicCard()
    {
        transform.localEulerAngles = new Vector3(0f, 0f, 0f);
        m_cardBasic.gameObject.SetActive(true);
        m_cardDetails.gameObject.SetActive(false);
        m_powerIconsHolder.DestroyChildren();
    }
    override protected void Activate(NGBusinessGift _gift, MAParserSection _maParserSection, INGDecisionCardHolder _fromHolder)
    {
        base.Activate(_gift, _maParserSection, _fromHolder);
        m_title.text = _gift.m_giftTitle;
        m_titleDetails.text = _gift.m_cardTitle;
        m_image.enabled = true;
        if (m_price != null)
        {
            if(Cost > 0)
                m_price.text = GlobalData.CurrencySymbol + Cost.ToString("F0");
            else
                m_price.transform.parent.gameObject.SetActive(false);
        }

        increase = (int)_gift.m_quantity;
        ShowBasicCard();
    }
}
