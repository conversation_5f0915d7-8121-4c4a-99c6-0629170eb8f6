using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using TMPro;

public class NGFillTile : NGDirectionCardBase
{
    override protected void Activate(NGBusinessGift _gift, MAParserSection _maParserSection, INGDecisionCardHolder _fromHolder)
    {
        base.Activate(_gift, _maParserSection, _fromHolder);
        m_title.text = _gift.m_giftTitle;
        m_image.enabled = true;
        m_image.sprite = _gift.GetSprite;
        if (m_price != null)
        {
            if (Cost > 0)
                m_price.text = GlobalData.CurrencySymbol + Cost.ToString("F0");
            else
                m_price.transform.parent.gameObject.SetActive(false);
        }

        ShowBasicCard();
    }
}
