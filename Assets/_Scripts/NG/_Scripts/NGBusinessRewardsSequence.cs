using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class NGSimpleDecisionCardHolder : MonoBehaviour, INGDecisionCardHolder
{
    public INGDecisionCardHolder.ECardView CardView { get { return INGDecisionCardHolder.ECardView.Simple; } }
    public bool DragIn3D { get { return true; } }
    public bool ShowOrderBoardGUIOnCardClick { get { return true; } }
    public void ToggleHiding(bool _hide, bool _isDueToClick = false) {}
    public bool IsHidden { get; }
    public void GiftReceived(IBusinessCard _card) {}
    public Transform GiftsHolder() { return transform; }
    public Transform Root { get; }
    public bool EnableCardOnUpwardDrag() { return true; }
    public Transform ParentForDraggingCard(NGDirectionCardBase _card) { return null; }
    public Transform GetParentHolder(NGDirectionCardBase _card) { return null; }
    public void OnStartCardDrag() {}
    public void OnEndCardDrag() {}
    public void OnCardClick() {}
}

public class NGBusinessRewardsSequence : MonoBehaviour
{
    public GameObject[] m_rewardCards;
    public TMPro.TextMeshProUGUI m_playerLevel;
    public TMPro.TextMeshProUGUI m_andOrOrText;
    float m_startTime;
    public event System.Action<List<NGBusinessDecisionCard>> OnComplete;
    private int m_sound;
    private bool m_dropped;
    private List<NGBusinessDecisionCard> m_cards = new List<NGBusinessDecisionCard>();
    
    public void Activate(List<NGBusinessGift> _gifts, MAParserSection _maParserSection, int _chooseCount = 0)
    {
        NGBusinessGiftsPanel.SetHidden(4f);
        for (int i = 0; i < m_rewardCards.Length; ++i)
        {
            m_rewardCards[i].transform.DestroyChildren();
            if (i < _gifts.Count)
            {
                var holder = m_rewardCards[i].GetComponent<NGSimpleDecisionCardHolder>();
                if (holder == null) holder = m_rewardCards[i].AddComponent<NGSimpleDecisionCardHolder>();
                var card = NGBusinessDecisionCard.Create(holder, _gifts[i], _maParserSection);
                m_cards.Add(card);
                card.DisableInteraction(true);
                card.Disable();

                DragCard dc = card.GetComponent<DragCard>();//stop the cards being dragged in this incarnation
                if (dc != null)
                    Destroy(dc);

                var rt = card.transform as RectTransform;                
                rt.pivot = rt.anchorMin = rt.anchorMax = Vector2.one * .5f;
                rt.anchoredPosition = Vector2.zero;
                m_rewardCards[i].transform.localScale = Vector3.zero;
            }
        }
        
        if(m_playerLevel)
            m_playerLevel.text = $"Level {NGBusinessDecisionManager.Me.PlayerLevel}";
        m_andOrOrText.text = _chooseCount > 0 ? "or" : "and";
        m_startTime = Time.time;
        m_dropped = false;

        if (GameManager.Me.IsOKToPlayUISound())
            AudioClipManager.Me.PlaySoundOld("PlaySound_HUDObjectiveComplete", GameManager.Me.transform);
    }
    
    public void OnCardClick() {}

    public Transform ParentForDraggingCard()
    {
        return this.transform;
    }
    
    void Update()
    {
        var animator = GetComponent<Animator>();
        var length = animator.GetCurrentAnimatorClipInfo(0)[0].clip.length;
        float t = Time.time - m_startTime;
        const float c_paddingTime = 3f;
        if (t >= length + c_paddingTime && GameManager.IsInCountryside)
        {
            if(NGBusinessGiftsPanel.Me != null) NGBusinessGiftsPanel.Me.AutoDeployCurrencyCards();
            
            for(int i = 0; i  < m_cards.Count; ++i)
            {
                var card = m_cards[i];
                card.transform.rotation = Quaternion.identity;
            }
            OnComplete?.Invoke(m_cards);            
            Destroy(gameObject);
        }
        if(t > 2.3f && m_dropped == false)
        {
            if (GameManager.Me.IsOKToPlayUISound())
                m_sound = AudioClipManager.Me.PlaySoundOld("PlaySound_Card_Drop", transform);
            m_dropped = true;
        }
    }
    
    public static NGBusinessRewardsSequence Create(List<NGBusinessGift> _gifts, MAParserSection _maParserSection, int _chooseCount = 0)
    {
        NGBusinessRewardsSequence prefab = null;
        switch (_gifts.Count)
        {
            case 1: prefab = NGBusinessDecisionManager.Me.m_NGBusinessRewardsSequence1CardPrefab; break;
            case 2: prefab = NGBusinessDecisionManager.Me.m_NGBusinessRewardsSequence2CardPrefab; break;
            case 3: prefab = NGBusinessDecisionManager.Me.m_NGBusinessRewardsSequence3CardPrefab; break;
            case 4: prefab = NGBusinessDecisionManager.Me.m_NGBusinessRewardsSequence4CardPrefab; break;
            default: prefab = NGBusinessDecisionManager.Me.m_NGBusinessRewardsSequence4OrMoreCardPrefab; break;
            case 0: Debug.LogError($"NGBusinessRewardsSequence doesn't support {_gifts.Count} rewards"); return null;
        }
        var seq = Instantiate(prefab, UIManager.Me.m_centralInfoPanelUI.transform);
        seq.Activate(_gifts, _maParserSection, _chooseCount);
        return seq;
    }
}
