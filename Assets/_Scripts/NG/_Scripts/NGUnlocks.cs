using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System.Reflection;
using UnityEngine.Rendering;

[System.Serializable]
public class NGUnlocks : MonoSingleton<NGUnlocks>
{
    const string  NoneEntry = "None";
    //Rarity

    List<string> m_unlocksDropdownList = new List<string>();
    [Dropdown("m_unlocksDropdownList")]
    public string ChangeUnlock;
    private static bool m_isLoaded = false;


    //    public string m_debugString;
    static bool m_townSatisfaction;
    static bool m_choices;
	static bool m_vault;
	static bool m_payBills;
    static bool m_trainStation;
    static bool m_filters = false;
    static int m_story = -1;
    static string m_characterName="Nobody";
    static bool m_paints;
    static bool m_stickers;
    static bool m_patterns;
    static bool m_multiDrop;
    static bool m_multiPickup;
    static bool m_awards;
    static bool m_designCompetition;
    static bool m_roads;
    static bool m_workerHealth;
    static bool m_entertainment;
    static bool m_workerCleanup;
    private static bool m_demolishBolders;
    private static bool m_workerAutoFindJob;
    private static bool m_culture;
    private static bool m_pickupOptions;
//   private static bool m_designBuildings;
    private static bool m_buildingNFTs;
    private static bool m_handPowerLightning;
    private static float m_handPowerLightningLevel = 1f;
    private static bool m_handPowerFireball;
    private static float m_handPowerFireballLevel = 1f;
    private static bool m_handPowerFlamethrower;
    private static float m_handPowerFlamethrowerLevel = 1f;
    void Update()
    {
        if (GameManager.Me.LoadComplete && m_unlocksDropdownList.Count == 0)
        {
            PopulateDropDown();
        }
  
        if (ChangeUnlock.Equals(NoneEntry, StringComparison.OrdinalIgnoreCase) == false && ChangeUnlock.IsNullOrWhiteSpace() == false)
        {
            ExecutePopup();
        }
    }

    void ExecutePopup()
    {
        if (ChangeUnlock.Contains('('))
        {
            var split = ChangeUnlock.Split('=', ':');
            Change(split[0]);
        }
        else
        {
            var split = ChangeUnlock.Split('=', ':');
            if (split.Length > 1)
            {
                var value = "true";
                if (split[1].Equals("true", StringComparison.OrdinalIgnoreCase))
                    value = "false";
                Change(split[0] + "=" + value);
            }
            else
            {
                Change(ChangeUnlock);
            }
        }

        PopulateDropDown();
    }
        
    void PopulateDropDown()
    {
        m_unlocksDropdownList = new List<string>();
        m_unlocksDropdownList.Add(NoneEntry);
        var debugStatus = ReactReflection.MakePropertyString<NGUnlocks>();
        foreach (var s in debugStatus.Split('\n'))
        {
            var split = s.Split("Boolean:");
            if(split.Length > 1)
                m_unlocksDropdownList.Add(split[1]);
        }
        
        ChangeUnlock = NoneEntry;
        m_unlocksDropdownList.Sort();
    }
    public static bool TownSatisfaction
    {
        get { return m_townSatisfaction; }
        set
        {
            // if (value)
            // {
            //     if (NGTownSatisfactionHUD.Me == null)
            //         NGTownSatisfactionHUD.Create();
            // }
            // else
            // {
            //     NGTownSatisfactionHUD.Me?.DestroyMe();
            // }
            m_townSatisfaction = value;
        }
    }
    // public static bool Choices 
    // { 
    //     get => m_choices; 
    //     set 
    //     { 
    //         m_choices = value; 
    //         //NGTownSatisfactionManager.Me.ActivateChoices(value); 
    //     } 
    // }
    public static bool MultiDrop { get => m_multiDrop; set => m_multiDrop = value; }
    public static bool MultiPickup { get => m_multiPickup; set => m_multiPickup = value; }

    public static bool Awards
    {
        get => m_awards;
        set
        {
            m_awards = value;
        }
    }

    public static bool DesignCompetition
    {
        get => m_designCompetition;
        set
        {
            m_designCompetition = value;
        }
    }

    public static bool Roads { get => m_roads; set => m_roads = value; }
    public static bool WorkerHealth { get => m_workerHealth; set => m_workerHealth = value; }
    public static bool Entertainment { get => m_entertainment; set => m_entertainment = value; }
    public static bool Culture { get => m_culture; set => m_culture = value; }
    public static bool Vault { 
		get => m_vault; 
		set	{ 
				/*if(value)
				{
					if(NGValutGUI.Me == null) 
						NGValutGUI.Create();
				}
				else if(NGValutGUI.Me)
				{
					NGValutGUI.Me.DestroyMe();
				}*/
				m_vault = value;
			}
		}
    public static bool PayBills { get => m_payBills; set => m_payBills = value; }
    public static bool TrainStation { get => m_trainStation; set => m_trainStation = value; }
    public static int Story { get => m_story; set => m_story = value; }
    public static bool Filters { get => m_filters; set => m_filters = value; }
    public static string CharacterName { get => m_characterName; set => m_characterName = value; }
    public static bool Paints { get => m_paints; set => m_paints = value; }
    public static bool Stickers { get => m_stickers; set => m_stickers = value; }
    public static bool Patterns { get => m_patterns; set => m_patterns = value; }
    public static bool PickupOptions { get => m_pickupOptions; set => m_pickupOptions = value; }
    public static bool WorkerCleanup { get => m_workerCleanup; set => m_workerCleanup = value; }
//    public static bool DesignBuildings { get => m_designBuildings; set => m_designBuildings = value; }
    public static bool WorkerAutoFindJob { get => m_workerAutoFindJob; set => m_workerAutoFindJob = value; }
    public static bool DemolishBolders { get => m_demolishBolders; set => m_demolishBolders = value; }
    public static bool BuildingNFTs { get => m_buildingNFTs; set => m_buildingNFTs = value; }
    public static bool HandPowerLightning { get => m_handPowerLightning; set => m_handPowerLightning = value; }
    public static float HandPowerLightningLevel { get => m_handPowerLightningLevel; set => m_handPowerLightningLevel = value; }

    public static void UnlockCharacter(string _what)
    {
        if (NGManager.Me.HouseList.Count == 0) return;
        var index = UnityEngine.Random.Range(0, NGManager.Me.HouseList.Count);
        UnlockACharacter(_what, NGManager.Me.HouseList[index]);
    }
    public static void UnlockACharacter(string _what, NGCommanderBase _where)
    {
        // var c = c_character.s_characters.Find(o => o.m_givenName.Equals(_what, StringComparison.OrdinalIgnoreCase));
        // if (c == null)
        // {
        //     Debug.LogError($"Trying to unlock character {_what} not found");
        //     return;
        // }
        // c.m_activated = true;
        // if(_where != null)
        //     _where.SetChoiceHome(c);
        // c.SetHouse(_where);
    }

    public static void Save(ref string _s)
    {
        _s = ReactReflection.MakePropertyString<NGUnlocks>();
    }
    public static void Load(string _l)
    {
        ReactReflection.DecodePropertyStrings<NGUnlocks>(_l);
        m_isLoaded = true;
    }
    public static bool Change(string _what)
    {
        var split = _what.Split('=', ':');
        var what = split[0].Trim();
        var value = true;
        if (split.Length >= 2)
        {
            bool.TryParse(split[1].ToLower().Trim(), out value);
        }

        var bracketSplit = _what.Split('(', ')', '[', ']');
        if(bracketSplit.Length > 1)
        {
            var funcName = bracketSplit[0];
            var f1 = typeof(NGUnlocks).GetMethod(funcName, BindingFlags.Static);
            var f2 = typeof(NGUnlocks).GetMethod(funcName, BindingFlags.Static);
            var func = typeof(NGUnlocks).GetMethod(funcName, BindingFlags.Static | BindingFlags.Public | BindingFlags.IgnoreCase);
            if(func == null) { return false; }
            func.Invoke(null, new object[] {bracketSplit[1]});
            return true;
        }
        var property = typeof(NGUnlocks).GetProperty(what, BindingFlags.Static | BindingFlags.Public | BindingFlags.IgnoreCase);
        if (property == null) { return false; }
        property.SetValue(null, value);
        return true;
    }

    public static bool IsUnlocked(string _what)
    {
        var split = _what.Split('=');
        var what = split[0].Trim();
        var property = typeof(NGUnlocks).GetProperty(what, BindingFlags.Static | BindingFlags.Public | BindingFlags.IgnoreCase);
        var o =property.GetValue(null);
        if (o is bool)
            return (bool) o;
        return false;
    }
    
    private static DebugConsole.Command s_unlockElement = new DebugConsole.Command("ngunlock", _s => {
        
        ReactReflection.DecodePropertyStrings<NGUnlocks>(_s.Replace(',', '='));

    });


}