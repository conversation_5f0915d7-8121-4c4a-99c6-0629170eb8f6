using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using TMPro;
using UnityEngine;
using Object = System.Object;
using Random = UnityEngine.Random;
using System.Reflection;
using System.Text;
using System.Text.RegularExpressions;
using UnityEngine.UI;

#if UNITY_EDITOR
using UnityEditor;
[CustomEditor(typeof(NGDemoManager))]
public class NGDemoManagerInspector : MonoEditorDebug.MonoBehaviourEditor
{
	public override void OnInspectorGUI()
	{
		base.OnInspectorGUI();

		var myScript = (NGDemoManager)target;
		if (GUILayout.Button($"Write Flow To file.txt"))
		{
			MAParserManager.ConvertFlowToText(NGBusinessFlow.s_flowsDict);
		}
		if (GUILayout.Button($"Set OnUnlock {myScript.m_checkBuilding.ToString()}"))
		{
			myScript.SetUnlock();
		}
		if (GUILayout.Button($"Show Blocks {myScript.m_checkBuilding.ToString()}"))
		{
			myScript.ShowBlocks();
		}
		if (GUILayout.Button("Add $100"))
		{
			NGDemoManager.Add100Dollars();
		}

		if (GUILayout.Button("Test Super View Flow"))
		{
			myScript.SuperViewFlow();
		}

		if (GUILayout.Button("Test Decision Text"))
		{
			myScript.TestDecisionText();
		}
		if (GUILayout.Button("Is Dock Unlocked"))
		{
			myScript.IsDockUnlocked();
		}
		if (GUILayout.Button("Test Tutorial Trigger"))
		{
			myScript.TestTutorialTrigger(); 
		}
		if (GUILayout.Button("Test Waggon Train"))
		{
			MAWaggonTrainController.Create(null, Vector3.zero, NGManager.Me.m_maBuildings[0].transform, new List<string>() { "MA_Produce_Mill_Thatch" }, null);
		}

		GUILayout.Space(20);
		replaceOrderBusinessGiftName = GUILayout.TextField(replaceOrderBusinessGiftName);
		if(GUILayout.Button($"SpawnOrder() Replace oldest order with Business Gift:\n '{replaceOrderBusinessGiftName}", GUILayout.Height(50)))
		{
			myScript.TestTutorialTrigger($"SpawnOrder({replaceOrderBusinessGiftName})");
		}
		if(Application.isPlaying)
		{
			GUILayout.Space(20);
			show = EditorGUILayout.Foldout(show, "Show Tutorial Triggers");
			if(show)
			{
				GUILayout.Label("Test trigger Button list:");
				foreach(var trigger in myScript.m_testTriggerList)
				{
					if(GUILayout.Button($"{trigger}"))
					{
						myScript.TestTutorialTrigger(trigger);
					}
				}
			}
		}
	}
	bool show;
	string replaceOrderBusinessGiftName = "Spawn Order business Gift Name";

}
#endif
public class NGDemoManager : MonoSingleton<NGDemoManager>
{
	[Header("Prefabs")]
	public NGPickupInfo m_pickupInfoPrefab;
	//public SCManager m_screenCapturePrefab;
	public GameObject m_rockPrefab;

	//public SVFManager m_svfManagerPrefab;
	[Header("Holders")]
	public Transform m_pickupInfoHolder;
	public Transform m_screenCaptureHolder;
	public Transform m_decorationHolder;
	public Transform m_guiHolder;
	public Transform m_guiVersion;
	[SerializeField] private bool m_disableServer = true;
	[SerializeField] private bool m_isNewGame = true;
	public Transform m_blockHolder;
	public string m_partTypeID;
	public List<string> m_removeBuildings = new List<string>() {
		"Factory Small",
		"Depot",
		"Warehouse",
		"Unemployment",

	};

	public bool m_genderChoice = false;
	public Camera m_townCamera;
	public string m_checkBuilding = "";
	public bool m_showAllDebug;
	public bool m_checkBuildingFlag = false;

	public bool m_doneRemoveBuildings = false;
	[SerializeField] private bool m_unlockAllBuys = true;
	public bool m_doneUnlockAllBuys = false;
	public bool m_showInfoBalloons = false;
	public List<string> m_disableList = new List<string>() { "HedgerowsCentralRoad_6_8" };
	public string m_findBlocks;
	public bool m_findBlocksNow;
	public List<NGBlockInfo> m_foundBlocks;
	public bool IsNewGame { get { return m_isNewGame;  } }
	public static bool IsServerDisabled => Me != null && Me.m_disableServer;
	public bool m_testCompetitionDialog;

	public bool m_testKeyboard;
	public string m_testRankingID = "125";
	public int m_testRankingDirection = 1;
	public bool m_iisDown;

	[System.Serializable] public class CreateComponentBuilding
	{
		public MABuilding m_building;
		public Vector3 m_pos;
		public bool m_create;
	}
	public List<CreateComponentBuilding> m_createComponentBuildings;
	public Transform m_componentBuildingHolder;
	bool m_componentBuildingsCreated;

	void Start()
	{
			
		foreach(var n in m_disableList)
        {
			var t = GameManager.Me.transform.parent.FindChildRecursiveByName(n);
			if (t) t.gameObject.SetActive(false);
        }

	

		GameManager.OnEnterTownManagementMode += OnEnterTownManagementMode;
		GameManager.OnExitTownManagementMode += OnExitTownManagementMode;
		StartCoroutine(AfterLoad());
	}
	public IEnumerator AfterLoad()
	{
		yield return new WaitUntil(() => ((GameManager.Me && GameManager.Me.LoadComplete)));
		MAParserSupport.TestAll();
//		MAChapterDialog.Create("Test", "This is a test", "Test", true);
/*		if(DistrictManager.Me.GetDistrictByID("Metal").HasBeenUnlocked == false)
			DistrictManager.Me.UnlockDistrictByID("Metal");

		NGBusinessFlow.s_flowsDict.TryGetValue("TestDestroyBoulders", out var flow);
		if (flow != null)
			MAGameFlow.StartFlow(flow);*/

	}

	public static void InvokeMethod(string input)
	{
		// Split the input string into class name and method name
		string[] parts = input.Split('.');
		string className = parts[0];
		string methodName = parts[1];

		// Get the Type object for the class
		Type classType = Type.GetType(className);

		// Get the MethodInfo object for the method
		MethodInfo method = classType.GetMethod(methodName);
		if (method != null)
		{
			object classInstance = Activator.CreateInstance(classType);
			method.Invoke(classInstance, null);
			
		}
		// Create an instance of the class

		// Invoke the method
	}
	
	private static void ParseInput(string input, out string className, out string methodName, out object[] parameters)
	{
		// Use regular expression to extract class name, method name and parameters
		parameters = new object[0];
		Match match = Regex.Match(input, @"(\w+)\.(\w+)\(([\w, ]*)\)");
		className = match.Groups[1].Value;
		methodName = match.Groups[2].Value;
		string paramString = match.Groups[3].Value;
		if (className.IsNullOrWhiteSpace() || methodName.IsNullOrWhiteSpace()) return;
		// Split the parameters string and convert them to the appropriate type
		string[] paramValues = paramString.Split(',');
		ParameterInfo[] paramInfos =  Type.GetType(className).GetMethod(methodName).GetParameters();
		parameters = new object[paramInfos.Length];
		for (int i = 0; i < paramInfos.Length; i++)
		{
			Type paramType = paramInfos[i].ParameterType;
			parameters[i] = Convert.ChangeType(paramValues[i], paramType);
		}
	}

	public void CreateComponentBuildings()
	{
		foreach (var b in m_createComponentBuildings)
		{
			if (b.m_create)
			{
				MABuilding.Create(b.m_building, m_componentBuildingHolder, b.m_pos);
			}
		}

		m_componentBuildingsCreated = true;

	}

	void TestAbstract(Object _obj, Object _obj2)
	{
		var t = _obj.GetType();
		var t2 = _obj2.GetType();
		if (t.IsSealed && t.IsAbstract)
		{
			
		}

		if (t2.IsSealed && t2.IsAbstract)
		{
			
		}
		Debug.Log("Peter");
	}
	
	private static DebugConsole.Command s_assignWorker = new DebugConsole.Command("AssignWorkers", _s => Me.AssignAllWorkers(_s));
	public void AssignAllWorkers(string _control)
	{/*
		int assignedWorkers = 0;
		if (_control.Equals("all"))
		{
			foreach (var worker in NGManager.Me.m_NGWorkerList)
				if (worker.MyJob)
				{
					worker.NGSetJob(null);
					worker.SetState(NGMovingObject.STATE.IDLE);
				}
		}
		foreach (var worker in NGManager.Me.m_NGWorkerList)
		{
			if (worker.MyJob) continue;
			int bestFactoryNeeded = 0;
			NGFactory bestFactory = null;
			foreach (var building in NGManager.Me.m_NGCommanderList)
			{
				var factory = building as NGFactory;
				if (factory == null || factory is NGHouse) continue;
				var needed = (int)factory.MaxWorkers - (int)factory.NumWorkers;
				if (needed > bestFactoryNeeded)
				{
					bestFactory = factory;
					bestFactoryNeeded = needed;
				}
			}

			if (bestFactory)
			{
				assignedWorkers++;
				worker.NGSetJob(bestFactory);
				worker.NGReturnToJob();
			}
		}
		Debug.LogError($"$Total Workers = {NGManager.Me.m_NGWorkerList.Count}, assigned = {assignedWorkers}");*/
	}
	void UnlockAllBuys()
	{
		ItemDetailsHelper.DEBUG_UnlockAllBuys(true);
		m_doneUnlockAllBuys = true;
	}

	void TestJoinCompetition()
	{
		Debug.Log("Join");
	}
	
	void TestCancelCompetition()
	{
		Debug.Log("Cancel");
	}

	public void SuperViewFlow()
	{
		//SVFManager.Create(m_svfManagerPrefab, NGManager.Me.m_centreScreenHolder);
	}

	public void IsDockUnlocked()
	{
		/*var unlocked = new List<MonoDistrict>();
		var districts = DistrictManager.Me.GetDistricts();
		var un = DistrictManager.Me.IsDistrictUnlocked("DockSide");
		foreach (var d in districts)
		{
			if(d.HasBeenUnlocked)
				unlocked.Add(d);
		}
		Debug.Log("Done");	*/
	}


	//private SCManager m_screenCaptureDialog;
    void Update()
    {
		/*var t = GameObject.Find("ShowPriceShadow");
		if (t != null)
		{
			Debug.Log("POP");
		}*/
	    CheckClipboard();
	    if((Utility.GetKey(KeyCode.LeftAlt) || Utility.GetKey(KeyCode.RightAlt)) && Utility.GetKeyDown(KeyCode.Z))
	    {
		    if(MADemoDialog.Me)
			    MADemoDialog.Me.ClickedClose();
		    else
			    MADemoDialog.Create();
	    }
#if UNITY_EDITOR || DEVELOPMENT_BUILD
	    // if (m_testKeyboard)
	    // {
		   //  NGKeyboardHelperGUI.Create(new List<NGKeyboardHelperGUI.NGKeyHelper>()
		   //  {
			  //   new NGKeyboardHelperGUI.NGKeyHelper("H", "Help Info"),
			  //   new NGKeyboardHelperGUI.NGKeyHelper("Shift", "Assign Job"),
			  //   new NGKeyboardHelperGUI.NGKeyHelper("Alt", "Assign Home")
	    //
		   //  });
		   //  m_testKeyboard = false;
	    // }
	    if (m_findBlocksNow)
	    {
		    m_findBlocksNow = false;
		    m_foundBlocks.Clear();
		    foreach (var d in NGBlockInfo.s_allBlocks)
		    {
			    if (d.Key.Contains(m_findBlocks, StringComparison.OrdinalIgnoreCase))
			    {
				    m_foundBlocks.Add(d.Value);
			    }
		    }
	    }
	   //  if((Input.GetKey(KeyCode.LeftAlt) || Input.GetKey(KeyCode.RightAlt)) && Input.GetKeyDown(KeyCode.S))
	   //  {
		  //   if(m_screenCaptureDialog) m_screenCaptureDialog.DestroyMe();
		  //   else
				// m_screenCaptureDialog = SCManager.Create();
	   //  }
	    
		if(Input.GetKey(KeyCode.LeftAlt) && !Input.GetKey(KeyCode.LeftControl) && Input.GetKeyDown(KeyCode.D))
        {
			m_showAllDebug = !m_showAllDebug;
        }
		if(!string.IsNullOrEmpty(m_checkBuilding) && m_checkBuilding.Length > 3)
		{
			CheckBuilding(m_checkBuilding);
		}
#endif
	    if(GameManager.Me.LoadComplete && m_componentBuildingsCreated == false)
		    CreateComponentBuildings();
    }
	public string m_previousClipboard;
    void CheckClipboard()
    {
	    object iiList = null;
	    Type iiType = null;
	    object currentLine = null;
	    var clipboard = GUIUtility.systemCopyBuffer;
	    if (m_previousClipboard == clipboard) return;
	    m_previousClipboard = clipboard;
	    List<FieldInfo> fields = new List<FieldInfo>();
	    var lines = clipboard.Split('\n');
	    
	    for (var i = 0; i < lines.Length; i++)
	    {
			var columns = lines[i].Split('\t');
			for (int j = 0; j < columns.Length; j++)
			{
				if (i == 0)
				{
					var name = columns[j].Trim('\t');

					if (j == 0)
					{
						iiType = Type.GetType(name);
						if (iiType == null) return;
						var prop = iiType.GetProperty("GetList");
						if (prop == null) return;
						iiList = prop.GetValue(null);
						if (IsList(iiList, out IList list) == false)
						{
							return;
						}
						break;
					}
					if (name.StartsWith("m_") == false)
					{
						name = "m_" + char.ToLower(name[0]) + name.Substring(1);
					}

					fields.Add(iiType.GetField(name));
					continue;
				}
				if (j >= fields.Count) continue;
				if (j == 0)
				{
					IsList(iiList, out IList list);
					foreach(var l in list)
					{
						var gg = fields[j].GetValue(l);
						if (gg.Equals(columns[j].Trim('\t')))
						{
							currentLine = l;
							break;								
						}
					}

					if (currentLine == null)
						break;
					continue;
				}

				if (currentLine == null || fields[j] == null) continue;
				object convertedValue = Convert.ChangeType(columns[j].Trim('\t'), fields[j].FieldType);
				fields[j].SetValue(currentLine, convertedValue);
			}
	    }
    }
    private bool IsList(object obj, out IList list)
    {
	    if (obj is IList asList)
	    {
		    list = asList;
		    return true;
	    }

	    list = null;
	    return false;
    }
    public List<BusinessLevelLeaderboard> m_testBusinessLevelLeaderboard;

    public void TestCallback(List<BusinessLevelLeaderboard> _leaderboard)
    {
	    m_testBusinessLevelLeaderboard = _leaderboard;
    }
	public ReactItemInfo.BuyItem m_checkedBuyItem;
	public void CheckBuilding(string _type)
	{
		m_checkedBuyItem = ItemDetailsHelper.GetBuyItemByPrefabName(_type) as ReactItemInfo.BuyItem;
	}
	public void SetUnlock()
	{
		m_checkedBuyItem = ItemDetailsHelper.GetBuyItemByPrefabName(m_checkBuilding) as ReactItemInfo.BuyItem;
		m_checkedBuyItem.Activate(true);
		m_checkedBuyItem.OnUnlock();
		//TownPlanningBoard.RefreshAllBoards();
		for (int i = 0; i < ItemDetailsHelper.s_buyItems.Count; i++)
		{
			var tt = ItemDetailsHelper.s_buyItems[i];
			if(tt == m_checkedBuyItem)
			{
				tt.Activate(true);
			}
		}
	}
	public int m_totalBlocks;
	public int m_totalGameObjects;
	public void ShowBlocks()
	{
		m_totalBlocks = 0;
		m_totalGameObjects = 0;
		var gos = Resources.LoadAll<GameObject>("_Prefabs/_Blocks/_CurrentlyUsed");
		foreach(var go in gos)
		{
			m_totalGameObjects++;
			var block = go.GetComponentInChildren<Block>(true);
			if(block == null) continue;

			m_totalBlocks++;
		}
	}

	/*public List<EventRankingTable.RankingTable> m_testLeaderboardItems;
	public EventRankingTable m_eventLeague;
	public EventRankingTable m_eventLeagueSmall;
	public EventRankingTable m_eventRankingTableSmallPrefab;
	public Transform m_eventRankingTableSmallHolder;
	public void TestLeaderboard()
	{
		m_testLeaderboardItems = new List<EventRankingTable.RankingTable>()
		{
			new EventRankingTable.RankingTable("123", "", "JAnn Staples", 5, 7, EventRankingTable.RankingTable.RewardType.LegacyGem, 999),
			new EventRankingTable.RankingTable("124", "", "John James the first of his name this is a very long name", 4, 5, EventRankingTable.RankingTable.RewardType.LegacyGem, 88),
			new EventRankingTable.RankingTable("125", "", "Sofia Sidle", 3, 3, EventRankingTable.RankingTable.RewardType.LegacyGem, 75),
			new EventRankingTable.RankingTable("126", "", "Andrew Car", 3, 1000, EventRankingTable.RankingTable.RewardType.LegacyGem, 60),
			new EventRankingTable.RankingTable("PODM", "", "Peter Molyenux", 3, 800, EventRankingTable.RankingTable.RewardType.LegacyDollar, 9),
			new EventRankingTable.RankingTable("127", "", "Andy cnut", 2, 700, EventRankingTable.RankingTable.RewardType.LegacyDollar, 8),
			new EventRankingTable.RankingTable("128", "", "Bill fcuk", 1, 600, EventRankingTable.RankingTable.RewardType.LegacyDollar, 7),
			new EventRankingTable.RankingTable("129", "", "Chris Wnaker", 0, 500, EventRankingTable.RankingTable.RewardType.LegacyDollar, 7),
			new EventRankingTable.RankingTable("130", "", "Dan Pneus", 0, 400, EventRankingTable.RankingTable.RewardType.LegacyDollar, 6),
			new EventRankingTable.RankingTable("131", "", "Eve Bigger", 0, 400, EventRankingTable.RankingTable.RewardType.LegacyDollar, 5),
			new EventRankingTable.RankingTable("PDM", "", "Fuk Fcuker", 0, 400, EventRankingTable.RankingTable.RewardType.LegacyDollar, 4),
			new EventRankingTable.RankingTable("133", "", "Gwen Gutter", 0, 400, EventRankingTable.RankingTable.RewardType.LegacyDollar, 4),
		};
		m_eventLeague = EventRankingTable.Create("Peter's Big Test", m_testLeaderboardItems, null);
		m_eventLeagueSmall = EventRankingTable.Create(m_eventRankingTableSmallPrefab, m_eventRankingTableSmallHolder, "Peter's big test", m_testLeaderboardItems);
	}

	public void TestLeaderboardUpdate()
	{
		var leaderboardItems = new List<EventRankingTable.RankingTable>()
		{
			new EventRankingTable.RankingTable("124", "", "John James the first of his name this is a very long name", 4, 5, EventRankingTable.RankingTable.RewardType.LegacyGem, 100),
			new EventRankingTable.RankingTable("123", "", "JAnn Staples", 5, 7, EventRankingTable.RankingTable.RewardType.LegacyGem, 99),
			new EventRankingTable.RankingTable("125", "", "Sofia Sidle", 3, 3, EventRankingTable.RankingTable.RewardType.LegacyGem, 75),
			new EventRankingTable.RankingTable("126", "", "Andrew Car", 3, 1000, EventRankingTable.RankingTable.RewardType.LegacyDollar, 60),
			new EventRankingTable.RankingTable("PDM", "", "Peter Molyenux", 3, 800, EventRankingTable.RankingTable.RewardType.LegacyDollar, 9),
		};
		if(m_eventLeague == null)
			TestLeaderboard();
		
		m_eventLeague.UpdateEntries(leaderboardItems);
	}
	public void TestLeaderboardRank()
	{
		var item = m_testLeaderboardItems.Find(o => o.m_id.Equals(m_testRankingID));
		if (item != null)
		{
			m_eventLeague.SetRank(m_testRankingID, m_testRankingDirection);
		}
	}

	public void TestLeaderboardResubmit()
	{
		var league = m_eventLeague.m_rankingTable;
		var smallLeague = m_eventLeagueSmall.m_rankingTable;
		for (int i = 0; i < league.Count; i++)
		{
			var rScore = Random.Range(0, 999);
			league[i].m_score = rScore;
			smallLeague[i].m_score = rScore;
		}
		league.Sort((x, y) => y.m_score.CompareTo(x.m_score));
		smallLeague.Sort((x, y) => y.m_score.CompareTo(x.m_score));
		m_eventLeague.UpdateEntriesNow(league);
		m_eventLeagueSmall.UpdateEntriesNow(smallLeague);
	}*/

	public void ShowEfficiencyDialogue()
	{/*
		foreach (var f in NGManager.Me.FactoryList)
		{
			SetEfficiencyDialogue(f);
		}

		foreach (var f in NGManager.Me.m_NGPubList)
		{
			SetEfficiencyDialogue(f);
		}

		foreach (var f in NGManager.Me.m_NGCafeList)
		{
			SetEfficiencyDialogue(f);
		}*/
	}

	public void DestroyUpgradeLevelDialogue()
	{
        foreach (var f in NGManager.Me.m_NGCommanderList)
        {
            if (f.m_balloonHolder == null) continue;
			//f.m_balloonHolder.GetComponentInChildren<NGShowUpgradeLevel>()?.Close();
		}
	}
/*
	private void SetUpgradeLevelDialogue(NGFactory _building, NGMovingObject _object, NGBusinessGift _gift)
	{
		if (_building.m_balloonHolder == null) return;
		if (!_building.IsWithinOpenDistrict() ) return;
		if (_object == null) return;
		var ds = _building.GetDropScore(_object);
		if (ds <= 0) return;

			var d = _building.m_balloonHolder.GetComponentInChildren<NGShowUpgradeLevel>();
		if (d == null) NGShowUpgradeLevel.Create(_building, _gift);
	}*/

	public void ShowUpgradeLevelDialogue(NGMovingObject _object, NGBusinessGift _gift)
	{/*
		foreach (var f in NGManager.Me.m_NGCommanderList)
		{
			SetUpgradeLevelDialogue(f as NGFactory, _object, _gift);
		}*/
	}

	public void OnEnterTownManagementMode(ETownManagementMode _mode)
    {
		if(_mode == ETownManagementMode.AssignWorkers)
        {
        }
		else if(_mode == ETownManagementMode.ShowEfficiency)
        {
			ShowEfficiencyDialogue();
		}
    }

	public void OnExitTownManagementMode(ETownManagementMode _mode)
	{
		if (_mode == ETownManagementMode.AssignWorkers)
		{
		}
		else if (_mode == ETownManagementMode.ShowEfficiency)
		{
		}
	}

	/*[ReadOnlyInspector][NonSerialized] */public List<string> m_testTriggerList = new List<string>()
	{
		"MoveCamera(BuildingComponent[Factory]StockIn, 75)",
		"UnlockInteractions(Building[Factory])",
		"Highlight(Component[ActionFactory])",
		"Highlight(Component[Chimney])",
		"Highlight(BuildingTile[28]:1)",
		"Highlight(Component[Chimney])",
		"ClearBanners()",
		"EndTutorialPhase()", 
		"DeactivateGUI(GUI[ObjectiveDialog])",
		"ActivateGUI(GUI[ObjectiveDialog])",
		"Highlight(GUI[ObjectiveDialog])",
		"UnlockInteractions(Building[5])",
		"MoveCamera(Building[5], 70)",
		"Highlight(Building[5])",
		"MoveCamera(Worker[13],50)",
		"FollowCamera(Worker[13], 50)",
		"CancelFollow()",
		"MoveCamera(Carryable[Product]:2, 60)",
		"FollowCamera(Carryable[Product]:2, 60)",
		"MoveCamera(Building[17], 75)",
		"UnlockInteractions(Building[17])",
		"Highlight(Component[HotspotProduceStockIn])",
		"KillGestures()",
		"GestureDrag(Building[17], Building[18])",
		"MoveCamera(Building[18], 75)",
		"Highlight(Building[18])",
		"GestureDrag(Building[18], Building[28))",
		"MoveCamera(Component[ActionOrderBoard], 60)",
		"UnlockInteractions(ActionOrderBoard)",
		"WaitForBuildingFull(Building[18], Component[StockOut])",
		"WaitForWorkerCount(3)",
		"WaitForStockCount(Product,2)",
		"WaitForAudioTriggered(OnboardDesignYour1stProduct_fit_for_king)",
		"WaitForAudioComplete(OnboardDesignYour1stProduct_fit_for_king)",
		"WaitForDesignTablePart(0)",
		"WaitForDesignTablePart(2)",
		"SpawnOrder(First Order Peoples)",
	};

	public int m_testTriggerCount = 0;
	public bool m_triggerWaitfor;
	public void TestTutorialTrigger()
	{
		m_triggerWaitfor = false;
		var what = m_testTriggerList[m_testTriggerCount];
		if (MAParserSupport.TryParse(what, out var result))
		{
			if (what.Contains("WaitFor", StringComparison.OrdinalIgnoreCase))
			{
				m_triggerWaitfor = result;
				if (result)
					m_testTriggerCount++;
			}
		
			
		}
	}	
	public void TestTutorialTrigger(string _trigger)
	{
		m_triggerWaitfor = false;
		var what = _trigger;
		if (MAParserSupport.TryParse(what, out var result, $"Demo{m_testTriggerCount}"))
		{
			if (what.Contains("WaitFor", StringComparison.OrdinalIgnoreCase))
			{
				m_triggerWaitfor = result;
				if (result)
					m_testTriggerCount++;
			}
		}
	}
	public void TestDecisionText()
	{
/*		var allFlows = NGBusinessFlow.s_flows;
		foreach (var f in NGBusinessFlow.s_choiceFlows)
		{
			allFlows.AddRange(f.Value);
		}
		foreach (var flow in allFlows)
		{
			if (flow.Decision != null)
			{
				var initialValue = flow.Decision.Activate();
				var currentValue = flow.Decision.GetValue();
				var currentExplainText = flow.Decision.GetCurrentExplainText(flow);
				var targetExplainText = flow.Decision.GetTargetExplainText(flow);
				var vv = 0;
			}
		}
		foreach (var d in NGBusinessDecision.s_decisions)
		{
			var initial = d.Activate();
			var current = d.GetValue();
			var text = d.GetCurrentExplainText();
			var explain = d.GetTargetExplainText();
			var vv = 0;

		}*/
		
	}		

	//	public BuildBarDraggableTile	m_buildCivicTilePrefab;
	//	public BuildBarDraggableTile	m_buildBusinessTilePrefab;

#if UNITY_EDITOR

	[MenuItem("22Cans/Misc/Add100Dollars")]
	public static void Add100Dollars()
	{
		if (NGPlayer.Me.m_cash != null)
		{
		//	MoneyCreator.SpawnMoneyFromBankOrShop((_amountGiven) =>
		//		MoneyHUDElement.OnEarnVisual(_amountGiven), 100);
			NGPlayer.Me.m_cash.Add(CurrencyContainer.TransactionType.Earned, 100,"Debug");
		}
	}
#endif

}
