using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;
using TMPro;
public class NGBuildTile : NGDirectionCardBase
{
    string m_designOverride;
    Sprite m_designSprite = null;
    bool m_needsDesign = false;
    public override bool ScaleCardDownOnDrag { get { return true; } }
    
    // Start is called before the first frame update
    bool BuildingPlaced(bool _placed)
    {
        if (_placed)
        {
            DesignTableManager.Me.SetNextDesignDrawerState(m_gift.m_cardPower);
            if(m_fromHolder != null)
            {
                PayGiftCost();
                m_fromHolder.GiftReceived(this);
            }
            else 
            {
                if (this != null)
                    Destroy(this.gameObject);
            }
        }
        else
        {
            ActionCancelled();
        }
        return true;
    }

    protected override void Update()
    {
       // m_price.text = GlobalData.CurrencySymbol + Cost.ToString("F0");
       if (m_price != null)
       {
           if(Cost == 0)
                m_price.gameObject.SetActive(false); 
           m_price.text = Cost.ToString("F0");
       }
        base.Update();
    }

    public override void ActionCancelled(bool _lockCardUntilDragEvent = false)
    {
        base.ActionCancelled(_lockCardUntilDragEvent);
        
        BuildingPlacementManager.Me.ToggleBuild(false, null, 0f);
    }
    
    void SetBuildingIcon()
    {
        var design = m_designOverride;
        if (string.IsNullOrEmpty(design)) design = m_gift.m_buildingDesign;
        GameManager.Me.GetDesignSpriteAsync(design, CaptureObjectImage.Use.Building, _s =>
        {
            m_designSprite = _s; 
            if (m_image == null)
                ; // image has been destroyed since requesting
            else if(m_designSprite == null) 
                m_image.sprite = SpriteAtlasMapLoader.LoadClonedSprite("_GUI/_Default_Images/todo");
            else
                m_image.sprite = _s;
        });
    }

    protected override bool IsPendingUserAction()
    {
        return BuildingPlacementManager.Me.IsConfirming;
    }

    public void SetBuildingDesign(string _design)
    {
        m_designOverride = _design;
        
        if (NGManager.Me.m_emptyBuildingCards)
        {
            if (m_designOverride.StartsWith("1|#") == false)
            {
                // TEMP HACK - pretend this is an "empty design" card
                var what = m_gift.m_buildingsToUpgradeList[0];
                int size = what[2..] == "House" ? 1 : 2;
                m_designOverride = $"1|#{size}:{size}|0|";
            }
        }
    }

    protected override void OnCardDraggedBackOverHolder()
    {
        BuildMode.CancelExternally();
    }

    override public NGCardInfoHolder GetDescriptionText()
    {
        return new NGCardInfoHolder(m_gift.m_giftTitle, "", Cost, 1, "Building");
    }

    protected override void OnDragWhenActivated()
    {
        BuildingPlacementManager.Me.m_overideRay = GetScreenRay();
    }

    override protected NGMovingObject DragActivate(PointerEventData _eventData)
    {
        DesignTableManager.Me.SetNextDesignDrawerState(m_gift.m_cardPower);
        SetCardHidden(true);
        BuildingPlacementManager.Me.ToggleBuild (true, new GameState_Design(m_designOverride), 0f, BuildingPlaced, () => {}, _eventData);
        //transform.localPosition = m_originalPosition;
        return null;
    }
    
    override protected void Activate(NGBusinessGift _gift, MAParserSection _maParserSection, INGDecisionCardHolder _fromHolder)
    {
        base.Activate(_gift, _maParserSection, _fromHolder);
        Utility.DoNextFrame(() => SetBuildingIcon());
        bool newBuilding = true;
        m_newAlert.gameObject.SetActive(newBuilding);
    }
}
