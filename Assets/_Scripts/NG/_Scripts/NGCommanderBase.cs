using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.EventSystems;
using TMPro;

using Product = GameState_Product;
using System.Text.RegularExpressions;
using UnityEngine.VFX;

public class NGCommanderBase : NGLegacyBase, IDamageReceiver {
	public enum EInOwnedDistrictState
	{
		Unknown,
		NotInOwnedDistrict,
		InOwnedDistrict,
	}

	//******************************************************************************************************************
	//Fields
	//******************************************************************************************************************
	#region Fields
	
	[Header("NGCommanderBase")] 
	public int m_linkUID;
	public Transform m_balloonHolder;
	private Transform m_transform = null;
	[NonSerialized] public Transform m_createdHolder;
	[NonSerialized] public Transform m_pickupHolder;
	[NonSerialized] public Transform m_storyHolder;
	[NonSerialized] public Transform m_miscHolder;
	[NonSerialized] public GameObject m_balloonAttachPoint;
	[NonSerialized] public List<SwitchObjects> m_switchObjects = null;
	protected Canvas m_debugCanvas;
	protected TMP_Text m_debugText;
	[NonSerialized] public Animator m_NGAnim;
	//protected VFXSatisfaction m_satisfactionVFX; 
	[NonSerialized] public Vector3 LocalHighestPoint;
	
	virtual public Vector3 DoorPosOuter
	{
		get;
		set;
	}

	virtual public Vector3 DoorPosInner
	{
		get;
		set;
	}
	[NonSerialized] public List<NGMovingObject> m_joinedObjects;
	public static NGCommanderBase s_heldBuilding;
	[NonSerialized] public List<NGPollutionEmitter> m_pollutionEmmiters = new List<NGPollutionEmitter>();
	[NonSerialized] public bool m_beingBuiltFlag;
	protected bool m_isPolluting = false;
	protected Vector3 m_doorPos;
	[SerializeField] protected Animator m_upgradeAnimator;
	[NonSerialized] public int m_buildingUpgradeID = Animator.StringToHash("BuildingUpgrade");
	[NonSerialized] public int m_materialIndex;
	protected float m_baseRadius = -1f;
	[NonSerialized] public string m_siteInfo;
	[NonSerialized] public float m_moneySpentWhenPlaceBuilding = 0;
	[NonSerialized] public bool m_needsCleanup = false;
	[NonSerialized] public GameState_Building m_stateData;
	[NonSerialized] public string m_title;
	private float m_currentAge = -1;
	private int m_currentAgeType = 0;
	CSGCuboids m_cutter;
	float m_smoothedProgress = 0;
	Transform m_visuals;
	protected EInOwnedDistrictState m_inOwnedDistrictState = EInOwnedDistrictState.Unknown;
	public EInOwnedDistrictState InOwnedDistrictState => m_inOwnedDistrictState;
	public  int m_ambientProductionSound = 0;
	public int m_lockCount = 0;
	public float m_buildingRaise = 0;
	
	public bool m_lockedFromPlayer;
	public string m_setPiecePrefab;
	public bool IsSetPiece => string.IsNullOrEmpty(m_setPiecePrefab) == false;
	public bool m_ignoreDoorPaths = false;
	public bool m_ignoreMoveToStockOut = false;
	
	public bool m_fuzzyDistrictCheck = false;

	#endregion Fields

	//******************************************************************************************************************
	// Properties
	//******************************************************************************************************************
	#region Properties
	public GameState_Design Design => m_stateData.m_buildingDesign;
	public string DebugText { get => m_debugText.text;  set { if (m_debugText) m_debugText.text = value; } }
	
	public bool IsPermanentlyLocked => (m_setPiecePrefab.IsNullOrWhiteSpace() == false || m_stateData.m_lockedFromPlayer) && GameManager.IsEditMode == false;
	public bool IsLocked => m_lockCount > 0 || IsPermanentlyLocked;
	public void AddLock() => ++m_lockCount;
	public void RemoveLock()
	{
		--m_lockCount;
		if (m_lockCount == 0)
			NGManager.Me.FireBuildingUnlockEvent(this);
	}
	
	public NGCommanderBase NotMe => NGManager.Me.m_NGCommanderList[0] == this ? NGManager.Me.m_NGCommanderList[1] : NGManager.Me.m_NGCommanderList[0];
	
	public bool IsBeingRedesigned => false;
	public float TotalUnbuffedBuildTime => 0;
	public bool HasBeenFullyConstructedAtLeastOnce { get => m_stateData.m_hasEverBeenConstructed; set => m_stateData.m_hasEverBeenConstructed = value; }
	public bool IsBeingConstructed => false;//m_stateData.m_constructionFraction < 1f;
	//public VFXSatisfaction SatisfactionVFX => m_satisfactionVFX;
	public Transform Visuals => m_visuals;
	public bool IsPaused { get { return m_stateData.m_isPaused; } }
	public void TogglePaused()
	{
		m_stateData.m_isPaused = !m_stateData.m_isPaused;
	}

	public string PriorityDeliverTarget
	{
		get => m_stateData.m_priorityDeliverTarget;
		set => m_stateData.m_priorityDeliverTarget = value;
	}
	
	private string m_districtName;
	public string DistrictName
	{
		get
		{
			if(m_districtName == null)
			{
				m_districtName = DistrictManager.Me.GetDistrictName(transform.position).ToLower();
			}
			return m_districtName;
		}
	}

	#endregion Properties

	//******************************************************************************************************************
	//Overrides	
	//******************************************************************************************************************
	#region Overrides

	override public void DestroyMe()
	{
		if (GameManager.Me == null) return; // shutting down
		GameManager.Me.m_state.m_buildings.Remove(m_stateData);
		NGManager.Me.DestroyBuilding(this, false);
		//TODO: Dimitri if the order is on the building, remove it (esp from the factory)
		base.DestroyMe();
	}

	#endregion Overrides

	//******************************************************************************************************************
	//Virtual Properties	
	//******************************************************************************************************************
	#region Virtual Properties
	virtual public bool IsLongPressReceiver => false;
	virtual public bool PlayIdleAnimationsWhenOutside => true;
	virtual public bool IsRepairable => false;
	virtual public bool IsProductProducer => false;
	virtual public bool IsUpgradableBuilding => false;
	virtual public bool IsTinted => false;
	virtual public MAOrder Order { get; set; }
	virtual public GameState_Product ProductMade { get => null; set { } }
	virtual public bool IsWorkerCleanUpEnabled { get => false; set { } }
	virtual public int MaxWorkers { get {  return 0; } set {  } } //Upgradeable though refection
	virtual public float ToProductScore { get => 0; set {  } }
	virtual public float NumWorkers => 0;
	virtual public float Radius { get { return 0f; } set { } }
	virtual public string GetBuildingTitle() => m_title;
	virtual public string DescriptionText => "";
	virtual public string WhatAmIText { get { return Name.MakeNice(); } }
	virtual public string WhoAmIText { get { return WhatAmIText; } }
	virtual public Transform StaticBalloonHolder => transform;
	virtual public bool IsStoryTeller => false;
	virtual public Transform StoryBubbleHolder { get => m_storyHolder; set => m_storyHolder = value; }
	virtual public bool IsVisibleInside => false;
	virtual public bool IsOperational => !m_beingBuiltFlag;
	virtual protected float PollutionIntensity => 0f;

	virtual public float ClickAddsHowMuch => 0f;
	virtual public bool FlattensLand => true;

	#endregion Virtual Properties
	
	//******************************************************************************************************************
	// Virtual Functions
	//******************************************************************************************************************
	#region Virtual Functions
	virtual public NGProductInfo GetProductLineInfo() => null;

	#region IDamageReceiver
	public HashSet<IDamager> TargettedBy => GetComponent<TargetObject>()?.TargetedBy ?? new();
	public virtual int TargetPriority { get; set; } = 1;
	public virtual bool CanBeTargeted { get { return true; } }
	public virtual MAMovingInfoBase GetMovingInfo() { return null; }
	public Transform Transform => m_transform;
	public virtual void ApplyDamageEffect(IDamageReceiver.DamageSource source, float _damageDone, Vector3 _damageOrigin, MAAttackInstance _attack=null, MAHandPowerInfo handPower = null)
	{
		_damageDone = 0;
	}
	#endregion

	public virtual void OnBuildingDesignChanged() {}
	
	public bool AssertIsInOpenDistrict()
	{
#if UNITY_EDITOR
		if(m_inOwnedDistrictState == EInOwnedDistrictState.Unknown)
		{
			Debug.LogError($"NGFactory - AssertIsInOpenDistrict - m_inOwnedDistrictState {m_inOwnedDistrictState} - TerrainManager.Me.AllBlocksReady {TerrainManager.Me.AllBlocksReady} - _building {name} {GetType().Name} - m_linkUID {m_linkUID}- Calculated state IsWithinOpenDistrict: {IsWithinOpenDistrict()}");
		}
#endif
		return m_inOwnedDistrictState == EInOwnedDistrictState.InOwnedDistrict;
	}
	
	virtual public void AddSmoke(GameObject obj) { }
	virtual public bool IsPushedStateBezierEligible() { return false; }
	virtual public void SetColorBasedOnJobs() {}
	virtual public Transform GetArriveTransform() => transform;
	virtual public Transform ReactionTransform(NGMovingObject _who) => GetArriveTransform();
	virtual public bool AddObjectFromDrop(NGMovingObject _worker, SpecialHandlingAction _restrictedAction) => false;
	virtual public bool CanDragMeInLongPress() => true;
	virtual public bool CanDragWorker() => true;
	virtual public void OnBeginPressHold(PointerEventData _eventData){}
	virtual public void OnUpdatePressHold(PointerEventData _eventData){}
	virtual public void OnEndPressHold(PointerEventData _eventData){}
	virtual public void OnBeginLongPress(PointerEventData _eventData){}
	virtual public void OnUpdateLongPress(PointerEventData _eventData, float _longPressTime){}
	virtual public void OnEndLongPress(PointerEventData _eventData){}
	virtual public void OnBeginDragging() {}
	virtual public void OnEndDragging() {}
	virtual public void OnDragging() {}
	virtual public ShowBuildingName ShowBuildingTitle()
	{
		return ShowBuildingName.Create(this);		
	}
    virtual public void ToggleBeingBuilt(bool _flag)
    {
        m_beingBuiltFlag = _flag;
    }
	virtual public void CompleteConstruction()
	{
		HasBeenFullyConstructedAtLeastOnce = true; 
		if(m_storyHolder != null)
			m_storyHolder.gameObject.SetActive(true);
	}
    virtual protected void OnBuildingSiteCreated() {}
    virtual public bool IsDropReceiver(NGMovingObject _object) => false;
	virtual protected bool ShouldPollutionBeActive() => false;
	virtual public void	ObjectArrived(NGMovingObject _object) {}
	
	virtual public Vector3 ThrowFromPos
	{
		get
		{
			var pos = GetArrivePos();
			if (m_doorPos.sqrMagnitude > 0)
				return pos + (pos - m_doorPos);
			return pos;
		}
	}
	virtual public	Vector3	GetArrivePos(NGMovingObject _who=null) { return DoorPosOuter; }
	virtual public string GetAddress() => DescriptionText;
    virtual public string GetPostcode() 
    {
        string townBit = "Oakridge ";
        townBit += "1 ";

        string street = DescriptionText.Replace(' ', 'Q');
        var digits = Regex.Match(street, @"\d+").Value;
        int no = int.Parse(digits);

        int l = street.Length - 3;
        int n = no % 9;
        n = n % l;
		n += 2;

        string extraBit = ((no % 9) + 1).ToString();
        if (l > 3)
            extraBit += street.Substring(n, 2).ToUpper();
        else
            extraBit += "AA";

        return townBit + extraBit;
    }

    virtual protected void Awake()
    {
	    m_transform = transform;
    }

	virtual public bool SetWorkerPickup(NGMovingObject _object) => false;
	virtual public SaveContainers.SaveReactCommander GetReactProto() { return new SaveContainers.SaveReactCommander(); }
	virtual protected void Update()
	{
		CheckSetPieceState();
		UpdatePlants();
		UpdateConstruction();
		UpdateCranes();
	}
	virtual public void UpdateWorkerPositions() { }
	virtual public string GetProducingSound() => string.Empty;
	virtual public void ShakenFromHand(ReactPickupPersistent _object) {}
	virtual protected void	SetupBalloons() {}
	virtual public void AcceptThrownDrop(NGMovingObject _obj) {}
	virtual protected void ChooseProduct() {}
	virtual public void Load(string _typeSpecificData) {}
	virtual public Vector3 GetBestDropPosition(NGMovingObject _object) => DoorPosInner;
	virtual public bool DroppedFromHand(NGMovingObject _object, Vector3 _velocity, Vector3 _dest = default, float flyTime = 1f) { return false; }
	virtual public bool IsOkayToCollide(NGMovingObject _object) { return false; }
	virtual public void WorkerArrivesInsideFromOutside(NGMovingObject _worker) {}
	virtual public bool HasDragContent() { return false; }
	virtual public GameObject GetDragContent() { return null; }
	virtual protected void TogglePollution(bool _flag) {}
	virtual public SpecialHandlingAction HasSpecialHandling(NGMovingObject _obj, SpecialHandlingAction _restrictedAction) => null;
	virtual public bool ApplySpecialDropHandling(NGMovingObject _obj, SpecialHandlingAction _restrictedAction) => false;

	virtual public void PlaceInWorld(Vector3 _position)
	{
		if (NGManager.Me.m_NGCommanderList.Contains(this) == false)
			NGManager.Me.m_NGCommanderList.Add(this);
		//BuildingLists.Add(this);

		if(GameManager.Me.LoadComplete)
		{
			SetInOwnedDistrictState(DistrictManager.Me.IsWithinDistrictBounds(_position, true));
		}

		NGWindManager.Me.UpdateWindBounderies();
		
		//CreateCranes();
	}
	virtual public void SetupFromLoad() => PlaceInWorld(transform.position);
	virtual public void SetupFromLoad(SaveContainers.SaveNGBuilding _save) {}
	 
	virtual protected void Start()
	{
		if (GameManager.IsVisitingInProgress)
		{
			// hide balloons and story bubbles when visiting
			if (m_balloonHolder != null) m_balloonHolder.gameObject.SetActive(false);
			if (m_storyHolder != null) m_storyHolder.gameObject.SetActive(false);
		}
		m_NGAnim = GetComponent<Animator>();

		m_switchObjects = new List<SwitchObjects>();
		foreach (var block in GetComponentsInChildren<Block>())
		{
			if (block.m_visualSwitcher != null)
				m_switchObjects.Add(block.m_visualSwitcher);
		}
		
		var debugCanavas = transform.Find("NGDebugCanvas");
		if(debugCanavas)
        {
			m_debugCanvas = debugCanavas.GetComponent<Canvas>();
			m_debugText = debugCanavas.GetComponentInChildren<TMP_Text>();
			//debugCanavas.transform.localPosition = new Vector3(0f, LocalHighestPoint.y, 0f);
			m_debugCanvas.gameObject.SetActive(false);
		}

		m_pollutionEmmiters = GetPollutionEmitters();
		TogglePollution(true);
	}

	protected virtual void OnDisable()
	{
		UpdateBuildSign();
	}

	virtual public void PickUpBuilding()
	{
		m_districtName = null;
		s_heldBuilding = this;
		if (GameManager.Me.IsOKToPlayUISound())
			AudioClipManager.Me.PlaySoundOld("PlaySound_Move_Building_Button", GameManager.Me.transform);
		BuildMode.MoveBuilding(this);
	}

	virtual public bool HasThrowPosition()
	{
		return false;
	}

	virtual public void OnStandardClick(PointerEventData _eventData) { }
	virtual public bool ObjectArrivedInside(NGMovingObject _object)
	{
		_object.CleanupIfCarrying();
		return true;
	}

	virtual public void EnterBuilding()
	{
		if (CanRedesign())
		{
			if (GameManager.Me.IsOKToPlayUISound())
				AudioClipManager.Me.PlaySoundOld("PlaySound_Building_To_DesignTable", GameManager.Me.transform);
			DesignTableManager.LoadBuildingDesign(this);
		}
	}
	
	virtual public void OnBeginDrag(PointerEventData _eventData)
	{
	}
	virtual public void OnEndClickHold(PointerEventData _eventData)
	{
	}
	virtual public void OnDrag(PointerEventData _eventData)
	{
	}
	virtual public void OnEndDrag(PointerEventData _eventData)
	{
	}
	virtual public void BeginDragInternal(PointerEventData _eventData)
	{
	}
	virtual public void EndDragInternal(PointerEventData _eventData)
	{
	}
	virtual public void ShowBuildingAnimation() {}

	public string GetDistrictName(Vector3 p)
    {
        return DistrictManager.Me.GetDistrictName(p);
    }

	public bool IsWithinOpenDistrict(bool _includeDebug = false)
	{
		if (DistrictManager.Me.IsWithinDistrictBounds(transform.position, true))
			return true;
		if (m_fuzzyDistrictCheck)
		{
			if (DistrictManager.Me.IsWithinDistrictBounds(transform.position + Vector3.right * .5f, true)) return true;
			if (DistrictManager.Me.IsWithinDistrictBounds(transform.position + Vector3.right * -.5f, true)) return true;
			if (DistrictManager.Me.IsWithinDistrictBounds(transform.position + Vector3.forward * .5f, true)) return true;
			if (DistrictManager.Me.IsWithinDistrictBounds(transform.position + Vector3.forward * -.5f, true)) return true;
		}
		if (_includeDebug && GameManager.IsEditMode)
			return true;
		return false;
	}
	
	public bool SetInOwnedDistrictState(bool _inOwnedDistrict)
	{
		var newInOwned = _inOwnedDistrict ? EInOwnedDistrictState.InOwnedDistrict : EInOwnedDistrictState.NotInOwnedDistrict;
		var oldInOwned = m_inOwnedDistrictState;
		m_inOwnedDistrictState = newInOwned;
		if (oldInOwned == EInOwnedDistrictState.Unknown || oldInOwned == newInOwned)
			return false;
		OnInOwnedDistrictChanged();
		return true;
	}
	virtual protected void OnInOwnedDistrictChanged() { }

	virtual public bool BuildingHasDoor() => false;

	protected bool CheckNuke(PointerEventData _eventData)
	{
#if UNITY_EDITOR
		if (Input.GetKey(KeyCode.N) && _eventData.button == PointerEventData.InputButton.Right)
		{
			BuildingPlacementManager.Me.ShowConfirmationDialog(transform.position, _b =>
			{
				if (!_b)
					return;
				var (prevMin, prevMax) = GetExtents();
				DestroyMe();
				Utility.DoNextFrame(() => RoadManager.Me.m_pathSet.CreateVisuals(prevMin, prevMax, null));
			}, 0, "Destroy this building? This may break your game, destroy at your own risk!");
			return true;
		}
#endif
		return false;
	}

	virtual public void OnBeginClickHold(PointerEventData _eventData)
	{
		// kept this incase we decide to reimplement the feature to block context menu items when a story bubble is above the building
		//if(GetComponentInChildren<c_objectGUI>() == null)
	}
	virtual public void OnCollisionEnter(Collision collision){}
	virtual protected ContextMenuData GetContextMenuData() {
		ContextMenuData data = new ContextMenuData();
		var building = (this as MABuilding);
		if(building != null)
			data.m_title = building.GetBuildingTitle();// building.GetDisplayName();
		else
			data.m_title = name.Split('>')[0].Trim();
		data.m_buttonDataList = GetContextMenuButtonData();
		return data;
	}
	virtual protected List<ContextMenuData.ButtonData> GetContextMenuButtonData() {
		List<ContextMenuData.ButtonData> buttonDatas;

		if (true) //TODO IsOperational)
		{
			buttonDatas = new List<ContextMenuData.ButtonData>{
				new ContextMenuData.ButtonData{
					m_label = Localizer.Get(TERM.GUI_INFO),
					m_onClick = ShowInfoPlaque
				},
				new ContextMenuData.ButtonData{
					m_label = Localizer.Get(TERM.GUI_DESIGN),
					m_onClick = EnterBuilding,
					m_interactableCallback = () => IsWithinOpenDistrict() && MAUnlocks.Me.m_designBuildings
				},
				new ContextMenuData.ButtonData{
					m_label = Localizer.Get(TERM.GUI_EJECT),
					m_onClick = DoubleTapByPlayer,
					m_interactableCallback = () => IsWithinOpenDistrict() 
				}
			};
		}
		else
		{
			buttonDatas = new List<ContextMenuData.ButtonData>{
				new ContextMenuData.ButtonData{
					m_label = Localizer.Get(TERM.GUI_INFO),
					m_onClick = ShowInfoPlaque
				}
			};
		}

		return buttonDatas;
	}
	virtual public float GetDropScore(NGMovingObject _object, out SpecialHandlingAction _action, SpecialHandlingAction _restrictedAction) { _action = null; return 0f; }
	virtual public Product GetProduct() { return null; }
	virtual public void DoubleTapByPlayer() {}
	virtual protected void ShowInfoPlaque()
	{
		NGBuildingInfoGUI.Create(this);
		return;
		GenericBuildingInfoUIController uiController = InfoPlaqueManager.Me.LoadUI<GenericBuildingInfoUIController>();

		uiController.m_ejectButton.onButtonClick += (data) =>
		{
			DoubleTapByPlayer();
			uiController.Close();
		};

		uiController.Setup(this as NGCommanderBase);
		uiController.Show();
		uiController.m_designButton.onButtonClick += (data) =>
		{
			/*TODO if (CanRedesign())
			{
				CheckForSurroundingBuildingsAndRoads();
				DesignTableSceneLoader.LoadBuildingDesign(this, m_buildingType);
			}*/
		};
	}

	#endregion Virtual Functions
	
	//******************************************************************************************************************
	// Functions
	//******************************************************************************************************************
	#region Functions
	
	protected void CheckSetPieceState()
	{
#if UNITY_EDITOR
		if (IsSetPiece)
		{
			if (m_linkUID == 0)
			{
				if (m_stateData == null) m_stateData = new GameState_Building();
				BuildingPlacementManager.ManageBuildingId(this);
				GameManager.Me.m_state.m_buildings.Add(m_stateData);
			}
			m_stateData.m_setPiecePrefab = m_setPiecePrefab;
			m_stateData.m_x = transform.position.x;
			m_stateData.m_z = transform.position.z;
			m_stateData.m_direction = transform.rotation.eulerAngles.y;
			m_stateData.m_id = m_linkUID;
		}
#endif
	}

	public static void MoveHeldBuilding(Vector3 _pos, Quaternion _rot)
	{
		if (s_heldBuilding != null)
		{
			s_heldBuilding.MoveMe(_pos, _rot);
			PlaceHeldBuilding(true);
		}
		else
			Debug.LogError("Trying to move held building when no building is held");
	}
	public static void PlaceHeldBuilding(bool _posChanged = false)
	{
		if (s_heldBuilding != null)
		{
			s_heldBuilding.PlaceMe(_posChanged);
			s_heldBuilding = null;
		}
	}
	public void PlaceMe(bool _posChanged)
    {
	    gameObject.SetActive(true);
		if (_posChanged)
		{
			RefreshPos();
		}
    }

	public void MoveMe(Vector3 _pos, Quaternion _rot)
    {
		transform.SetPositionAndRotation(_pos, _rot);

		var visuals = transform.Find("Visuals");
        foreach(Transform child in visuals)
            BuildingPlacementManager.CheckRoadsAndDegrass(child);
    }
	public void RefreshPos()
	{
		/*TODO
		Debug.Assert(Nav22.PolyNavigation.Me.RemoveCageFromNetwork(transform), "No cage was found for the building being dragged", gameObject);
		Nav22.PolyNavigation.Me.requestnewBake = true;
		Building.RecreateCage(gameObject, true);
		*/
		
		UpdateWorkerPositions();
		NGWindManager.Me.UpdateWindBounderies();

		MABuilding building = this as MABuilding;
		if(building != null)
		{
			foreach(var component in building.m_components)
			{
				component.OnBuildingMoved();
			}
		}
	}

	public (Vector3, Vector3) GetExtents(float _groundExtent = 0)
	{
		return BuildingPlacementManager.GetFlattenBounds(this, _groundExtent);
	}

	void OnDrawGizmos_()
	{
		var (min, max) = GetExtents(2);
		var c = (min + max) * .5f;
		c = c.GroundPosition();
		var size = max - min;
		size.y = 2;
		Gizmos.color =  new Color(1, 1, 0, .5f);
		Gizmos.DrawCube(c, size);
		Gizmos.color = new Color(1, 1, 0, 1);
		Gizmos.DrawWireCube(c, size);
	}

	Dictionary<Block, int> m_blockToAudio = new();
	Dictionary<Block, int> m_blockToBuildingAudio = new();
	bool m_lastAnimationSoundState = false;
	void PlayAnimationSound(bool _active)
	{
		if (m_lastAnimationSoundState == _active) return;
		m_lastAnimationSoundState = _active;
		var blocks = m_visuals.GetComponentsInChildren<Block>();
		foreach (var block in blocks) 
		{
			PlayBlockAudio(_active, block, block.m_animatedAudio, m_blockToAudio, block.gameObject);
			PlayBlockAudio(_active, block, block.m_animatedBuildingAudio, m_blockToBuildingAudio, gameObject);
		}
	}

	void PlayBlockAudio(bool _active, Block _block, AkEventHolder _hook, Dictionary<Block, int> _lookup, GameObject _go)
	{
		if (_hook.IsValid())
		{
			var transformOverride = _go.GetOrAddComponent<AudioTransformOverride>();
			transformOverride.m_rotationAdjust = Vector3.up * (180 + _block.m_animatedAudioOrientationOffset);
			if (_active)
				_lookup[_block] = _hook.Play(_go);
			else if (_lookup.Remove(_block, out var id))
				AudioClipManager.Me.StopSound(id, _go);
		}
	}
	

	int m_switchVisualsState = -1;
	public void SwitchVisuals(bool _active)
	{
		int newState = _active ? 1 : 0;
		if (m_switchVisualsState == newState) return;
		m_switchVisualsState = newState;
		
		PlayAnimationSound(_active);
		foreach (var visual in m_switchObjects)
		{
			visual.SwitchTo(_active ? 1 : 0);
		}
	}
	public void TriggerAnim(string _which, bool _flag)
	{
		if(m_NGAnim)
			m_NGAnim.SetBool(_which, _flag);
	}
	public void AnimEndShowCreated()
	{
		m_createdHolder.DestroyChildren();
		m_createdHolder.localPosition = Vector3.zero;
		m_createdHolder.localEulerAngles = Vector3.zero;
		m_createdHolder.localScale = new Vector3(1,1,1);
		TriggerAnim("CreatedProduct", false);
	}
	private List<NGPollutionEmitter> GetPollutionEmitters()
	{
		var emitters = new List<NGPollutionEmitter>();

		Transform t = this.transform;

		// Only include enabled gameobjects
		var allEmitters = t.GetComponentsInChildren<NGPollutionEmitter>(false);

		float highestEmitter = float.MinValue;
		NGPollutionEmitter chosenEmitter = null;
		foreach (var emitter in allEmitters)
		{
			if (emitter.enabled)
			{
				emitters.Add(emitter);
				continue;
			}

			float y = emitter.transform.parent.position.y;
			if (y > highestEmitter)
			{
				chosenEmitter = emitter;
				highestEmitter = y;
			}
		}

		// If we havent find any enabled, consider only the highest
		if (emitters.Count == 0 && chosenEmitter != null)
		{
			emitters.Add(chosenEmitter);
		}
		return emitters;
	}

	public void SetupSmoke()
	{
		m_pollutionEmmiters = GetPollutionEmitters();
	}
	public void HighlightBuilding(){
		CameraRenderSettings.Me.SetHighlight(this, -1);
		//if(m_satisfactionVFX == null)
		//	m_satisfactionVFX =  VFXSatisfaction.Create(this, transform, "Highlight");
		//else
		//	m_satisfactionVFX.RefreshDisplay("Highlight");
	}

	public void UnhighlightBuilding() {
		//if(m_building.SatisfactionVFX != null)
		//	m_building.SatisfactionVFX.Deactivate();
		CameraRenderSettings.Me.ClearHighlight(this);
	}
	public void ShowSatisfactionVFX(){
		// if(m_satisfactionVFX == null)
		// 	m_satisfactionVFX =  VFXSatisfaction.Create(this, transform, NGTownSatisfactionHUD.s_selectedSatisfactionCategory);
		// else
		// 	m_satisfactionVFX.RefreshDisplay(NGTownSatisfactionHUD.s_selectedSatisfactionCategory, true);
	}

	virtual public float TotalRestPerSecond() => 1;
	virtual public int MaxCustomerSpaces => 0;
	virtual public int AvailableCustomerSpaces => 0;
	
	// public void ShowStoryIcon(c_character _who, c_choice _choice, c_objectGUI.BubbleType _type) {
	// 	c_objectGUI.Create(this, _who, _choice, _type);
	// }
	public void GetRoadFacingEdge(out Vector3 _a, out Vector3 _b) {
		var front = DoorPosOuter;
		var fwd = (DoorPosOuter - DoorPosInner).normalized;
		var side = new Vector3(fwd.z, fwd.y, -fwd.x);
		_a = front + fwd * .2f - side * 2f;
		_b = front + fwd * .2f + side * 2f;
	}
	public Vector3 GetRoadFacingEdgePoint(int _index, int _total) {
		Vector3 a, b;
		GetRoadFacingEdge(out a, out b);
		return Vector3.Lerp(a, b, (float)_index / (float)_total);
	}
	public List<Vector3> GetPathFromDoorToRoadFacingEdgePoint(int _index, int _total) {
		var path = new List<Vector3>();
		path.Add(DoorPosInner);
		path.Add(DoorPosOuter);
		path.Add(GetRoadFacingEdgePoint(_index, _total));
		path.Add(path[path.Count - 1] - Camera.main.transform.forward.GetXZNorm() * 0.4f);
		float ty = transform.position.y;
		for (int i = 0; i < path.Count; i++)
		{
			path[i] = path[i].NewY(ty);
		}
		return path;
	}
	// public bool IsShowingIcon(c_objectGUI.BubbleType _type = c_objectGUI.BubbleType.Any) {
	// 	var holder = (StoryBubbleHolder == null) ? transform : StoryBubbleHolder;
	// 	return c_objectGUI.IsNoOtherBubble(holder, _type);
	// }
	public bool UpdateBuildingDesign(NGCommanderBase _commander, bool _isConstructed = false) => false;
	public void RevertDesignID(int _designID){}
	public static List<Transform> CreateInteractionPoints(NGCommanderBase _commanderBuilding, int _points, Transform _parent)
	{
		return null;
	}
	public Vector3 GetNotificationPoint()
	{
		return transform.localToWorldMatrix * LocalHighestPoint;
	}
	public void SetMaterial(int _index) 
	{
		if (_index == -1) return;
		Utility.SetTintWindowColour(gameObject, 0, GlobalData.Me.m_pollutionColours[_index].m_buildingColor, false, true,true, null, true);
	}
	protected bool CanRedesign() {
		return NGDirectionCardBase.s_cardInFlight == null;
		if(true)//NGPlayer.Me.m_cash.Balance >= 0)
			return true;
		return false;
	}
	private Vector3 m_highestPointCache = default;
	public void InvalidateHighestPointCache() => m_highestPointCache = default;
	public Vector3 GetHighestPoint(bool _ignoreDisabled = true)
	{
		if (m_highestPointCache.sqrMagnitude < .001f * .001f)
		{
			var bounds = ManagedBlock.GetTotalVisualBounds((Visuals ?? transform).gameObject, null, _ignoreDisabled);
			m_highestPointCache = bounds.center + Vector3.up * bounds.extents.y;
		}
		return m_highestPointCache;
	}
	public Vector3 GetHighestPoint(Vector3 _offset)
	{
		var bounds = ManagedBlock.GetTotalVisualBounds(gameObject);
		var hp = bounds.center + Vector3.up * bounds.extents.y;
		return hp + _offset;
	}
	public GameObject[] BuildingBlocks()
	{
		List<Block> blockList = new();
		GetComponentsInChildren<Block>(blockList);
		return blockList.ConvertAll(x => x.gameObject).ToArray();
	}

	private static DebugConsole.Command s_highlight = new DebugConsole.Command("highlight", _s =>
	{
		if (string.IsNullOrEmpty(_s))
		{
			CameraRenderSettings.Me.ClearHighlight();
			return;
		}
		_s = _s.ToLower();
		var bits = _s.Split(',');
		foreach (var c in NGManager.Me.m_NGCommanderList)
		{
			if (c.GetType().ToString().ToLower() == bits[0])
			{
				float max = 1;
				if (bits.Length > 1 && float.TryParse(bits[1], out var m)) max = m;
				CameraRenderSettings.Me.SetHighlight(c, max);
				break;
			}
		}
	});
	private static DebugConsole.Command s_setAge = new DebugConsole.Command("setage", _s => {
		float age = float.Parse(_s);
		NGManager.Me.m_townAge = age;
	});
	private static DebugConsole.Command s_setAgeType = new DebugConsole.Command("setagetype", _s => {
		int type = int.Parse(_s);
		NGManager.Me.m_townAgeType = type;
	});
	public static void UpdateTownAge() {
		float age = NGManager.Me.m_townAge;
		int ageType = NGManager.Me.m_townAgeType;
		Vector4 ageUVs = Vector4.zero;
		ageUVs.x = (ageType & 1) * .5f;
		ageUVs.y = ((ageType >> 1) & 1) * .5f;
		foreach (var c in NGManager.Me.m_NGCommanderList) {
			if (c.m_currentAgeType != ageType || Mathf.Abs(c.m_currentAge - age) > .001f) { 
				c.m_currentAge = age;
				c.m_currentAgeType = ageType;
				foreach (var r in c.GetComponentsInChildren<MeshRenderer>()) {
					var mats = r.sharedMaterials;
					if (mats == null || mats.Length == 0) mats = r.materials;
					foreach (var m in mats) {
						if (m != null) {
							m.SetFloat("_Aging", age);
							m.SetVector("_UVOffsetAging", ageUVs); 
						}
					}
				}
			}
		}
	}

	public bool HasValidEntrance() => GetComponent<BuildingNav>()?.HasDoor ?? false;

	public bool IsFake()
	{
		var buildingNav = GetComponent<BuildingNav>();
		return buildingNav.IsFakeBuilding;
	}

	public Vector3 GetTempDoorOuterPos()
	{
		var nav = GetComponent<BuildingNav>();
		if (nav)
			return nav.DoorExterior;
		return transform.position;
	}
	private void AnalyseDesignChanges(bool isPainted, bool isDecorated)
    {
    }

	public void InitialiseBuildingData(GameState_Building _data, GameObject _holder, GameObject _visuals, bool _redesign = false)
	{
		m_visuals = _visuals.transform;
		SetWildblockIDs();
		UpdateBuildSign();
		if ((BuildingPlacementManager.c_newBuildingPlacementScheme && (_data.m_buildingDesign?.HasValidDesign ?? false)) ||  _data.m_removeBaseVisuals) SwitchOnBedrockType.DisableAll(_visuals);
	}

	Block m_currentConstructionBlock = null;

	private BaseBlock m_baseBlock;
	private void UpdateBuildSign()
	{
		if (Utility.IsShuttingDown) return;
		if (Visuals == null) return;
		if (m_baseBlock == null) m_baseBlock = Visuals?.GetComponentInChildren<BaseBlock>(); 
		bool show = IsLocked == false && GameManager.Me.IsDesignTable == false && Visuals.childCount == 1 && InOwnedDistrictState == EInOwnedDistrictState.InOwnedDistrict;
		if (m_baseBlock != null) m_baseBlock.ShowEmptyPlotSign(show);
	}
	
	public void AddPlantController()
	{
		m_plantController = gameObject.AddComponent<PlantController>();
	}

	PlantController m_plantController;
	void UpdatePlants()
	{
		if (m_plantController == null) return;
		m_plantController.SetLevel(Mathf.Lerp(m_plantController.m_plantLevel, m_stateData.m_plantLevel, .1f), m_stateData.m_plantLevel);
	}
	
	public bool IsDisabledByPlants => m_stateData.m_plantLevel > 0;

	public void PlantClick()
	{
		if (m_stateData.m_plantLevel <= 0) return;
		const float c_plantReducePerClick = .2f;
		m_stateData.m_plantLevel = Mathf.Clamp01(m_stateData.m_plantLevel - c_plantReducePerClick);
		PlantController.TriggerDestroyFX(Utility.mousePosition, m_stateData.m_plantLevel <= 0);
		
		if (m_stateData.m_plantLevel <= 0)
		{
			MABuilding building = this as MABuilding;
			if(building != null)
			{
				var actionTitle = building.GetActionComponentBasedTitle();
				Utility.ShowObjectMessagePosition(actionTitle, building.GetCentalPosition(), gameObject, 5, null, true);
			}
			OnPlantDestroyed();
		}
	}
	protected virtual void OnPlantDestroyed() { }

	private static float s_constructionOverride = -1;
	private static DebugConsole.Command s_setConstructionOverride = new DebugConsole.Command("overrideconstruction", _s => s_constructionOverride = float.Parse(_s));
	void UpdateConstruction() {
	}

	public Vector3 CenterPosition(float _upFactor = 0.5f)
	{
		if (this == null)
			return Vector3.zero;

		Vector3 center = Vector3.zero;
		int count = 0;
		float minY = 0f, maxY = 0f;
		if (m_visuals != null)
		{
			foreach (var mc in m_visuals.GetComponentsInChildren<MeshCollider>())
			{
				maxY = count == 0 ? mc.bounds.max.y : Mathf.Max(maxY, mc.bounds.max.y);
				minY = count == 0 ? mc.bounds.min.y : Mathf.Min(minY, mc.bounds.min.y);
				center += mc.bounds.center;
				count++;
			}
		}
		if(count > 0)
		{
			return new Vector3(center.x/(float)count, minY + (maxY - minY)*_upFactor, center.z/(float)count);
		}
		return transform.position;
	}
	
	private struct ColliderState
	{
		public bool m_enabled;
		public bool m_isTrigger;
	}
	
	private Dictionary<MeshCollider, ColliderState> m_prefabMeshColliderStateBackup = null;
	private void EnableColliders(GameObject _visuals, bool _enable) { //rather than toggling all colliders on/off we make sure we switch them to the state defined in the prefab
		if (!_enable || m_prefabMeshColliderStateBackup == null) m_prefabMeshColliderStateBackup = new Dictionary<MeshCollider, ColliderState>();
		bool foundActiveMeshCollider = false;
		foreach (MeshCollider mc in _visuals.GetComponentsInChildren<MeshCollider>()) {
			if (!_enable) {
				m_prefabMeshColliderStateBackup[mc] = new ColliderState() { m_enabled = mc.enabled, m_isTrigger = mc.isTrigger };
				mc.enabled = _enable;
			}
			else {
				if (m_prefabMeshColliderStateBackup.TryGetValue(mc, out ColliderState _enabledState))
				{
					mc.enabled = _enabledState.m_enabled; //revert to prefab state
					mc.isTrigger = _enabledState.m_isTrigger;
				}
				else mc.enabled = _enable;
			}
			foundActiveMeshCollider |= mc.enabled && !mc.isTrigger;
		}
		foreach (var bc in _visuals.GetComponentsInChildren<BoxCollider>()) {
			DesignTableManager.TryToggleBoxCollider(_enable, bc, !foundActiveMeshCollider);
		}
	}

	public void RefreshPaths(bool _visualsOnly)
	{
		//var (min, max) = GetExtents();
		//RoadManager.Me.m_pathSet.CreateVisuals(min, max, null, _visualsOnly); // no longer needed since base blocks are always present
		RefreshTopmost();
	}

	public void RefreshTopmost()
	{
		var top = Vector3.one * -1e23f;
		var allCurrentTop = top;
		int topCount = 1;
		// If a point is above or below by this amount, we take the average of all points within this range
		const float minHeightCheck = 0.1f;
		var blocks = GetComponentsInChildren<Block>();
		foreach(var block in blocks)
		{
			foreach(var r in block.m_toVisuals.GetComponentsInChildren<Renderer>())
			{
				if(r as VFXRenderer != null || r as ParticleSystemRenderer)
					continue;
				var thisTop = r.bounds.center + Vector3.up * r.bounds.extents.y;
				float delta = thisTop.y - top.y;
				if (delta > minHeightCheck)
				{
					top = thisTop;
					allCurrentTop = top;
					topCount = 1;
				}
				else if(Mathf.Abs(delta) < minHeightCheck)
				{
					allCurrentTop += thisTop;
					++topCount;
				}
			}
		}
		
		top = allCurrentTop / topCount;
		if (top.y < -1e22f) top = transform.position;
		LocalHighestPoint = top;
		if (m_balloonHolder != null)
			m_balloonHolder.position = LocalHighestPoint;
	}
	
	
	//===============  CRANE ===============

	private CraneHandler m_inputCrane;
	public CraneHandler InputCrane => m_inputCrane;
	private CraneHandler m_outputCrane;
	public CraneHandler OutputCrane => m_outputCrane;

	void CreateCranes()
	{
		if (m_inputCrane != null) m_inputCrane.Shutdown();
		m_inputCrane = new CraneHandler(gameObject, CraneHandler.c_inputBlock);
		if (m_outputCrane != null) m_outputCrane.Shutdown();
		m_outputCrane = new CraneHandler(gameObject, CraneHandler.c_outputBlock);
	}

	void UpdateCranes()
	{
		if (m_inputCrane != null)
		{
			m_inputCrane.Update();
		}
		if (m_outputCrane != null)
		{
			m_outputCrane.Update();
			if (m_outputCrane.CraneAvailable)
			{
				if (m_outputCrane.HasContent)
				{
					m_outputCrane.CraneTransportHeldToConveyor(_b => { });
				}
			}
		}
	}

	public bool InputCranePickup(System.Action<bool> _cb)
	{
		if (m_inputCrane == null) return false;
		if (m_inputCrane.CraneAvailable == false) return false;
		return m_inputCrane.CranePickup(_cb);
	}

	/*public bool OutputCraneDropoff(System.Action<bool> _cb)
	{
		if (m_outputCrane == null) return false;
		if (m_outputCrane.CraneAvailable == false) return false;
		//if (m_outputCrane.HasSpace == false && m_outputCrane.DoesConveyorAttachHaveSpaceFor(OutputIs)) return false;
		var pickup = GetDragContent(false);
		if (pickup == null) return false;
		return m_outputCrane.CraneDropoff(pickup, _cb);
	}*/

	//===============  ----- ===============	

	public void OnDesignChange()
	{
		GetComponent<BuildingNav>().UpdateNavigationData(0,0);
		InvalidateHighestPointCache();
		if (Visuals != null) SwitchOnBedrockType.DisableAll(Visuals.gameObject,  (BuildingPlacementManager.c_newBuildingPlacementScheme && m_stateData.m_buildingDesign.HasValidDesign) || m_stateData.m_removeBaseVisuals);
	}

	public void RefreshWildBlockStore()
	{
		if (m_stateData.m_lastWildBlocks == null)
			m_stateData.m_lastWildBlocks = new List<int>();
		else
			m_stateData.m_lastWildBlocks.Clear();
		var blocks = Visuals.GetComponentsInChildren<Block>();
		for (int i = 0; i < blocks.Length; ++i)
			if (blocks[i].m_lastWildBlockID != 0)
				m_stateData.m_lastWildBlocks.Add((blocks[i].m_lastWildBlockID << 8) | blocks[i].m_indexInDesign);
	}
	
	public void SetWildblockIDs()
	{
		if (m_stateData.m_lastWildBlocks == null || m_stateData.m_lastWildBlocks.Count == 0) return;
		var blocks = Visuals.GetComponentsInChildren<Block>();
		Dictionary<int, Block> lookup = new();
		foreach (var block in blocks) lookup[block.m_indexInDesign] = block;
		foreach (var id in m_stateData.m_lastWildBlocks)
		{
			var blockIndex = id & 0xFF;
			var wildIndex = id >> 8;
			if (lookup.TryGetValue(blockIndex, out var thisBlock))
				thisBlock.m_lastWildBlockID = wildIndex;
		}
	}

	virtual public void RefreshDesignDetails() { }
	virtual public void SetProduct(Product _product) { }
	virtual public void SetVOResourceSoundSwitch() { }
	virtual public bool CanHaveWorkersOrDeliveries() => false;
	
	#endregion Functions
}
