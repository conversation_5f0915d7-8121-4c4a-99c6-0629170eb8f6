using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using TMPro;
using DG.Tweening;

public class TooPoorIndicator : MonoBehaviour
{
	public Vector2 startPos;
	public Vector2 midPos;
	public Vector2 endPos;
	public TMP_Text m_title;
	public TextMeshProUGUI m_amount;
	private float m_cost;
	private float m_dieTime = 0;
	private bool m_endAnimStarted = false;
	public GameObject m_infoHolder;
	
	public void Activate(string _title)
	{
		m_title.text = _title;
		m_infoHolder.SetActive(false);
		RefreshDieTime();
	}
	
	public void Activate(string _title, float _cost)
	{
		if (m_dieTime == 0)
			StartAnim();
		m_title.text = _title;
		m_cost = _cost;
		RefreshDieTime();
		SetText();        
	}

	public void RefreshDieTime()
	{
		m_dieTime = Time.time + 2f;
	}
	
	private void StartAnim()
	{
		AudioClipManager.Me.PlayUISound("PlaySound_NotEnoughMoneyWarning");
		DOTweenAnimation dota = Instantiate(DoAnimationManager.Me.m_flyToCentre, DoAnimationManager.Me.transform);
		dota.targetIsSelf = false;
		dota.targetGO = gameObject;
		dota.target = GetComponent<RectTransform>();
		dota.targetType = DOTweenAnimation.TargetType.RectTransform;
		GetComponent<RectTransform>().anchoredPosition = startPos;
		dota.endValueV3 = midPos;
		dota.CreateTween();
		StartCoroutine(DestroyAfter(dota));
	}

	private void EndAnim()
	{
		DOTweenAnimation dota = Instantiate(DoAnimationManager.Me.m_flyFromCentre, DoAnimationManager.Me.transform);
		dota.targetIsSelf = false;
		dota.targetGO = gameObject;
		dota.target = GetComponent<RectTransform>();
		dota.targetType = DOTweenAnimation.TargetType.RectTransform;
		dota.endValueV3 = endPos;
		dota.CreateTween();
		StartCoroutine(DestroyAfter(dota, true));
	}

	private IEnumerator DestroyAfter(DOTweenAnimation _anim, bool _destroyObject = false)
	{
		float endTime = _anim.duration + Time.time;
		while (Time.time < endTime && GameManager.Me.CurrentCanvas.gameObject.activeInHierarchy)
        {
			yield return null;
		}
		Destroy(_anim.gameObject);
		_anim.gameObject.SetActive(false);
		if (_destroyObject)
		{
			Destroy(gameObject);
			gameObject.SetActive(false);
		}
	}

	private void SetText()
	{
		m_amount.text = GlobalData.CurrencySymbol + m_cost.ToString("0.00");
	}

	private void Update()
	{
		SetText();
		if(Time.time > m_dieTime && !m_endAnimStarted)
		{
			m_endAnimStarted = true;
			EndAnim();
		}
	}

	private float GetRemainingMoney()
	{
		return m_cost - NGPlayer.Me.m_cash.Balance;
	}
}
