using System;
using System.Collections.Generic;
using Cans.Analytics;
using UnityEngine;
using SaveContainers;
using Random = UnityEngine.Random;

[System.Serializable]
    public class NGProductInfo
    {
        public const string c_food = "Food";
        public const string c_weapons = "Weapons";
        public const string c_ammo = "Ammo";
        public const string c_projectile = "Projectile";
        public const string c_clothingMale = "Clothing";
        public const string c_clothingFemale = "Clothing Female";
        public const string c_armourMale = "Armour";
        public const string c_armourFemale = "Armour Female";
        
        public string id;
        public bool m_debugChanged;
        public static List<NGProductInfo> s_productInfos = new List<NGProductInfo>();
        public static List<NGProductInfo> GetList => s_productInfos;
        public string DebugDisplayName => m_prefabName;
        public string m_prefabName ="";
        [ScanField] public string m_materialsUsed;
        [ScanField] public string m_fallbackMaterial;
        public string m_title;
        public string m_titleSingular;
        public List<NGCarriableResource> MaterialsUsed = new ();
        public NGCarriableResource FallbackMaterial = null;

        public static Dictionary<string, NGProductInfo> s_allProducts = new Dictionary<string, NGProductInfo>();

        public List<NGCarriableResource> GetMaterialsUsed()
        {
            var result = new List<NGCarriableResource>();
            if(string.IsNullOrEmpty(m_materialsUsed))
                return result;
            foreach (var c in m_materialsUsed.Split(';', '|', '\n', ','))
            {
                var res = NGCarriableResource.GetInfo(c);
                if(res != null)
                    result.Add(res);
            }
            return result;
        }
        
        public static NGProductInfo GetRandomFromUnlocked()
        {
            var unlockedLines = new List<NGProductInfo>();
            foreach(var p in s_allProducts)
            {
                unlockedLines.Add(p.Value);
            }
            if(unlockedLines.Count == 0) return null;
            var index = Random.Range(0, unlockedLines.Count);
            return unlockedLines[index];
        }

        public static NGProductInfo GetInfo(string _name)
        {
            if (s_allProducts.ContainsKey(_name))
                return s_allProducts[_name];
            return null;
        }

        public void ConvertToProductLineMaterials(List<NGCarriableResource> _resources)
        {
            if(FallbackMaterial == null) return;
            
            for(int i = _resources.Count-1; i >= 0; --i)
            {
                if(!MaterialsUsed.Contains(_resources[i]))
                {
                    _resources.RemoveAt(i);
                }
            }
            if(_resources.Count == 0)
            {
                _resources.Add(FallbackMaterial);
            }
        }
        
        public static bool PostImportARecord(NGProductInfo _what)
        {
            if (s_allProducts.ContainsKey(_what.m_prefabName) == false)
                s_allProducts.Add(_what.m_prefabName, _what);
                
            _what.MaterialsUsed = _what.GetMaterialsUsed();
            if(!string.IsNullOrEmpty(_what.m_fallbackMaterial))
                NGCarriableResource.s_carriableResources.TryGetValue(_what.m_fallbackMaterial, out _what.FallbackMaterial);
            return true;
        }
        
        public static List<NGProductInfo> LoadInfo()
        {
            s_productInfos = NGKnack.ImportKnackInto<NGProductInfo>(PostImportARecord);

            BlockBalanceManager.ReadPartTypeData();
            return s_productInfos;
        }

        public static void ShowUnlockedChoiceUI(Action<string> _onConfirm = null, string _pl = "")
        {
        }
        static DebugConsole.Command s_chooseProductLine = new DebugConsole.Command("chooseproduct", _s => { s_showAllProductLines = true; Utility.SetOrToggle(ref s_showAllProductLines, _s); ShowChoiceUI((productLineChosen) => {}, false, true); });
        static bool s_showAllProductLines = false;
        //--------------------------------------------------------------------------------------------------
        public static void ShowChoiceUI(Action<string> _onConfirm = null, bool _isFirstUnlock = false, bool _isDebug = false)
        {
        }
        
        private static void SendProductSelectionAnalyticsEvent(string _nameProductLineChosen, List<int> _dataBaseIndexedAvailableProducts, string _action)
        {
            if(GameManager.IsVisitingInProgress == false)
            {
                string unlockableIndices = string.Join(';',
                    _dataBaseIndexedAvailableProducts.ConvertToStringList((prod) => prod.ToString()));
                AnalyticsEvent productChoiceEvent = AnalyticsManager.Me.Events.ProductSelectionInteraction;
                productChoiceEvent.AddParameter(EventParams.productSelectionProduct, _nameProductLineChosen);
                productChoiceEvent.AddParameter(EventParams.productSelectionInteraction, _action);
                productChoiceEvent.AddParameter(EventParams.productSelectionAvailableProducts, unlockableIndices);
                AnalyticsManager.Me.LogEvent(productChoiceEvent);
            }
        }
        
        public static bool IsAProductLine( string _productLine )
        {
            return s_allProducts.ContainsKey(_productLine);
        }
        
        //--------------------------------------------------    ------------------------------------------------
        // Save & Load
        //--------------------------------------------------------------------------------------------------
        public static void Save(ref SaveGame _save)
        {
        }
        //--------------------------------------------------------------------------------------------------
        public static void Load(SaveGame _load)
        {
            
        }
    }