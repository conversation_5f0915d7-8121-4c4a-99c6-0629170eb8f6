using System;
using System.Collections;
using System.Collections.Generic;
using NaughtyAttributes;
using UnityEngine;
using UnityEngine.EventSystems;

#if UNITY_EDITOR
using System.Linq;
using UnityEditor;
#endif

public interface IDamageReceiver
{
	[Flags]
	public enum DamageSource : int
	{
		None = 0,
		AI = 1 << 0,
		HandPower = 1 << 1,
		ThrownByHand = 1 << 2,
		Possession = 1 << 3,
		ThrownObject = 1 << 4,
		Debug = 1 << 5,
		// Building
		Turret = 1 << 6,
		Wall = 1 << 7,
		// Character
		Hero = 1 << 8,
		Worker = 1 << 9,
		Worshipper = 1 << 10,
		Zombie = 1 << 11,
		Undead = 1 << 12,
		Bandit = 1 << 13,
	}

	public static DamageSource GetSourceFromCharacter(MACharacterBase ch)
	{
		if (ch == null)
			return DamageSource.None;
		
		var source = ch.IsPossessed ? DamageSource.Possession : DamageSource.AI;
		if (ch is MAHeroBase)
			source |= DamageSource.Hero;
		else if (ch is MAWorker)
			source |= DamageSource.Worker;
		// else if (ch is MAWorshipper)
		// 	source |= DamageSourceasd.Worshipper;
		else if (ch is MAZombie)
			source |= DamageSource.Zombie;
		else if (ch is MAUndead)
			source |= DamageSource.Undead;
		else if ((ch is MABanditMelee) || (ch is MABanditRanged))
			source |= DamageSource.Bandit;
		
		return source;
	}

	public static DamageSource GetSourceFromBCAction(BCActionBase bc)
	{
		if (bc == null)
			return DamageSource.None;
		
		var source = (GameManager.Me.PossessedObject == bc.gameObject) ? DamageSource.Possession : DamageSource.AI;
		if (bc is BCActionTurret)
			source |= DamageSource.Turret;
		
		return source;
	}
	
	void ApplyDamageEffect(DamageSource source, float _damageDone, Vector3 _sourceOfDamage, MAAttackInstance attack = null, MAHandPowerInfo handPower = null);

	public HashSet<IDamager> TargettedBy { get; }
	public bool CanBeTargeted { get; }
	public int TargetPriority { get; set; }
	public MAMovingInfoBase GetMovingInfo();
	public Transform Transform { get; }
}

public class NGMovingObject : NGObjectBase, IDamageReceiver, IStrikePoint
{
	#region Constants
	
	public const string c_bodyToBodyCollisionLayer = "BodyToBodyCollider";
	public const string NGWife = "Wife";
	public const string Foreman = "Foreman";
	public const string NGWorker = "NGWorker";
	private const float RotationSpeed = 5;
	protected const string m_pickUpPersonSound = "PickupPersonSwipe";
	protected const string m_assignJobSound = "AssignJob";
	protected const string m_throwPersonSound = "ThrowPerson";
	public bool m_debugBreakHere;

	public const bool UsePhysicsForPickup = true;

	[NonSerialized] public bool m_isFromNFT = false;
	
	protected bool m_stoppedForBuildingPlacement = false;
	
	protected FollowChild m_followChild = null;
	public void SetFollow(FollowChild _followChild)
	{
		m_followChild = _followChild;
		SetupFollowLeaderState(_followChild != null);
	}
	
	public void SetFollowBeingControlled(bool _on)
	{
		if (m_followChild != null) m_followChild.SetBeingControlled(_on);
	}
	
	virtual public void SetupFollowLeaderState(bool _on) { }

	public enum EFollowLeaderState
	{
		None,
		Following,
		FollowingWithFreeWill,
	}

	public bool IsFollowLeader => m_followChild != null;
	public EFollowLeaderState FollowLeaderState => m_followChild == null ? EFollowLeaderState.None : m_followChild.IsInsideFreeWillRange ? EFollowLeaderState.FollowingWithFreeWill : EFollowLeaderState.Following;
	public virtual bool CheckFollowLeader()
	{
		if (m_followChild == null) return false;
		m_followChild.RunNav();
		return true;
	}

	public bool CheckFollowLeaderTargetViable(Vector3 _pos)
	{
		if (m_followChild == null) return true;
		return m_followChild.IsTargetViable(_pos);
	}

	Vector3 IStrikePoint.StrikePoint => transform.position + Vector3.up * 1.5f;
	
	public MAWorkerInfo.WorkerGender Gender
	{
		get
		{
			var gsPerson = GameState as GameState_Person;
			if(gsPerson == null || gsPerson.m_isMale)
				return MAWorkerInfo.WorkerGender.Male;
			return MAWorkerInfo.WorkerGender.Female;
		}
	}
	
	#endregion Constants
	
	#region Enums
	
	public enum STATE
	{
		NONE = -1,
		IDLE,
		MOVE_TO_POSITION,
		PICKUP_DESTINATION_OBJECT,
		MOVE_WITHIN_COMMANDER,
		IN_COMMANDER_AVAILABLE,
		IN_COMMANDER_UNAVAILABLE,
		HELD_BY_PLAYER,
		THROWN_BY_PLAYER,
		DROPPED_BY_PLAYER,
		MA_DEAD,
		DESTROY_ME,
		WORKING,
		RESTING,
		HIDING,
		BLOCKED_BY_BUILDING,
		IN_SERVICE_BUILDING, //e.g. NGBank. Anu building the movingObj isn't 'working' in but can be inside of. See usage in NGWorker::SetupFromLoad
		MA_MOVE_TO_BUILDING,
		MA_MOVE_TO_INSIDE_BUILDING,
		MA_MOVE_TO_OUTSIDE_BUILDING,
		MA_MOVE_TO_POSITION,
		MA_MOVE_TO_OBJECT,
		MA_WAITING_FOR_WORK,
		MA_WAITING_FOR_HOME,
		MA_PETRIFIED,
		MA_LOADING,
		MA_DECIDE_WHAT_TO_DO,
		MA_WAITING_FOR_ANIMATION,
		MA_LEAVING,
		MA_CHALLANGE,
		MA_CHOP_OBJECT,
		MA_CHASE_OBJECT,
		MA_HANGOUT,
		MA_IN_PILLORY_STOCKS,
		MA_IN_GALLOWS,
		MA_ANIMATOR_CONTROLLED,
		MA_FOLLOW_TRANSFORM,
		MA_POSSESSED,
		MA_WAITING_TO_TELEPORT,
		MA_FOLLOW_LEADER,
		LAST
	}
	
	public enum MWCPurpose
	{
		NONE=0,
		// Used to arrive at the outer door of a commander. Will call ObjectArrived when the move is complete.
		ARRIVE,
		// Used to go to a commander's inner door position. Will call ObjectArrivedInside when move is complete.
		// Should only be called by a commander to pull an object inside of it. Should only be used for objects which have already arrived.
		ARRIVE_INSIDE,
		// Used for objects which have already arrived at their commander and are just making a positional move. Objects will be set to 
		// IN_COMMANDER_AVAILABLE when the move is complete. Could be used for an object to move from the inner door to the outer door 
		// to wait on the porch during build time.
		OTHER
	}
	
	public enum COLLISIONSTYLE
	{
		DEFAULT, //Dropped on the floor
		TRIGGER, //Being thrown at a factory, so that it can only collide with the target factory
		KINEMATIC, //Being held by the player, so that it still collides but is held in place
		NOPHYSICS, //Being held by a worker, all movement is animated
	}
	
	public enum AnimationTransformMatchType
	{
		None,
		LookAt,
		Match,
		MatchWithTimedRotation,
		Last
	};
	
	#endregion Enums
	
	#region Subclasses
	
	public class VisualContainer {
		public Transform HoldingTransform { get; set; }
		public AnimationHandler AnimHandler { get; set; }
	}
	
	#endregion Subclasses
	
	#region Static Properties
	
	protected static float lastTone = -1f;
	protected static int civCount = 0;
	protected static float[] toneRange = {
		0.0f, 0.75f,
		0.75f, 0.0f
	};
	protected static GameObject s_lastBeginDrag;

	private bool m_interactLatched = false;
	private float m_interactLatchedTime = 0.0f;
	private float m_interactLatchedCooldown = 2.0f;

	[NonSerialized]
	public List<Collider> m_mainColliders = new();
	[NonSerialized]
	public List<Collider> m_triggerColliders = new();
	
	public string m_contentRootName = "Target";
	public string m_headMainBoneName = "mixamorig:Head";
	public string m_headBoneName = "mixamorig:HeadTop_End";
	public string m_neckBoneName = "mixamorig:Neck";
	public string m_toeBoneName = "mixamorig:RightToeBase";

	#endregion Static Properties

	public string m_travelSound = "";
	public AkEventHolder m_travelSoundEvent;
	public AkRTPCHolder m_travelSpeedRTPC;
	int m_travelSoundID = 0;

	public float m_dragRaise = 0;
	
	virtual public bool CanEnterPossessionMode => GameState.m_blockPossession == false;
#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
	virtual public bool CanTransferPossessionToOther => GameManager.Me.PossessedCharacter != this && MAUnlocks.Me.m_possessedCanTransferPossession;
#else
	virtual public bool CanTransferPossessionToOther => GameManager.Me.PossessedCharacter != this;
#endif

	virtual public string HumanoidType => "NonHumanoid";
	public virtual Rigidbody RigidBody => null;

    private float m_oldForceThroughObstacle = 0.0f;

    public Animator m_anim = null;
    protected AnimationHandler m_animHandler = null;

    public Transform m_possessedCameraFocusOrigin = null;
    
    private static HashSet<string> s_layerErrors = new();
    
    private bool m_haveCheckedLoco = false;
    private bool m_hasStandardLoco = false;

    private Action<MAAnimationSet.AnimationParams, bool> m_onAnimationFinished = null;
    protected string m_lastAnimationClip = "";

    #region IDamageReceiver

    public virtual int TargetPriority { get; set; } = 1;
    public virtual bool CanBeTargeted { get { return true; } }
    public virtual MAMovingInfoBase GetMovingInfo() { return null; }
    public Transform Transform => m_transform;

    public HashSet<IDamager> TargettedBy => GetComponent<TargetObject>()?.TargetedBy ?? new();
    public HashSet<IDamager> TargetedByEnemies => GetComponent<TargetObject>()?.TargetedByEnemies ?? new();
    
    #endregion

    #region IDamager
    
    public virtual IDamageReceiver IsTargetting => null;
    public virtual void ClearTarget() { }
    
    #endregion
	
    #region Unity Messages

	protected virtual void Awake()
	{
		m_transform = transform;
		m_nav = GetComponentInChildren<NavAgent>();
		Visuals = new VisualContainer();

		m_pickupBehaviour = GetComponent<IPickupBehaviour>();
		if (gameObject.GetComponent<CameraSoundOcclusion>() == null)
		{
			gameObject.AddComponent<CameraSoundOcclusion>();
		}
		
		m_mainColliders.Clear();
		m_triggerColliders.Clear();
		
		Collider[] colliders = GetComponentsInChildren<Collider>(true);
		foreach(var col in colliders)
		{
			if(col.isTrigger == false)
			{
				m_mainColliders.Add(col);
			}
			else
			{
				m_triggerColliders.Add(col);
			}
		}
		
		m_allowedCharacterInteractionLayers = LayerMask.GetMask(new string[] { "Default", "MovingObject", "BodyToBodyCollider", "Pickup"});
		
		if(GameManager.Me)
			GameManager.Me.StartCoroutine(Co_SetHoldingTransform());
	}
	
	protected virtual void Start() { }

	public virtual void PostLoad()
	{
		GameState_MovingObject gameState = GameState;
		UpdateComponentReferences();
		if(Leader == null)
			Leader = NGManager.Me.m_MACharacterList.Find(x => x.m_ID == gameState.m_leaderId);
	}
	
	virtual public void UpdateComponentReferences()
	{
		GameState_MovingObject gameState = GameState;
		if(gameState.m_home == null)
			gameState.m_home = MABuilding.LookupComponent(GameState.m_homeComponentId);
		if(gameState.m_job == null)
			gameState.m_job = MABuilding.LookupComponent(GameState.m_jobComponentId);
	}
	
	protected virtual void SetHumanoidType()
	{
		AudioClipManager.Me.SetHumanoidType(this, HumanoidType);
	} 

	protected virtual bool CheckOutOfBounds()
	{
		var posMin = GlobalData.c_terrainMin;
		var posMax = GlobalData.c_terrainMax;
		
		var pos = transform.position;
		if (pos.x < posMin.x) { return true; }
		else if (pos.x > posMax.x) { return true; }
		if (pos.z < posMin.z) { return true; }
		else if (pos.z > posMax.z) { return true; }
		return false;
	}

	protected virtual void HandleOutOfBounds()
	{
		var posMin = GlobalData.c_terrainMin;
		var posMax = GlobalData.c_terrainMax;
		
		var pos = transform.position;
		if (pos.x < posMin.x) { pos.x = posMin.x; }
		else if (pos.x > posMax.x) { pos.x = posMax.x; }
		if (pos.z < posMin.z) { pos.z = posMin.z; }
		else if (pos.z > posMax.z) { pos.z = posMax.z; }
		transform.position = pos;
	}
	
	virtual protected void Update()
	{
		if (m_debugMe)
			Debug.Log("Debug Me :" + m_ID);
		CommonUpdateState();
		UpdateState();
		if (CheckOutOfBounds())
		{
			HandleOutOfBounds();
		}
		UpdateFallenOffMap();
		UpdateInteractLatchedCooldown();
		ClearVengeanceTargetIfInvalid();
	}
	
	virtual protected void LateUpdate() {
		UpdateGameState();
	}

	protected override void OnDisable()
	{
		if (GameState != null)
		{
			UpdateGameState();
		}
		base.OnDisable();
	}

	protected override void OnDestroy()
	{
		m_onPathProcessed = null;

		if (m_turningCoroutine != null)
		{
			StopCoroutine(m_turningCoroutine);
			m_turningCoroutine = null;
		}		

		base.OnDestroy();
	}
#endregion

	virtual protected void UpdateState()
	{
		switch (m_state)
		{
			case STATE.IDLE:
				StateIdle();
				break;
			case STATE.HELD_BY_PLAYER:
				StateHeldByPlayer();
				break;
			case STATE.THROWN_BY_PLAYER:
				StateThrownByPlayer();
				break;
			case STATE.DROPPED_BY_PLAYER:
				StateDroppedByPlayer();
				break;
			case STATE.MA_DEAD:
				StateDead();
				break;
			case STATE.DESTROY_ME:
				StateDestroyMe();
				break;
			case STATE.MA_FOLLOW_TRANSFORM:
				StateFollowTransform();
				break;
		}
#if TODO
		var currentInteraction = m_state == STATE.HELD_BY_PLAYER ? EInteractionType.HOLD : EInteractionType.DROP;
		m_p2cInstance.UpdateInteractionStates(currentInteraction);
#endif
		
	}
	
	virtual protected void CommonUpdateState(){ }

	void UpdateFallenOffMap()
	{return;
		var pos = transform.position;
		var oldPos = pos;
		const float c_margin = 4;
		if (pos.x < GlobalData.c_terrainOrigin.x + c_margin)
			pos.x = GlobalData.c_terrainOrigin.x + c_margin;
		if (pos.z < GlobalData.c_terrainOrigin.z + c_margin)
			pos.z = GlobalData.c_terrainOrigin.z + c_margin;
		if (pos.x > GlobalData.c_terrainOrigin.x + GlobalData.c_terrainExtent.x - c_margin)
			pos.x = GlobalData.c_terrainOrigin.x + GlobalData.c_terrainExtent.x - c_margin;
		if (pos.z > GlobalData.c_terrainOrigin.z + GlobalData.c_terrainExtent.z - c_margin)
			pos.z = GlobalData.c_terrainOrigin.z + GlobalData.c_terrainExtent.z - c_margin;
		if (Health > 0)
		{
			var groundPos = pos.GroundPosition();
			if (pos.y < groundPos.y - 10)
				pos.y = groundPos.y + 20;
		}
		if ((pos - oldPos).sqrMagnitude > .01f * .01f)
		{
			transform.position = pos;
			OnPositionForceReset(oldPos, pos);
		}
	}

	virtual protected void OnPositionForceReset(Vector3 _oldPosition, Vector3 _newPosition) { }

	#region Virtual Properties

	virtual public string HoldingTransformName => "HandAttach_L";

	private GameState_MovingObject m_gameStateBase = null;
	
	public virtual void SetGameStateSaveData(GameState_MovingObject _gameState)
	{
		m_gameStateBase = _gameState;
	}
	
	public virtual float Energy
	{
		get
		{
			if (m_gameStateBase == null)
				return 0f;

			return m_gameStateBase.m_energy;
		}
		set { }
	}
	public virtual float Stamina { get { return 0; } set { } }
	public virtual float Health
	{
		get
		{
			if (m_gameStateBase == null)
				return 0f;

			return m_gameStateBase.m_health;
		}
		set { }
	}
	public virtual float ArmourPoints
	{
		get
		{
			if (m_gameStateBase == null)
				return 0f;

			return m_gameStateBase.m_armour;
		}
		set { }
	}
	public virtual bool CanBeTargetted { get => true; set { } }
	public virtual bool WaitingForPay { get { return false; } set { } }
	public virtual float EnergyRestThreshold { get { return 0; } set { } }
	public virtual float EnergyWorkThreshold { get { return 0; } set { } }
	public virtual PeepActions PeepAction { get { return PeepActions.None; } set { } }

	public BCBase Job 
	{ 
		get => GameState.m_job;
		set 
		{
			GameState.m_job = value;
			GameState.m_jobComponentId = (GameState.m_job == null ? 0 : GameState.m_job.m_uid);
		}
	}
	
	public BCBase Home 
	{ 
		get => GameState.m_home;
		set 
		{
			GameState.m_home = value;
			GameState.m_homeComponentId = (GameState.m_home == null ? 0 : GameState.m_home.m_uid);
		}
	}
	
	[SerializeField]
	private NGMovingObject m_leader;
	public virtual NGMovingObject Leader
	{
		get
		{
			if (m_leader == null)
			{
				if(GameState.m_leaderId > -1)
				{
					int iL = NGManager.Me.m_MACharacterList.FindIndex(x => x.m_ID == GameState.m_leaderId);
					if(iL > -1) m_leader = NGManager.Me.m_MACharacterList[iL];
					else GameState.m_leaderId = -1;
				}
				return m_leader;
			}
			GameState.m_leaderId = m_leader.m_ID;
			return m_leader;
		}
		set 
		{
			m_leader = value;
			GameState.m_leaderId = (m_leader == null ? 0 : m_leader.m_ID);
		}
	}
	
	public virtual void DeallocateJob() { GameState.m_job = null; GameState.m_jobComponentId = 0; }
	public virtual void DeallocateHome() { GameState.m_home = null; GameState.m_homeComponentId = 0;}
	
	public void DeallocateJobAndHome()
	{
		DeallocateJob();
		DeallocateHome();
	}

	public virtual CameraRenderSettings.VisionMode VisionMode => CameraRenderSettings.Me.m_defaultVision;

	protected virtual float[] NavigationCosts => GlobalData.s_pedestrianCosts;
	protected virtual float NavigationCorridor => 0f;

	#endregion Virtual Properties
	
	#region Virtual Functions
	
	public bool CanReturn { get;set;}
	public NGCommanderBase DraggedFrom { get;set;}

	public virtual bool CanPickup() { return GameState.m_canPickupOverride ? GameState.m_canPickupValue : true; }

	public void SetCanPickup(bool _canPickup)
	{
		GameState.m_canPickupOverride = true;
		GameState.m_canPickupValue = _canPickup;
	}

	public virtual void FinishThrow() { }
	
	public virtual float GetBezierDistanceOverride(SpecialHandlingAction _action) => -1;
	
	virtual public void SetCollisionStyle(COLLISIONSTYLE _style)
	{
		m_collisionStyle = _style;
		
		var rigidBody = GetComponentInChildren<Rigidbody>();
		var colliders = GetComponentsInChildren<Collider>().FindAll(c =>
		{
			//Filters:
			return m_triggerColliders.Contains(c) == false && // collider shouldn't be one of the pre-designated trigger colliders (such as 'picking' trigger shapes)
			       (m_carrying == null || m_carrying == this || // unCarried objects can be their own carrier. Also happens for a few moments before being assigned to a carrier
			        c.transform.IsChildOf(m_carrying.transform) == false); // if this is a carrier (e.g. worker), do not apply the collision style to their carried object
		});

		bool isKinematic = false;
		bool isColliderEnabled = false;
		bool isColliderTrigger = false;
		
		switch (_style)
		{
			case COLLISIONSTYLE.DEFAULT:		
				isKinematic = false;
				isColliderEnabled = true;
				isColliderTrigger = false;
				break;
			case COLLISIONSTYLE.KINEMATIC:
				isKinematic = true;
				isColliderEnabled = true;
				isColliderTrigger = false;
				break;
			case COLLISIONSTYLE.NOPHYSICS:
				isKinematic = true;
				isColliderEnabled = false;
				isColliderTrigger = false;
				break;
			case COLLISIONSTYLE.TRIGGER:
				isKinematic = false;
				isColliderEnabled = true;
				isColliderTrigger = true;
				break;
		}
		
		if (rigidBody != null) rigidBody.isKinematic = isKinematic;
		
		foreach (var c in colliders)
		{
			c.enabled = isColliderEnabled;
			c.isTrigger = isColliderTrigger;
		}
	}
	
	public virtual bool SetDead()
	{
		Health = 0;
		bool stateChanged = false;
		if (m_state != STATE.MA_DEAD)
		{
			SetState(STATE.MA_DEAD);
			stateChanged = true;
		}

		if (GameManager.Me.IsPossessed(this))
		{
			// RW-03-APR-25: It's been requested that when the possessed character dies in a cave, there's a delay before the camera withdraws from the cave,
			// so the player has time to understand what's happening. As such, I delay unpossessing the character.
			if (GenericSubScene.Me && GenericSubScene.Me.IsActive)
			{
				 this.DoAfter(GameManager.Me.caveDeathUnpossessDelay, () =>  GenericSubScene.PossessedCharacterDied());
			}
			else
			{
				GameManager.Me.Unpossess();
			}
		}
	
		if(m_nav != null) m_nav.Pause(true, true);
		PeepAction = PeepActions.None;
		return stateChanged;
	}

	public HashSet<Collider> m_triggersOverlapping = new();
	
	protected virtual void OnTriggerEnter(Collider _other)
	{
		OnTrigger(_other);
	}
	
	protected virtual void OnTriggerStay(Collider _other)
	{
		OnTrigger(_other);
	}
	
	protected virtual void OnTriggerExit(Collider other)
	{
		m_triggersOverlapping.Remove(other);
	}
	
	protected virtual bool OnTrigger(Collider _other)
	{
		if (_other.gameObject.GetComponent<BuildingNavBlocker>() != null) return false;
		
		bool newOverlap = m_triggersOverlapping.Add(_other);
		
		 // Not found this to be necessery
		BCEntrance buildingEntrance = _other.gameObject.GetComponentInParent<BCEntrance>();
		if(buildingEntrance == null) return newOverlap;
		MABuilding building = buildingEntrance.GetComponentInParent<MABuilding>();
		if (building != null && building == m_destinationMABuilding && m_stoppedForBuildingPlacement == false)
		{
			switch(m_state)
			{
				case STATE.MA_MOVE_TO_BUILDING:
				// Not required as we will have already done the neccessery 
				//case STATE.MA_MOVE_TO_OUTSIDE_BUILDING:
				//case STATE.MA_MOVE_TO_INSIDE_BUILDING:
					MASetStateMoveIntoBuilding();
					break;
			}
		}
		
		return newOverlap;
	}
	
	public void StartTravelSound()
	{
		if (m_travelSoundID > 0)
			return;
		var audioID = m_travelSoundEvent?.Event(m_travelSound);
		if (string.IsNullOrEmpty(audioID))
			return;
		m_travelSoundID = AudioClipManager.Me.PlaySound(audioID, gameObject);
	}

	public void SetTravelSoundSpeed(float _speed)
	{
		if (m_travelSpeedRTPC != null) m_travelSpeedRTPC.Set(_speed, gameObject);
	}

	public void StopTravelSound()
	{
		if (m_travelSoundID == 0) return;
		AudioClipManager.Me.StopSound(m_travelSoundID, gameObject);
		m_travelSoundID = 0;
	}

	virtual protected bool StateMoveToPosition()
	{
		if (m_nav)
		{
			if (HasAgentArrived())
			{
				SetState(STATE.IDLE);
				StopTravelSound();
				return true;
			}
			return false;
		}

		float speed = NGManager.Me.m_defaultObjectSpeed * Time.deltaTime;
		Vector3 destinationPos = DestinationPosition;
		Vector3 new_pos = Vector3.MoveTowards(transform.localPosition, destinationPos, speed);
		transform.position = new_pos;
		LookAt(destinationPos);
		if(new_pos == destinationPos)
			return true;
		return false;
	}

	virtual protected bool HasAgentArrived()
	{
		return m_nav.TargetReached;
	}
	
	float m_backupDrag;
	public void SetDrag(float _drag)
	{
		m_backupDrag = m_nav.m_body.linearDamping;
		m_nav.m_body.linearDamping = _drag;
	}
	
	public void RestoreDrag()
	{
		m_nav.m_body.linearDamping = m_backupDrag;
	}


	public virtual float GetDesiredSpeed()
	{
		return 1;
	}

	virtual public void MASetAsPetrified(MACharacterBase _attacker, bool _dropCarried = true, bool _rotateToThreat = true) { }

	virtual public void MASetAsHangOut() { }
	
	virtual public void MASetStateMoveOutOfBuilding(Vector3 _startPosition)
	{
		transform.position = _startPosition;
		(m_insideMABuilding as MABuilding)?.SetEntranceCollidersDisabled(true, this);
		SetCustomGravity(false);
		m_mainColliders.FindAll(x => x.gameObject.layer == LayerMask.NameToLayer(c_bodyToBodyCollisionLayer)).ForEach(x => x.enabled = false);
		
		SetState(STATE.MA_MOVE_TO_OUTSIDE_BUILDING);
		m_nav.Speed = GetDesiredSpeed();
		m_nav.SetTarget(m_insideMABuilding.DoorPosOuter, true);
	}
    
	virtual public void MASetStateMoveIntoBuilding()
	{
		if(m_state == STATE.MA_DEAD || m_state == STATE.MA_MOVE_TO_INSIDE_BUILDING) return;
		m_insideMABuilding = m_destinationMABuilding;
		(m_insideMABuilding as MABuilding)?.SetEntranceCollidersDisabled(true, this);
		
		m_mainColliders.FindAll(x => x.gameObject.layer == LayerMask.NameToLayer(c_bodyToBodyCollisionLayer)).ForEach(x => x.enabled = false);

		SetCustomGravity(false);
		m_nav.Speed = GetDesiredSpeed();
		//m_nav.SetTarget(m_insideMABuilding.DoorPosInner, true);
		SetState(STATE.MA_MOVE_TO_INSIDE_BUILDING);
	}
	
	public virtual void SetCustomGravity(bool _active)
	{
	}

	protected void OnPathToBuildingReady(NavAgent.NavInfo _navInfoResult)
	{
		m_nav.AddLastPos(m_destinationMABuilding.DoorPosInner);
	}
	
	protected void OnPathToObjectReady(NavAgent.NavInfo _navInfoResult)
	{
		
	}
	
	protected void OnPathToPositionReady(NavAgent.NavInfo _navInfoResult)
	{
		
	}

	virtual public void MASetAsHiding(bool _isVisibleInside)
	{
		PeepAction = PeepActions.Flee;
		SetState(STATE.HIDING);
		gameObject.SetActive(_isVisibleInside);
	}

	public virtual bool SetMoveToComponent(BCBase _component, PeepActions _action = PeepActions.None)
	{
		if(_component == null)
		{
			Debug.LogError($"Cant move to null component with action {_action.ToString()}");
			return false;
		}
		
		if(_component.Building != null)
			return SetMoveToBuilding(_component.Building, _action);
		
		if(_action == PeepActions.ReturnToWork) _action = PeepActions.WaitingForWork;
		else if(_action == PeepActions.ReturnToRest) _action = PeepActions.WaitingForHome;
		
		return SetMoveToPosition(_component.GetDoorPos(), false, _action);
	}
	
	public virtual bool SetMoveToPosition(Vector3 _position, bool _direct = false, PeepActions _action = PeepActions.None, float _destinationRadius = 0f)
	{
		return true;
	}
	
	public virtual bool SetMoveToBuilding(NGCommanderBase _building, PeepActions _action = PeepActions.None)
	{
		return true;
	}

	virtual public void NGSetAsWorking(bool _isVisibleInside)
	{/*
		if (MyJob == null && m_temporaryJob == null)
		{
			PeepAction = PeepActions.Idle;
			SetState(STATE.IDLE);
			return;
		}
		PeepAction = PeepActions.Working;
		SetState(STATE.WORKING);
		gameObject.SetActive(_isVisibleInside);*/
	}
	
	virtual public void NGSetAsResting(bool _isVisibleInside = false)
	{
		PeepAction = PeepActions.Resting;
		SetState(STATE.RESTING);
	}
	
	virtual public void SetStopAction(bool _stop) { }

	public void SetHeldMode(bool _held)
	{
		SetObjectHeld(_held);
	}

	public void TrySetTeleportPosition()
	{
#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)	
		var pos = transform.position;
		if (DistrictManager.Me.IsWithinDistrictBounds(pos) && GameState != null)
		{
			GameState.m_teleportBackTo = pos;
		}
#endif //!(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
	}

	public void PrepareForDropInGrey()
	{
		TrySetTeleportPosition();
	}

	protected virtual void SetTeleportingState()
	{
		
	}

	public bool CheckDropInGrey()
	{
		if (DistrictManager.Me.IsWithinDistrictBounds(transform.position) == false)
		{
			SetTeleportingState();
			return true;
		}
		return false;
	}

	public IEnumerator Co_HandleDropInGrey()
	{
		yield return new WaitForSeconds(.8f);
		if (NGManager.Me.m_teleportStartPrefab != null)
		{
			var teleportStart = Instantiate(NGManager.Me.m_teleportStartPrefab, transform.position, Quaternion.identity);
			Destroy(teleportStart, 1f);
		}
		yield return new WaitForSeconds(.2f);
		var rc = GetComponentInChildren<RagdollController>();
		if (rc != null)
			rc.StartTeleportation(GameState.m_teleportBackTo);
		gameObject.SetActive(false);
		transform.position = GameState.m_teleportBackTo;
		yield return new WaitForSeconds(.5f);
		if (NGManager.Me.m_teleportEndPrefab != null)
		{
			var teleportEnd = Instantiate(NGManager.Me.m_teleportEndPrefab, transform.position, Quaternion.identity);
			Destroy(teleportEnd, 1f);
		}
		yield return new WaitForSeconds(.2f);
		if (rc != null)
			rc.EndTeleportation();
		gameObject.SetActive(true);
	}

	protected virtual void OnTeleportEnded()
	{
		
	}

	virtual public void SetupPossessed(bool _possessedOn) { }

	virtual public bool SetupHeldByPlayer(NGCommanderBase _originator) {
		
		//TS - warning, we generally don't reach this old code anymore unless we have a CharacterPickupBehaviour 
		//on a NGMovingObject with no proper override. Not Guaranteed to work, might be enough for tests/debug
		//at time of writing see MAWorker override for more info.
		if(m_state != STATE.MA_DEAD)
			RagdollHelper.CancelRagdoll(gameObject);
		
		m_originator = _originator;
		if (m_insideMABuilding)
		{
			m_insideMABuilding.SetWorkerPickup(this);
			m_insideMABuilding = null;
		}
		m_insideMABuilding = null;
		SetState(STATE.HELD_BY_PLAYER);
		BlobShadowDisplay(m_blobShadow, false);
		//AudioClipManager.Me.PlaySound(m_pickUpPersonSound, transform);
		StopAgent();
		
		return	true;
	}

	protected Transform m_followTransform = null;
	virtual public void SetStateFollowTransform(Transform _followTransform)
    {
		m_followTransform = _followTransform;
		SetState(STATE.MA_FOLLOW_TRANSFORM);
	}
	
	virtual public void ShakenFromHand(Vector3 _returnPos) 
	{
		//AudioClipManager.Me.PlaySound("DropPerson", transform);
		transform.position = _returnPos;
		PlaySingleAnimation("WorkerBrushOff", (c) => { });
		transform.rotation = Quaternion.identity;
	}
	
	virtual public float GetReturnToBuildingPower() { return 1f; }
	public bool UndoDrag()
	{
		if (m_originator == null)
			return false;

		m_originator.AcceptThrownDrop(this);
		return true;
	}
	virtual public void OnBeginDrag(PointerEventData _eventData) {}
	virtual public void FinallyDroppedByPlayer(NGCommanderBase _commander, SpecialHandlingAction _restrictedAction) {}
	virtual public void FinallyDroppedByPlayer(MADecorationActionBase _dab, SpecialHandlingAction _restrictedAction) {}
	virtual	public void SetBodyColor(int _material_index) {}
	virtual public void SetSkinColor(float _gradValue) {}
	virtual public void SetHairColor(float _gradValue) {}
	virtual public void StopAgent() {}
	virtual public void ShowInHandDetails(GameObject _commander, NGMovingObject _object) { }
	virtual public bool IsPeep => false;
	virtual public void ShakenFromHand() { }
	virtual public bool IsOkayToCollide(NGCommanderBase _object) { return false; }
	virtual public void HasCollidedWith(NGCommanderBase _what) { }
	virtual public float GetDropScore(string _name) { return 0f;  }
	public virtual void OnPickUp() {}
	public virtual float PickupThrowThresholdMultiplier() { return 1f; }
	virtual public void ThrownByPlayer(Vector3 _throwVelocity) {}
	virtual public bool DroppedByPlayerAt(NGCommanderBase _hitCommander) { return true; }
	
	virtual public void SetSpeed(float _speed) { if (m_nav != null) m_nav.Speed = _speed; }

	override public void DestroyMe()
	{
		NGManager.Me.RemoveObject(this, false);
		base.DestroyMe();
	}
	
	#endregion Virtual Functions

	#region Properties & Fields

	[Header("NGMovingObject")]
	public STATE m_state = STATE.NONE;

	[SerializeField]
	protected MAAnimationSet m_animationSet = null;
	
	[SerializeField] 
	public Collider m_dedicatedPickupTriggerCollider = null;

	[ReadOnlyInspector] public NGCommanderBase m_insideMABuilding;
	[ReadOnlyInspector] public NGCommanderBase m_destinationMABuilding;
	
	[ReadOnlyInspector] public string m_givenName;
	[ReadOnlyInspector] public string m_workerName;

	[NonSerialized] public float m_destinationRemainingDistance;
	
	[ReadOnlyInspector] public ReactPickup m_cleaningPickup;

	[ReadOnlyInspector] public Transform m_transform;
	[ReadOnlyInspector] public VisualContainer Visuals;
	[NonSerialized] public Transform m_blobShadow;
	[ReadOnlyInspector] public NGCommanderBase m_commander;
	[NonSerialized] public NavAgent m_nav;
	[ReadOnlyInspector] public COLLISIONSTYLE m_collisionStyle;
	//[ReadOnlyInspector] public int m_stuckCount;
	[ReadOnlyInspector] public Transform m_destinationObject;
	[ReadOnlyInspector] public bool m_turningToFaceAway;
	public float m_matchTransformRotateSecs = 0.5f;
	public bool m_debugSelectedForWindow;

	protected ReactPickup m_carrying;
	//protected float m_forceThroughObstacle = 0;
	protected float m_timePathRequested = 0f;
	public Action<NGMovingObject.TargetReachability> m_onPathProcessed = null;
	protected Func<NGMovingObject, IEnumerator> m_arrivalAction;
	
	IPickupBehaviour m_pickupBehaviour = null;
	public bool IsHeld => m_pickupBehaviour.Held;
		
	public Vector3 DestinationPosition
	{
		get => GameState.m_destPos;
		set => GameState.m_destPos = value;
	}

	public float m_possessionFwdWalkSpeed = 4.5f;
	public float m_possessionFwdRunSpeed = 12f;
	public float m_possessionSideWalkSpeed = 3f;
	public float m_possessionSideRunSpeed = 6f;
	public float m_possessionBackWalkSpeed = 3f;
	public float m_possessionBackRunSpeed = 6f;

	#endregion Properties & Fields
	
	#region Functions
	
	public virtual ReactPickup Carrying => m_carrying;

	public virtual GameState_MovingObject GameState => m_gameStateBase;
	
	public void SetID(int _id) {
		m_ID = _id;
		if (m_ID >= s_objectCount) s_objectCount = m_ID + 1;
		s_idLookup[m_ID] = this;
	}
	
	public void AllocateID() {
		if(m_ID > 0) Debug.LogError($"Trying to ReAllocateID of MovingObject: {m_ID}");
		if(m_ID <= 0) m_ID = s_objectCount++;
		s_idLookup[m_ID] = this;
	}
	
	private string GetLocalizedRole() {
		var type = Name;
		switch (type) {
			case NGWorker:
				return Localizer.Get(TERM.ROLE_WORKER);
			/*case Wife:
				return Localizer.Get(TERM.ROLE_WIFE);
			case Kid:
				return Localizer.Get(TERM.ROLE_KID);
			case Driver:
				return Localizer.Get(TERM.ROLE_DRIVER);
			case Sheep:
				return Localizer.Get(TERM.ROLE_SHEEP);
			case Firefighter:
				return Localizer.Get(TERM.ROLE_FIREFIGHTER);
			case Van:
				return Localizer.Get(TERM.ROLE_VAN);*/
			case Foreman:
				return Localizer.Get(TERM.ROLE_FOREMAN);
			default:
				return "<unknown>";
		}
	}
	
	public virtual void PrepareForHolding()
	{ //TS - question: isn't this normally done through 'SetupHeldByPlayer'?
		SetCollisionStyle(COLLISIONSTYLE.KINEMATIC);
		Rigidbody rigidBody = m_nav != null ? m_nav.m_body : GetComponentInChildren<Rigidbody>();
		if (rigidBody != null)
		{
			rigidBody.linearVelocity = Vector3.zero;
			rigidBody.angularVelocity = Vector3.zero;
		}
		transform.eulerAngles = Vector3.zero;
	}
	
	virtual public void UpdateGameState()
	{
		GameState_MovingObject gameState = GameState;
		if (gameState == null || GameManager.Me == null || GameManager.Me.LoadComplete == false) return;
		gameState.m_pos = transform.position;
	}

	protected void StateIdle()
	{
		if (isActiveAndEnabled && m_commander == null)
		{
			//Debug.LogError("Commander has gone missing, this ReactObject doesn't know what to do.", this.gameObject);
			return;
		}

		if (isActiveAndEnabled && m_commander.PlayIdleAnimationsWhenOutside)
		{
			Vector3 facingDir = m_commander.DoorPosOuter - m_commander.DoorPosInner;
			var destAngle = Mathf.Atan2(facingDir.x, facingDir.z) * Mathf.Rad2Deg;

			Vector3 currDir = transform.forward;
			var currAngle = Mathf.Atan2(currDir.x, currDir.z) * Mathf.Rad2Deg;

			if (!m_turningToFaceAway)
			{
				if (Mathf.Abs(destAngle - currAngle) > 0.1f)
				{
					transform.rotation = Quaternion.Slerp(transform.rotation, Quaternion.Euler(0, destAngle, 0), .1f);
				}
				else
				{
#if TODO
					if (m_timeBetweenIdleAnimations == 0)
						ResetIdleAnimationTimers(false);

					if (m_currentIdleAnimationIndex == -1 && Time.time - m_timeSinceIdleAnimation > m_timeBetweenIdleAnimations)
					{
						m_currentIdleAnimationIndex = Random.Range(0, ReactManagment.Me.m_idleAnimations.Count);

						// Assign a new animation index if we get the same one twice
						if (m_currentIdleAnimationIndex == m_lastIdleAnimationIndex)
							m_currentIdleAnimationIndex = m_currentIdleAnimationIndex == ReactManagment.Me.m_idleAnimations.Count - 1 ? 0 : m_currentIdleAnimationIndex + 1;

						PlaySingleAnimation(ReactManagment.Me.m_idleAnimations[m_currentIdleAnimationIndex], ResetIdleAnimationTimers);
					}
#endif
				}
			}
		}
	}

	protected Vector3 GetSpawnHeight(Vector3 _pos, NGCommanderBase _base)
	{
		bool spawnRay = false;
		BaseBlock baseBlock = _base.GetComponentInChildren<BaseBlock>();
		if(baseBlock == null)
		{
			Debug.LogWarning("No BaseBlock found in " + _base.name);
			return _pos;
		}
		float bestHeight = _pos.y;
		var col = baseBlock.GetComponentsInChildren<Collider>(true);
		if (col != null)
		{
			foreach(var coll in col)
			{
				spawnRay = coll.Raycast(new Ray(_pos + Vector3.up * 100f, Vector3.down), out RaycastHit hit, 200f);
				if(spawnRay)
				{
					if(hit.point.y > bestHeight)
					{
						bestHeight = hit.point.y;
					}
				}
			}
		}
		_pos.y = bestHeight + 0.2f;
		return _pos;
	}
	
	protected void SetMovingObjectToLeaveBuilding(bool _isAlreadyActive)
	{
		bool spawnRay = false;
		Vector3 groundPosition;
		if(_isAlreadyActive == false)
		{
			groundPosition = GetSpawnHeight(m_insideMABuilding.DoorPosInner, m_insideMABuilding);
		}
		else
		{
			groundPosition = transform.position;
		}
		MASetStateMoveOutOfBuilding(groundPosition);
	}

	protected virtual bool StateMoveToObject()
	{
		StateMoveToPosition();
		return false;
	}

	protected virtual bool StateChaseObject()
	{
		return false;
	}
	
	protected void StatePickupDestinationObject()
	{
#if TODO
		if (m_destinationObject && m_visualContainer.HoldingTransform)
		{
			var rp = GetComponent<ReactPickup>();
			if (rp) rp.Pickup(this);
		}
		EndState();
#endif
	}
	
#if TODO
	protected void StateMoveWithinCommander()
	{

		if (m_agent && HasAgentArrived())
		{
			// tutorial requires arrival to wait until on screen

			switch (m_mwcPurpose)
			{
				case MWCPurpose.ARRIVE:
					if (m_tutorialWorker)
					{
						TutorialMaster.Me.SetGlobalVarWorkerWaitingAtDepot(true);
						if (m_onlyAllowArrivalInView)
						{
							if (InView())
							{
								m_onlyAllowArrivalInView = false;
								TutorialMaster.Me.SetGlobalVarCameraInPosition(true);
							}
							else
							{
								m_stuckCount = 0;
								return;
							}
						}
					}

					m_commander.ObjectArrived(this);
					break;
				case MWCPurpose.ARRIVE_INSIDE:
					SetState(STATE.IN_COMMANDER_UNAVAILABLE);
					m_commander.ObjectArrivedInside(this);
					break;

				default:
					if (m_arrivalAction != null)
					{
						if (m_currentlyExecutingArrivalAction == null)
						{
							m_commander.StartCoroutine(PerformArrivalActions());
						}
					}
					else
					{
						SetState(STATE.IN_COMMANDER_AVAILABLE);
					}
					break;
			}
		}
	}
#endif

	public void StartAgent() {}
	public void StopCurrentAnimation(bool instant = true) {
		AnimationOverride.Stop(gameObject, instant);
	}
	public void BlobShadowDisplay(Transform blobShadow, bool enabled) {}
	
	protected void StateMoveWithinCommander(){}
	protected void StateInCommanderAvailable()
	{
		// The if statement would null ref if a commander (such as a building site being completed) went missing.
		// I've added this error as it's a bit more descriptive than null ref.
		if (isActiveAndEnabled && m_commander == null)
		{
			Debug.LogError("Commander has gone missing, this ReactObject doesn't know what to do.", this.gameObject);
			return;
		}

		if (isActiveAndEnabled && m_commander.PlayIdleAnimationsWhenOutside)
		{
			Vector3 facingDir = m_commander.DoorPosOuter - m_commander.DoorPosInner;
			var destAngle = Mathf.Atan2(facingDir.x, facingDir.z) * Mathf.Rad2Deg;

			Vector3 currDir = transform.forward;
			var currAngle = Mathf.Atan2(currDir.x, currDir.z) * Mathf.Rad2Deg;

			if (!m_turningToFaceAway)
			{
				if (Mathf.Abs(destAngle - currAngle) > 0.1f)
				{
					transform.rotation = Quaternion.Slerp(transform.rotation, Quaternion.Euler(0, destAngle, 0), .1f);
				}
				else
				{
#if TODO
					if (m_timeBetweenIdleAnimations == 0)
						ResetIdleAnimationTimers(false);

					if (m_currentIdleAnimationIndex == -1 && Time.time - m_timeSinceIdleAnimation > m_timeBetweenIdleAnimations)
					{
						m_currentIdleAnimationIndex = Random.Range(0, ReactManagment.Me.m_idleAnimations.Count);

						// Assign a new animation index if we get the same one twice
						if (m_currentIdleAnimationIndex == m_lastIdleAnimationIndex)
							m_currentIdleAnimationIndex = m_currentIdleAnimationIndex == ReactManagment.Me.m_idleAnimations.Count - 1 ? 0 : m_currentIdleAnimationIndex + 1;

						PlaySingleAnimation(ReactManagment.Me.m_idleAnimations[m_currentIdleAnimationIndex], ResetIdleAnimationTimers);
					}
#endif
				}
			}
		}
	}
	
#if TODO
	protected void StateHeldByPlayer()
	{
		m_p2cInstance.SearchForInteractionTarget(EInteractionType.HOLD);
	}
#endif
	
	
	protected void StateInCommanderUnavailable(){}

	protected void StateHeldByPlayer()
	{
		
	}
	protected void StateThrownByPlayer()
	{
	}

	protected void StateDroppedByPlayer()
	{
	}

	protected virtual void StateDead()
	{
	}
	
	virtual public void StateWorking()
	{
	}

	public void StateResting()
	{
	}

	public void StateDestroyMe()
	{
		Debug.LogError($"StateDestroyMe:{name}");
		DestroyMe();
	}

    virtual protected void StateFollowTransform()
    {
		if(m_followTransform != null)
        {
			/*if(RigidBody != null)
            {
				RigidBody.Move(m_followTransform.position, m_followTransform.rotation);
            }*/
			m_transform.position = m_followTransform.position;
			m_transform.rotation = m_followTransform.rotation;
		}
    }

	public virtual string GetStateInfo()
	{
		return $"State [{m_state}] Action[{PeepAction}]";
	}
	
	public virtual string GetTypeInfo()
	{
		return GetType().Name;
	}
	
	public virtual string GetGameObjectName()
	{
#if UNITY_EDITOR
		string nameStr = $"{GetTypeInfo()}[{m_ID}] {GetStateInfo()}";
		if (Job)
		{
			if(Job.Building)
				nameStr += $" Job[{Job.Building.name}]";
			else
				nameStr += $" Job[{Job.name}]";
		}
		
		if (Home)
		{
			if(Home.Building)
				nameStr += $" Home[{Home.Building.name}]";
			else
				nameStr += $" Home[{Home.name}]";
		}
		return nameStr;
#else
		return $"[{m_ID}]";
#endif
	}

	virtual public void SetState(STATE _state)
	{
		if (m_state != _state)
		{
			switch (m_state)
			{
				case STATE.MA_FOLLOW_TRANSFORM:
					m_followTransform = null;
					break;
				default:
					break;
			}
		}

		m_state = _state;
		
		string nameStr = GetGameObjectName();
		if (name != nameStr)
		{
			name = nameStr;
		}
	}

	public virtual void SetCarriedObject(ReactPickup _obj)
	{
		m_carrying = _obj;
	}
	public void CleanupIfCarrying(bool _interrupted = false) {
		if (Carrying) {
			DestroyCarriedObject(_interrupted);
		}
		StopCleaningUp();
	}
	
	public void DestroyCarriedObject(bool _interrupted = false) {
		if (m_carrying != null) {
			if (_interrupted) {
				m_carrying.Interrupted(this);
			}
			m_carrying.Consume();
			Destroy(m_carrying.gameObject);
			SetCarriedObject(null);
		}
	}

	public void StopCleaningUp()
	{
		GameState.m_targetObject = "";
		if (m_cleaningPickup == null)
			return;
		m_cleaningPickup = null;
	}
	
	public void NGSetAsGettingPaid()
	{
		SetState(STATE.IN_SERVICE_BUILDING);
		gameObject.SetActive(false);
	}

	public IEnumerator Co_TurnToFaceAngle(float _angle)
	{
		var rot = transform.rotation;
		var targetRot = Quaternion.Euler(0, _angle, 0);
		float lerpProportion = 0f;
		m_turningToFaceAway = true;
		while (m_state == STATE.IN_COMMANDER_AVAILABLE || m_state == STATE.MOVE_WITHIN_COMMANDER || m_state == STATE.IDLE)
		{
			if (!transform.rotation.AlmostEquals(targetRot))
			{
				lerpProportion += Time.deltaTime * RotationSpeed; 
				transform.rotation = Quaternion.Slerp(rot, targetRot, lerpProportion);
				yield return null;
			}
			else
			{
				break;
			}
		}
		m_turningToFaceAway = false;
	}
	
	public void Internal_EndDrag()
	{
		SetObjectHeld(false);
		var pickup = GetComponent<Pickup>();
		if(pickup == null) return;
		pickup.EndDrag();
	}

	public bool Internal_BeginDrag(PointerEventData _eventData, bool fromDragMethod, int _pickupCount = 1) {
		var pickup = gameObject.GetComponent<Pickup>();
		if (pickup == null) pickup = gameObject.AddComponent<Pickup>();

        if (GameManager.Me.IsOKToPlayUISound())
            AudioClipManager.Me.PlaySoundOld("PlaySound_PickUpResource", GameManager.Me.transform);

        SetObjectHeld(true);

		// TODO not ideal for mobile
        pickup.SetInputId(0);
        
		pickup.StartDrag(true);
		pickup.BeginDragging(null, null, _pickupCount == 1);
		s_lastBeginDrag = gameObject;
		return false;
	}

	Transform m_attachToHand = null;
	
	protected virtual void SetObjectHeld(bool _willBeHeld)
	{
		var rc = GetComponentInChildren<RagdollController>();
		if (UsePhysicsForPickup && rc != null)
		{
			if (_willBeHeld)
			{
				rc.ForceDraggedStateForPickup();
				
				m_attachToHand = rc.BoneHipsRagdoll.FindChildRecursiveByName("mixamorig:RightHand");
				var attachRb = m_attachToHand.GetComponent<Rigidbody>();
				if (PlayerHandManager.Me.Attached == attachRb)
					return;
				PlayerHandManager.Me.AttachToHand(attachRb, this);
			}
			else
			{
				PlayerHandManager.Me.DetachFromHand(m_attachToHand.GetComponent<Rigidbody>());
				if (CanCancelRagdoll())
				{
					rc.CancelRagdoll();
				}
			}
			GetComponent<Pickup>().m_dragHandledByPhysics = true;
		}
	}

	public virtual void ActivateRagDoll(Vector3? _force = null, Action<bool> _onComplete = null)
	{
		RagdollHelper.StartRagdoll(gameObject, _force, _onComplete);
	}

	protected virtual bool CanCancelRagdoll()
	{
		return true;
	}
	
	void CheckLoco()
	{
		if (m_haveCheckedLoco)
			return;
		if (m_anim == null)
			return;
		m_haveCheckedLoco = true;
		
		foreach (var param in m_anim.parameters)
		{
			if (param.name == "Speed")
			{
				m_hasStandardLoco = true;
				break;
			}
		}
	}

	#region Animations

	public bool AnimExists(string _animName)
	{
		return m_animationSet.GetAnimIndex(_animName) != -1;
	}

	public bool PlayAnim(string _animName, Action<MAAnimationSet.AnimationParams, bool> _onFinished = null, float _customSpeedFactor = 1f)
	{
		m_lastAnimationClip = "";
		if(m_animationSet == null)
		{
			Debug.LogError($"{GetType().Name} - PlayAnim - m_animationSet is null for '{name}', anim '{_animName}'");
			return false;
		}
			
		if (m_anim == null || _animName == null)
			return false;
		CheckLoco();
		if (m_hasStandardLoco)
		{
			string animNameLow = _animName.ToLower();
			if(animNameLow.Equals("walk") || animNameLow.Equals("run") || animNameLow.Equals("idle") ||
			   animNameLow.Equals("stop") || animNameLow.Equals("stopLook"))
			{
				return false;
			}
		}

		var anim = m_animationSet.Play(_animName, m_animHandler, m_anim, _onFinished, _customSpeedFactor);
		if (anim.animParams == null && anim.clipName.IsNullOrWhiteSpace()) return false;

		m_lastAnimationClip = anim.clipName;

		return true;
	}

	protected void SetupWithAnimator(Animator _anim)
	{
		if (_anim == null)
		{
			Debug.LogError( $"{GetType().Name} - No animator found for {name}" );
			return;
		}
		
		m_anim = _anim;
		m_animHandler = m_anim.GetComponentInChildren<AnimationHandler>();
		
		var root = m_anim.transform.FindChildRecursiveByName(GlobalData.c_avatarRootName);
		if (root == null)
			return;
		root.gameObject.AddComponent<SendPosToParent>().StartMotionExtraction(true);
	}

	public void SetAnimatorFloat(string _paramName, float _paramValue) //All animator parameter setting should ideally be done through this
	{
		if (_paramValue is < -100f or > 100f)
		{
			Debug.LogError($"Trying to set animator float {_paramName} to {_paramValue}, must be between -100 and 100");
			_paramValue = Mathf.Clamp(_paramValue, -100f, 100f);
		}
		m_anim.SetFloat(_paramName, _paramValue);
	}

	public void BlendAnimatorFloat(string _paramName, float _val, float _blendingSpeed = 2f)
	{
		if (_blendingSpeed <= 0)
			SetAnimatorFloat(_paramName, _val);
		else
		{
			float currentVal = m_anim.GetFloat(_paramName);
			AnimatorParamBlender.Create(m_anim, _paramName, currentVal, _val, _blendingSpeed, newVal => SetAnimatorFloat(_paramName, newVal));
		}
	}

	public void SetAnimatorLayerWeight(string _layerName, float _weightValue)
	{
		if (_weightValue is < 0f or > 1f)
		{
			Debug.LogError($"Trying to set layer weight to {_weightValue}, must be between 0 and 1");
			_weightValue = Mathf.Clamp01(_weightValue);
		}
		var layerInd = m_anim.GetLayerIndex($"{_layerName} Layer");
		m_anim.SetLayerWeight(layerInd, _weightValue);
	}

	public void BlendAnimatorLayerWeight(string _layerName, float _weightValue, float _blendingSpeed = 2f)
	{
		if (m_anim.layerCount == 0) return;
		var layer = m_anim.GetLayerIndex($"{_layerName} Layer");
		if (layer < 0)
		{
			if (s_layerErrors.Add($"{m_ID}_{_layerName}"))
				Debug.LogError($"Character {name} of type {GetType().FullName} has no layer '{_layerName} Layer' (Total layers: {m_anim.layerCount})", gameObject);
			return;
		}
		if (_blendingSpeed <= 0f)
			SetAnimatorLayerWeight(_layerName, _weightValue);
		else
		{
			float currentVal = m_anim.GetLayerWeight(layer);
			AnimatorParamBlender.Create(m_anim, _layerName, currentVal, _weightValue, _blendingSpeed, newVal => SetAnimatorLayerWeight(_layerName, newVal));
		}
	}

	virtual public void SetNavAnimatorValues(float _speed, float _sideSpeed, float _idleness)
	{
		SetAnimatorFloat("Speed", _speed);
		SetAnimatorFloat("SideSpeed", _sideSpeed);
		SetAnimatorFloat("SpeedMag", Mathf.Sqrt(_speed * _speed + _sideSpeed * _sideSpeed));
		SetAnimatorLayerWeight("Idle", _idleness);
	}

	private void SetupAnimHandler()
	{
		var animator = GetComponentInChildren<Animator>();
		var handler = animator.gameObject.GetComponent<AnimationHandler>();
		if(handler == null)
			handler = animator.gameObject.AddComponent<AnimationHandler>();
		handler.Animator = animator;
		Visuals.AnimHandler = handler;
	}
	
	public AnimationHandler.Handle PlaySingleAnimation(string _animClip, System.Action<bool> _callback, bool _re_enableNav = true, bool _usingMotionExtraction = false, float animSpeed = 1.0f) {
		if (Visuals.AnimHandler == null)
		{
			SetupAnimHandler();
		}
		Visuals.AnimHandler.PlaySingleAnimation(_animClip, _callback, _usingMotionExtraction, animSpeed);
		return null;
	}
	public AnimationHandler.Handle PlayLoopAnimation(string _animClip, System.Action<bool> _callback, float _startTime = 0, bool _re_enableNav = true, float animSpeed = 1.0f) {
		if (Visuals.AnimHandler == null)
		{
			SetupAnimHandler();
		}
		Visuals.AnimHandler.PlayLoopingAnimation(null, _animClip, null, null, _callback, null, animSpeed);
		return null;
	}

	public void SetAnimationSpeed(float animSpeed) {
		if (Visuals.AnimHandler != null)
			Visuals.AnimHandler.SetAnimationSpeed(animSpeed);
	}
	
	public void PlayWorkerSyncedLoopAnimation(string _enterLoopAnim, System.Action<bool> _enterLoopCallback,
		string _loopAnim, System.Action<bool> _loopCallback,
		string _exitLoopAnim, System.Action<bool> _exitLoopCallback,
		AnimationHandler _syncedHandler,
		string _syncedEnterLoopAnim, System.Action<bool> _syncedEnterLoopCallback,
		string _syncedLoopAnim, System.Action<bool> _syncedLoopCallback,
		string _syncedExitLoopAnim, System.Action<bool> _syncedExitLoopCallback) {
			Visuals.AnimHandler.PlayLoopingAnimation(_enterLoopAnim, _loopAnim, _exitLoopAnim, _enterLoopCallback, _loopCallback, _exitLoopCallback);
			_syncedHandler.PlayLoopingAnimation(_syncedEnterLoopAnim, _syncedLoopAnim, _syncedExitLoopAnim, _syncedEnterLoopCallback, _syncedLoopCallback, _syncedExitLoopCallback);
	}
	public void PlayerWorkerLoopAnimation(string _startAnimClip, 
		string _loopAnimClip, 
		string _endAnimClip, 
		System.Action<bool> _startCallback, 
		System.Action<bool> _loopCallback, 
		System.Action<bool> _endCallback, 
		Transform _transform, 
		AnimationTransformMatchType _type, 
		float _startTime = 0f, 
		bool _re_enableNav = true, 
		float _playbackSpeed = 1f) {
		switch (_type) {
			case AnimationTransformMatchType.None:
				Visuals.AnimHandler.PlayLoopingAnimation(_startAnimClip, _loopAnimClip, _endAnimClip, _startCallback, _loopCallback, _endCallback, _playbackSpeed);
				break;
			case AnimationTransformMatchType.Match:
				StartCoroutine(Co_MatchTransform(_transform, () =>
					Visuals.AnimHandler.PlayLoopingAnimation(_startAnimClip, _loopAnimClip, _endAnimClip, _startCallback, _loopCallback, _endCallback, _playbackSpeed)));
				break;
			case AnimationTransformMatchType.MatchWithTimedRotation:
				StartCoroutine(Co_MatchTransform(_transform, () =>
					Visuals.AnimHandler.PlayLoopingAnimation(_startAnimClip, _loopAnimClip, _endAnimClip, _startCallback, _loopCallback, _endCallback, _playbackSpeed), m_matchTransformRotateSecs));
				break;
			}
	}
	public bool StopWorkerLoopAnimation(bool _terminate = false) {
		if (Visuals.AnimHandler == null)
		{
			SetupAnimHandler();
		}
		AnimationOverride.Stop(Visuals.AnimHandler.gameObject, _terminate);
		return true;
	}
	public void InsertWorkerSyncedAnimation(string _animClip, System.Action<bool> _callback, Transform _transform, AnimationTransformMatchType _type, AnimationHandler _syncedHandler, string _syncedClip) {
		AnimationOverride.InsertClip(Visuals.AnimHandler.gameObject, _animClip, _callback);
		AnimationOverride.InsertClip(_syncedHandler.gameObject, _syncedClip, null);
	}
	public void InsertWorkerAnimationIntoLoop(string _clip, System.Action<bool> _callback) {
		AnimationOverride.InsertClip(Visuals.AnimHandler.gameObject, _clip, _callback);
	}

	public void EnableMotionExtraction(Transform _destination, string _rootBone)
	{
		AnimationOverride.EnableMotionExtraction(Visuals.AnimHandler.gameObject, _destination, _rootBone);
	}
	#endregion

	public bool IsFacing(Vector3 _targetPos, float _angleThreshold, bool _ignoreY = true)
	{
		Vector3 dir = (_targetPos - transform.position);

		if (_ignoreY)
		{
			dir.y = 0.0f;
		}

		float angle = Vector3.Angle(dir, transform.forward);

		return angle <= _angleThreshold;
	}

	public bool IsTurning => m_turningCoroutine != null;
	Coroutine m_turningCoroutine = null;
	public Action m_onFinishedTurningToLookAt = null;

	public void LookAt(Vector3 _at, float _rotSpeed = 480f, Action _onFinishedTurning = null)
	{
		_at.y = transform.position.y;

		if (_rotSpeed == 0f)
		{
			_at.y = transform.position.y;
			transform.LookAt(_at, Vector3.up);
			return;
		}
		if (m_turningCoroutine != null)
			StopCoroutine(m_turningCoroutine);
		m_turningCoroutine = StartCoroutine(TurnToLookAt(_at, _rotSpeed, _onFinishedTurning));
	}

	private IEnumerator TurnToLookAt(Vector3 _at, float _rotSpeed, Action _onFinishedTurning)
	{
		m_onFinishedTurningToLookAt += _onFinishedTurning;
		Quaternion _idealRot = Quaternion.LookRotation(_at - transform.position, Vector3.up);
		var angleLeft = Quaternion.Angle(transform.rotation, _idealRot);
		angleLeft = Mathf.Min(360f - angleLeft, angleLeft);
		var angleToTurn = _rotSpeed * Time.deltaTime;
		while (angleToTurn < angleLeft)
		{
			if (m_nav.IsTravelling)
			{
				m_turningCoroutine = null;
				yield break;
			}
			transform.rotation = Quaternion.Slerp(transform.rotation, _idealRot, angleToTurn / angleLeft);
			angleLeft -= angleToTurn;
			yield return null;
			angleToTurn = _rotSpeed * Time.deltaTime;
		}
		transform.LookAt(_at, Vector3.up);
		m_onFinishedTurningToLookAt?.Invoke();
		m_onFinishedTurningToLookAt -= _onFinishedTurning;
		m_turningCoroutine = null;
	}


	IEnumerator Co_SetHoldingTransform() {
		yield return null;
		if (this == null) //We start this on GameManager in case this gets disabled by the next frame, but it should not run if this gets destroyed rather than disabled
			yield break;
		Visuals.HoldingTransform = transform.FindChildRecursiveByName(HoldingTransformName);
		m_carrying?.SetUpWithHolder(Visuals.HoldingTransform);
	}

	IEnumerator Co_MatchTransform(Transform _t, System.Action _cb, float _secsToTurnTowardsTarget = 0f) {
		while ((transform.position - _t.position).xzSqrMagnitude() > .01f) {
			transform.position = Vector3.Lerp(transform.position, _t.position, .1f);
			yield return null;
		}
		
		if(_secsToTurnTowardsTarget > 0)
		{
			float turnAroundDuration = 0;
			Quaternion startRotation = transform.rotation;
			while (turnAroundDuration < _secsToTurnTowardsTarget)
			{
				float norm = turnAroundDuration / _secsToTurnTowardsTarget;
				turnAroundDuration += Time.deltaTime;
				transform.rotation = Quaternion.Slerp(startRotation, _t.rotation, norm);

				yield return null;
			}
		}
		else
		{
			while (!transform.rotation.AlmostEquals(_t.rotation))
			{
				transform.rotation = Quaternion.Slerp(transform.rotation, _t.rotation, .1f);
				yield return null;
			}
		}
		_cb();
	}

	public void PrepareGraphicsForPickup() {}

	public void OnDrop() 
    {
        if(GameManager.Me.IsOKToPlayUISound())
            AudioClipManager.Me.PlaySoundOld("PlaySound_DropResource", transform);
    }
	
	public void Warp(Vector3 _position) {}
	
	SurfaceTypeTracker m_surfaceTypeTracker = new SurfaceTypeTracker();
	public SurfaceTypeTracker SurfaceTypeTracker => m_surfaceTypeTracker;
	

	public string m_debugSurface;
	protected virtual void OnCollisionStay(Collision _other)
	{
#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
		if (_other.impulse.normalized.y > .5f)
			m_surfaceTypeTracker.CheckSurface(_other.gameObject, gameObject);
#if UNITY_EDITOR
		m_debugSurface = m_surfaceTypeTracker.DebugInfo;
#endif
		Conveyor.CheckConveyor(_other, gameObject);
#endif
	}

	#endregion Functions

	public virtual void DoPossessionTransferAction() {}
	public virtual void DoPossessedAction() {}
	public virtual void DoPossessedSecondaryAction() {}
	public virtual void DoPossessedBlockAction() {}
	public virtual void UndoPossessedBlockAction() {}
	public virtual void Whip() {}
	public virtual void Encourage(float _amount) {}

	public AkEventHolder m_hitByDecorationEvent;


	protected IDamageReceiver m_vengeanceTarget = null;

	protected virtual bool TakesVengeance => false;
	public void SetVengeanceTarget(IDamageReceiver _target)
	{
		if (TakesVengeance && m_vengeanceTarget == null)
		{
			m_vengeanceTarget = _target;
			ClearTarget();
		}
	}

	void ClearVengeanceTargetIfInvalid()
	{
		if (m_vengeanceTarget != null)
		{
			if (!IsTargetAliveAndValid(m_vengeanceTarget.Transform))
			{
				m_vengeanceTarget = null;
			}
		}
	}

	public virtual bool IsTargetAliveAndValid(Transform _target) { return _target != null; }
	
	public virtual void ApplyDamageEffect(IDamageReceiver.DamageSource source, float _damageDone, Vector3 _sourceOfDamage, MAAttackInstance attack = null, MAHandPowerInfo handPower = null)
	{
		float damage = (attack != null) ? attack.Damage : _damageDone;
		var character = this as MACharacterBase;
		if (character != null)
		{
			bool throwDamage = source.HasFlag(IDamageReceiver.DamageSource.ThrownObject)
				|| source.HasFlag(IDamageReceiver.DamageSource.Turret);
			if (throwDamage)
				damage *= character.CreatureInfo.m_thownObjectDamageMultiplier;
		}
		
		// RW-01-APR-25: Debug command for Russ.
		if (GameManager.Me.DisableCharacterDamage)
		{
			return;
		}

		if (ArmourPoints > 0f)
		{
			ArmourPoints -= damage;
			if (ArmourPoints < 0f)
			{
				damage = -ArmourPoints;
				ArmourPoints = 0f;
			}
			else
			{
				damage = 0f;
			}
		}
		
		damage = Mathf.Clamp(damage, 0, Health);
		Health -= damage;

#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
		if (!GameManager.Me.IsPossessing)
			DamageIndicator.Show(transform, damage);
#endif // !COMBAT_TESTING_ENABLED
	}

	public bool NavigateToPosition(Vector3 _pos, bool _direct = false, float _remoteDestinationDistance = 0f, float _destinationRadius = 0f)
	{
		SetState(STATE.MA_MOVE_TO_POSITION);
		//if(m_nav.PathPending == false || DestinationPosition.Approximately(_pos) == false)
		{
			DestinationPosition = _pos;
			m_timePathRequested = Time.time;
			
			MoveToPos(_pos, _direct, OnPathProcessed, _remoteDestinationDistance, _destinationRadius);

			return true;
		}
		//Debug.LogError($"{GetType()} - PathFindingAlreadyInProgress: Object '{name}'. Current Pos: {transform.position}. m_nav.PathPending == {m_nav.PathPending} || DestinationPosition.Approx(_pos) == {DestinationPosition.Approximately(_pos)}");
		//m_onPathProcessed?.Invoke(NGMovingObject.TargetReachability.PathFindingAlreadyInProgress);
		//m_onPathProcessed = null;
		return false;
	}
	
	protected virtual void MoveToPos(Vector3 _targetPos, bool _direct = false, Action<NavAgent.NavInfo> _onPathReady = null, float _remoteDestinationDistance = 0f, float _destinationRadius = 0f)
	{
		DestinationPosition = _targetPos;
		m_destinationOfLastSuccessfulNav = default;
		m_nav.Speed = GetDesiredSpeed();
		m_nav.Unpause();
		SetNavTarget(_targetPos, _direct, _onPathReady, _remoteDestinationDistance, _destinationRadius);
	}
	
	protected void SetNavTarget(Vector3 _position, bool _direct = false, Action<NavAgent.NavInfo> _onPathReady = null, float _remoteDestinationDistance = 0f, float _destinationRadius = 0f)
	{
		m_nav.SetNavCosts(NavigationCosts);
		m_nav.SetTarget(_position, _direct, NavigationCorridor, _onPathReady, _remoteDestinationDistance, _destinationRadius);
	}
	
	protected Vector3 m_destinationOfLastSuccessfulNav;
	public bool IsNavUpToDate() => (m_destinationOfLastSuccessfulNav - DestinationPosition).xzSqrMagnitude() < .1f * .1f;
	
	private void OnPathProcessed(NavAgent.NavInfo _finalPathPoint)
	{
		float timeTaken = Time.time - m_timePathRequested;
		m_timePathRequested = 0;
		if(_finalPathPoint == null)
		{
			Debug.LogError($"{GetType()} - PathFailed: time taken {timeTaken}. Object '{name}'. Current Pos: {transform.position}. _finalPathPoint is null");
			m_onPathProcessed?.Invoke(NGMovingObject.TargetReachability.PathFailure);
			return;
		}

		TargetReachability reachability;
		switch (_finalPathPoint.m_result)
		{
			case GlobalData.NavJob.EResult.FoundTarget:
				reachability = TargetReachability.IsReachable;
				break;
			case GlobalData.NavJob.EResult.FoundNearest:
				reachability = TargetReachability.IsNotReachable;
				break;
			default:
			case GlobalData.NavJob.EResult.FoundNothing:
				reachability = TargetReachability.IsNotReachableAndAlreadyAtNearestPos;
				break;
		}
		m_destinationOfLastSuccessfulNav = m_nav.OriginalTargetPosition;
		m_onPathProcessed?.Invoke(reachability);
		m_onPathProcessed = null;
	}
	
	public enum TargetReachability
	{
		IsReachable,
		IsNotReachable,
		IsNotReachableAndAlreadyAtNearestPos,
		PathFailure,
		PathFindingAlreadyInProgress,
	}

	// Base interact system
	protected bool IsInteractTypeAllowed(string _type) => string.IsNullOrEmpty(_type) || GetMovingInfo().IsInteractTypeAllowed(_type);

	[Header("Interactables")]
	[SerializeField] protected float m_interactionCheckInterval = 0.1f;	
	[SerializeField] private bool m_enableInteractionScanDebugList = false;
	protected float m_lastTimeInteractionChecked = 0f;
	protected const int c_maxOverlapResults = 150;
	protected readonly Collider[] m_lastInteractionResults = new Collider[c_maxOverlapResults];
	protected HashSet<ICharacterObjectInteract> m_validInteractionResults = new();
	protected LayerMask m_allowedCharacterInteractionLayers;
	protected float m_characterMaxInteractionScanRadius = 5f; //TS - beware, increasing this to, e.g. '10', will increase the potential number of colliders found to over 100 when in town
	public const float c_defaultInteractRange = 4f; //TS - we need interaction with quest scrolls/flowcharacters/questgivers and similar to be further. While interactions e.g. 'kick' need to be closer
	
	[Foldout("Detected Interactables")][SerializeField][TextArea(1, 5)]
	private string m_characterInteractionOverlapDebug = "";
	
	public void CheckForInteractions(Action<ICharacterObjectInteract> _cb)
	{
		if (Time.time >= m_lastTimeInteractionChecked + m_interactionCheckInterval)
		{
			m_validInteractionResults.Clear();
			m_lastTimeInteractionChecked = Time.time;
			
			Transform tr = Transform;
			float radius = Mathf.Clamp(m_characterMaxInteractionScanRadius, 1f, 10f);
			
			float overlapToRear = 1f;
			overlapToRear = Mathf.Clamp(overlapToRear, 0f, radius);
			Vector3 origin = tr.position + Vector3.up * 1.5f + tr.forward * (radius - overlapToRear);
			
			var hitCount = Physics.OverlapSphereNonAlloc(origin, radius, m_lastInteractionResults, m_allowedCharacterInteractionLayers);
			for (int i = hitCount; i < m_lastInteractionResults.Length; ++i) m_lastInteractionResults[i] = null;
			if (hitCount == 0) return;
			
			for (int i = 0; i < hitCount; ++i)
			{
				var hit = m_lastInteractionResults[i];
				if (hit == null) break; //one null hit means all entries are null from this point onwards.
				if (m_mainColliders.Contains(hit) || m_triggerColliders.Contains(hit)) continue;
				var interact = hit.GetComponentInParent<ICharacterObjectInteract>();
				if (interact != null)
				{
					m_validInteractionResults.Add(interact);
				}
			}
		}
		
#if UNITY_EDITOR
		if (m_enableInteractionScanDebugList)
		{
			m_characterInteractionOverlapDebug =
				$"Total Entries: {Array.FindAll(m_lastInteractionResults, x => x != null).Length}\nValid Entries: {m_validInteractionResults.Count}\nColliders Found:\n{string.Join("\n", m_lastInteractionResults.ToList().ConvertToStringList(x => x != null ? $"{x.name}" : ""))}\nValids Listed:\n{(string.Join("\n", m_validInteractionResults.ToList().ConvertToStringList(x => x != null ? $"{x.name}" : "")))}";
		}
#endif

		foreach (var interact in m_validInteractionResults)
		{
			if (interact != null && IsInteractTypeAllowed(interact.InteractType) &&
			    interact.CanInteract(this))
			{
				_cb(interact);
				return;
			}
		}
	}

	public bool IsInInteractLatched => m_interactLatched;
	
	public bool DoStandardInteraction()
	{
		if (m_followManager != null && m_followManager.HasPotentialFollowers)
		{
			m_followManager.GrabNextFollower();
			return true;
		}

		if (IsInInteractLatched)
			return false;
		bool foundInteract = false;
		CheckForInteractions((interact) =>
		{
			if (m_followManager == null || m_followManager.TryRunInteraction(interact) == false)
				interact.DoInteract(this);
			foundInteract = true;
		});
		if (foundInteract) m_interactLatched = true;
		return foundInteract;
	}

	public FollowManager m_followManager = null;
	public bool HasFollowingCharacters => m_followManager != null && m_followManager.HasFollowers;
	private ICharacterObjectInteract m_lastInteract = null;
	private int m_lastInteractFrame = 0;
	private float m_interactHoldTime = 0;
	public string GetStandardInteractionLabel()
	{
		if (m_followManager != null && m_followManager.HasPotentialFollowers)
			return "Tag";
		string label = null;
		bool found = false;
		CheckForInteractions((interact) => {
			var newLabel = interact.GetInteractLabel(this);
			if (newLabel != null) found = true;
			if (m_interactLatched == false) label = newLabel;
			else if (newLabel == null) m_interactLatched = false;

			if (interact != m_lastInteract || m_lastInteractFrame < Time.frameCount - 1)
			{
				m_interactHoldTime = 0;
			}
			else
			{
				var autoInteractTime = interact.AutoInteractTime;
				if (autoInteractTime > 0)
				{
					var oldTime = m_interactHoldTime;
					m_interactHoldTime += Time.deltaTime;
					if (oldTime < autoInteractTime && m_interactHoldTime >= autoInteractTime)
					{
						m_interactHoldTime = 0;
						interact.DoInteract(this);
					}
				}
			}
			m_lastInteract = interact;
			m_lastInteractFrame = Time.frameCount;
		});
		if (found == false) m_interactLatched = false;
		return label;
	}

	public virtual string GetFallbackInteractionLabel() => null;

	private void UpdateInteractLatchedCooldown()
    {
		if(m_interactLatched)
        {
			m_interactLatchedTime += Time.deltaTime;

			if(m_interactLatchedTime >= m_interactLatchedCooldown)
            {
				m_interactLatched = false;
				m_interactLatchedTime = 0.0f;
			}
        }
		else
        {
			m_interactLatchedTime = 0.0f;
        }
    }

	public void PlayExplodeInteractionAnim(string saveIdentifier, string characterTriggerAnimation)
	{
		m_nav.PushPause(saveIdentifier);
		PlaySingleAnimation(characterTriggerAnimation, (_b) =>
			{
				m_nav.PopPause(saveIdentifier);
			});
	}

	public void PlayRuneEffecStaggerAnim(string runeID)
	{
		PlayAnim("StaggerBack", (a, b) =>
			{
				m_nav.PopPause(runeID);
			});
	}

	public void PlayPowerEffectTargetAnim(string pauseId, string targetAnimation, string targetRecoverAnimation)
	{
        m_nav.PushPause(pauseId, false, true);
        PlaySingleAnimation(targetAnimation, (b) =>
			{
				if (string.IsNullOrEmpty(targetRecoverAnimation) == false)
				{
					PlaySingleAnimation(targetRecoverAnimation, (b) =>
						{
							m_nav.PopPause(pauseId);
						});
				}
				else
				{
					m_nav.PopPause(pauseId);
				}
			});
	}

#if UNITY_EDITOR
	virtual public void DebugShowGUIDetails(MACharacterWindow _window, GUIStyle _labelStyle)
	{
		EditorGUILayout.LabelField($"Name: ", Name, _labelStyle);
		EditorGUILayout.LabelField($"m_state: ", m_state.ToString(), _labelStyle);
		//EditorGUILayout.LabelField($"m_stuckCount: ", m_stuckCount.ToString(), _labelStyle);
		if (GameState != null)
		{
			EditorGUILayout.LabelField($"m_pos: ", GameState.m_pos.ToString(), _labelStyle);
			EditorGUILayout.LabelField($"m_destPos: ", GameState.m_destPos.ToString(), _labelStyle);
		}
		
		var stateArray = new string[(int) NGMovingObject.STATE.LAST + 1];
		var stateInt = new int[(int) NGMovingObject.STATE.LAST + 1];
		stateArray[0] = "None";
		stateInt[0] = -1;
		for (int i = 0; i < (int) NGMovingObject.STATE.LAST; i++)
		{
			stateArray[i + 1] = ((NGMovingObject.STATE) i).ToString();
			stateInt[i + 1] = i;
		}

		var statePopup = EditorGUILayout.IntPopup($"Set State:", (int) m_state, stateArray, stateInt);
		if (statePopup != (int) m_state)
		{
			SetState((NGMovingObject.STATE) statePopup);
		}

		var selectPopup = EditorGUILayout.IntPopup($"Actions:", 0, new string[] {"None", "Follow", "Destroy"}, new int[] {0, 1, 2});
		switch (selectPopup)
		{
			case 0:
				break;
			case 1:
				GameManager.Me.StartCameraTrackingObject(gameObject, Vector3.up * -20 + Vector3.forward * 16);
				_window.m_cameraFollowing = gameObject;
				break;
			case 2:
				DeallocateJobAndHome();
				DestroyMe();
				break;
		}

	}
#endif
}
