
using System.Collections;
using System.Collections.Generic;
using Cans.Analytics;
using UnityEngine;

[System.Serializable]
public class NGDeviceID
{
    public static List<NGDeviceID> s_deviceIds = new List<NGDeviceID>();
    public static List<NGDeviceID> GetList => s_deviceIds;
    public string DebugDisplayName => m_deviceModelId;

    public string id;
    public bool m_debugChanged;
    public string m_deviceModelId;
    public string m_deviceModelName;
    public string m_idiom;
    public string m_powerLevel;
    
    public bool IsTablet => m_idiom.ToLower() == "tablet";

    public static List<NGDeviceID> LoadInfoStage(System.Func<NGDeviceID, bool> _callbackOnEachElement)
    {
        s_deviceIds = NGKnack.ImportKnackInto<NGDeviceID>(_callbackOnEachElement);
        return s_deviceIds;
    }

    public static bool PostLoad(NGDeviceID _what)
    {
        return true;
    }
    public static List<NGDeviceID> LoadInfo()
    {
        s_deviceIds = NGKnack.ImportKnackInto<NGDeviceID>(PostLoad);
        return s_deviceIds;
    }
    
    public static NGDeviceID DeviceId
    {
        get
        {
            if(s_deviceIds.Count > 1)
            {
                int iDevice = s_deviceIds.FindIndex(x => x.m_deviceModelId.ToLower() == SystemInfo.deviceModel.ToLower());
                NGDeviceID deviceId = null;
                if(iDevice == -1)
                {
                    deviceId = new NGDeviceID
                    {
                        m_deviceModelId = SystemInfo.deviceModel,
                        m_deviceModelName = SystemInfo.deviceName,
                        m_idiom = Utility.IsTablet ? "Tablet" : "Phone",
                        m_powerLevel = "Medium"
                    };
                    Debug.LogWarning($"DeviceId '{SystemInfo.deviceModel}' not found in NGDeviceID Knack," +
                                   $" attempting to create best possible device info:" +
                                   $" m_deviceModelId= {deviceId.m_deviceModelId}," +
                                   $" m_deviceModelName= {deviceId.m_deviceModelName}," +
                                   $" m_idiom= {deviceId.m_idiom}," +
                                   $" m_powerLevel= {deviceId.m_powerLevel}");
#if !UNITY_EDITOR
                    AnalyticsEvent deviceNotFoundEvent = AnalyticsManager.Me.Events.DeviceNotFound;
                    deviceNotFoundEvent.Parameters.Add(EventParams.systemDeviceModelID, SystemInfo.deviceModel);
                    AnalyticsManager.Me.LogEvent(deviceNotFoundEvent);
#endif
                }
                else
                {
                    deviceId = s_deviceIds[iDevice];
                }
                s_deviceIds.Clear();
                s_deviceIds.Add(deviceId);
            }

            return s_deviceIds.Count > 0 ? s_deviceIds[0] : null;
        }
    }
}
