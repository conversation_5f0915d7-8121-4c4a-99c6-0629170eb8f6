using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Animations;
using UnityEngine.Playables;
using Object = UnityEngine.Object;

#if UNITY_EDITOR
using UnityEditor;
[CustomEditor(typeof(AnimationOverride))]
public class AnimationOverrideEditor : Editor {
    public override void OnInspectorGUI() {
        var ao = target as AnimationOverride;
        base.OnInspectorGUI();
        ao.InspectorGUI();
        Repaint();
    }
}
#endif

public class PlayQueuePlayable : PlayableBehaviour {
    private int m_currentClipIndex = -1;
    private int m_currentInsertIndex = -1;
    private int m_totalNormalClips = 0;
    private bool m_usingRootMotion = false;
    private bool m_playBackwards = false;
    private Playable m_mixer;
    private int m_interrupt = 0;
    private System.Action<bool>[] m_callbacks;
    private List<AnimationAttachData>[] m_attaches;

		private List<float> m_blendTimes;
    private System.Action<bool> m_insertFinishedCallback;
    private List<AnimationClip> m_insertedAnimations = new List<AnimationClip>();
    public event System.Action m_onFinishEvent;
    private AnimationClipPlayable m_insertHolder;
    private GameObject m_owner;
    private AnimationClip[] m_normalClips;
    private List<bool> m_loopOverrides;
    public bool m_isEnding = false;

    public void Initialize(GameObject _owner, AnimationClip[] clipsToPlay, List<AnimationAttachData>[] _clipAttaches, 
                           System.Action<bool>[] _clipCallbacks, List<bool> _loopOverrides, List<float> _blendTimes, bool _usingRootMotion, bool _instantSetup, Playable owner, PlayableGraph graph) {
        m_usingRootMotion = _usingRootMotion;
        m_callbacks = _clipCallbacks;
        m_owner = _owner;
        m_attaches = _clipAttaches;
        m_loopOverrides = _loopOverrides; 
				m_blendTimes = _blendTimes;
        owner.SetInputCount(1);
        m_mixer = AnimationMixerPlayable.Create(graph, clipsToPlay.Length);
        graph.Connect(m_mixer, 0, owner, 0);
        owner.SetInputWeight(0, 1);
        for (int clipIndex = 0; clipIndex < m_mixer.GetInputCount() ; ++clipIndex) {
            graph.Connect(AnimationClipPlayable.Create(graph, clipsToPlay[clipIndex]), 0, m_mixer, clipIndex);
            m_mixer.SetInputWeight(clipIndex, _instantSetup ? 1f : 0f);
        }
        m_normalClips = clipsToPlay;
        m_totalNormalClips = m_mixer.GetInputCount();
        m_isEnding = false;
    }
    public void InsertClip(AnimationClip _clip, List<AnimationAttachData> _attach, System.Action<bool> _cb, float _blendTime, bool _forceLoop = false) {
        if (m_interrupt != 0) return; // insert clip when we're leaving the loop
        var graph = m_mixer.GetGraph();
        int i;
        for (i = 0; i < m_insertedAnimations.Count; ++i) {
            if (m_insertedAnimations[i] == _clip) break;
        }
        m_currentInsertIndex = m_totalNormalClips+i;
        if (i == m_insertedAnimations.Count) {
            m_mixer.SetInputCount(m_currentInsertIndex+1);
            m_insertedAnimations.Add(_clip);
            graph.Connect(AnimationClipPlayable.Create(graph, _clip), 0, m_mixer, m_currentInsertIndex);
            m_mixer.SetInputWeight(m_currentInsertIndex, 0.0f);
            m_loopOverrides.Add(_forceLoop);
        }
        m_loopOverrides[m_currentInsertIndex] = _forceLoop;
				m_blendTimes.Add(_blendTime);
        m_insertFinishedCallback = _cb;
        StartClip(m_currentInsertIndex, _attach);
    }

    void StartClip(int _index, List<AnimationAttachData> _attach) {
        //Debug.LogError($"{Obj} {_index} {m_normalClips[_index].name} started");
        ApplyMotionExtractionInternal();
        EnterAttach(_attach);
        var currentClip = (AnimationClipPlayable)m_mixer.GetInput(_index);
        // Reset the time so that the next clip starts at the correct position
        currentClip.SetTime(m_playBackwards ? currentClip.GetAnimationClip().length : 0);
        if (m_rootMotionSource != null)
            m_rootMotionBase = Vector3.zero;//m_rootMotionSource.position;
    }
    
    List<AnimationAttachData> m_currentAttach = null;
    Dictionary<AnimationAttachData, GameObject> m_attachLookup = new Dictionary<AnimationAttachData, GameObject>();
    public void ClearAttach() {
        if (m_currentAttach != null) {
            for(int i = 0; i < m_currentAttach.Count; i++) {
                if(m_currentAttach[i].AttachBone == AttachmentBone.NONE || m_currentAttach[i].PropPrefab == null) return;
                GameObject go;
                if (m_attachLookup.TryGetValue(m_currentAttach[i], out go)) {
                    Object.Destroy(go);
                }
            }
        }
        m_attachLookup.Clear();
        m_currentAttach = null;
    }
    void EnterAttach(List<AnimationAttachData> _attach) {
        ClearAttach();
        m_currentAttach = _attach;
        if (m_currentAttach != null) {
		    for (int i = 0; i < _attach.Count; i++) {
			    if(_attach[i].AttachBone == AttachmentBone.NONE || _attach[i].PropPrefab == null) return;
                GameObject go = Object.Instantiate(_attach[i].PropPrefab);
                Transform bone = m_owner.GetComponent<AnimationHandler>().GetAttachBone(_attach[i].AttachBone);
                m_attachLookup[_attach[i]] = go;
                go.transform.SetParent(bone);
			    go.transform.localPosition = Vector3.zero;
			    go.transform.localRotation = Quaternion.identity;
			    go.transform.localScale = Vector3.one;
		    }
        }
    }
    
    string Obj => m_owner?.GetComponentInParent<NGMovingObject>()?.name ?? m_owner?.name ?? "<none>";
    
    override public void PrepareFrame(Playable owner, FrameData info) {
        if (m_mixer.GetInputCount() == 0)
            return;
        // Check if we're stopping (blending out)
        bool stopping = false;
        if (m_currentClipIndex >= m_totalNormalClips) {
            stopping = true;
            ClearAttach();
        } else {
            // Advance to next clip if necessary
            float currentTime = 1, endTime = 0;
            bool isLooping = false;
            if (m_currentClipIndex != -1)
            {
                var clip = ((AnimationClipPlayable) m_mixer.GetInput(m_currentClipIndex));
                var aClip = clip.GetAnimationClip();
                isLooping = aClip.isLooping;
                if (m_currentClipIndex < m_totalNormalClips && m_loopOverrides[m_currentClipIndex])
                    isLooping = true;
                endTime = (float)aClip.length;
                currentTime = (float)clip.GetTime();
                if (m_playBackwards)
                    currentTime = endTime - currentTime;
            }
            if (m_interrupt == 2 || currentTime >= endTime) {
                if (m_currentInsertIndex != -1 && m_loopOverrides[m_currentInsertIndex] == false) {
                    m_currentInsertIndex = -1;
                    if (m_insertFinishedCallback != null) m_insertFinishedCallback(false);
                } else if (m_interrupt != 0 || m_currentClipIndex == -1 || !isLooping) {
                    if (m_currentClipIndex != -1 && m_callbacks[m_currentClipIndex] != null)
                    {
                        if (m_currentClipIndex + 1 >= m_totalNormalClips) // if this is the last clip set the isEnding flag 
                            m_isEnding = true;
                        //Debug.LogError($"{Obj} {m_currentClipIndex} {m_normalClips[m_currentClipIndex].name} ended");
                        m_callbacks[m_currentClipIndex](m_interrupt != 0);
                    }
                    m_currentClipIndex++;
                    if (m_interrupt != 0) m_interrupt = 3;
                }
                if (m_currentClipIndex < m_totalNormalClips) {
                    StartClip(m_currentClipIndex, m_attaches[m_currentClipIndex]);
                }
            }
        }
        StepMotionExtraction();

        // Adjust the weight of the inputs
        float maxWeight = 0;
        int clipToMatch = (m_currentInsertIndex == -1 && !stopping) ? m_currentClipIndex : m_currentInsertIndex;
        for (int clipIndex = 0 ; clipIndex < m_mixer.GetInputCount(); ++clipIndex) {
            float target = (clipIndex == clipToMatch) ? 1 : 0;
            float current = m_mixer.GetInputWeight(clipIndex);
            float next = m_blendTimes[clipIndex] == 0 ? target : Mathf.MoveTowards(current, target, Time.deltaTime / m_blendTimes[clipIndex]);
            m_mixer.SetInputWeight(clipIndex, next);
            maxWeight = Mathf.Max(maxWeight, next);
        }
        if (stopping && (maxWeight < .01f)) Stop();
    }
    
    public void Interrupt(bool _waitForEndOfLoop, bool _endBlending) {
        m_interrupt = Mathf.Max(m_interrupt, _waitForEndOfLoop ? 1 : 2);
        if (_endBlending)
        {
            if (m_mixer.IsValid())
            {
                for (int clipIndex = 0; clipIndex < m_mixer.GetInputCount(); ++clipIndex)
                    m_mixer.SetInputWeight(clipIndex, 0);
            }
		}
    }
    void Stop() {
        m_onFinishEvent?.Invoke();
    }
    public void SetSpeed(float _speed) {
        m_playBackwards = _speed < 0f;
        m_mixer.SetSpeed(_speed);
    }

    private Transform m_rootMotionDestination = null, m_rootMotionSource = null;
    private Vector3 m_rootMotionBase, m_rootMotionTotal = Vector3.zero;
    public void EnableMotionExtraction(Transform _destination, Transform _root)
    {
        m_rootMotionDestination = _destination;
        m_rootMotionSource = _root;
        m_rootMotionTotal = Vector3.zero;
    }

    private void ApplyMotionExtractionInternal()
    {
        if (m_rootMotionSource == null || m_rootMotionBase.sqrMagnitude < .001f*.001f) return;
        m_rootMotionDestination.position += m_rootMotionTotal;
        m_rootMotionTotal = Vector3.zero;
    }

    private void StepMotionExtraction()
    {
        if (m_rootMotionSource == null) return;
        if (m_rootMotionBase.sqrMagnitude < .001f * .001f)
        {
            if (m_currentClipIndex != -1)
            {
                var clip = ((AnimationClipPlayable) m_mixer.GetInput(m_currentClipIndex));
                if (!clip.IsNull() && clip.GetTime() > 0)
                    m_rootMotionBase = m_rootMotionSource.position;
            }
        }
        if (m_rootMotionBase.sqrMagnitude < .001f * .001f) return;
        var pos = m_rootMotionSource.position;
        var diff = pos - m_rootMotionBase;
        m_rootMotionBase = pos;
        m_rootMotionTotal += diff;
    }

    public Vector3 MotionExtractionOffset => m_rootMotionSource == null ? Vector3.zero : m_rootMotionTotal;

    public float GetFractionPlayed()
    {
        if (m_currentClipIndex == -1) return 1;
        var clip = ((AnimationClipPlayable) m_mixer.GetInput(m_currentClipIndex));
        if (clip.IsNull()) return 1;
        var aClip = clip.GetAnimationClip();
        var currentTime = (float) clip.GetTime();
        var endTime = (float) aClip.length;
        return currentTime / endTime;
    }

    public float GetTimePlayed()
    {
        if (m_currentClipIndex == -1) return 0;
        var clip = ((AnimationClipPlayable) m_mixer.GetInput(m_currentClipIndex));
        if (clip.IsNull()) return 0;
        var currentTime = (float) clip.GetTime();
        return currentTime;
    }

    public string GetDebugInfo()
    {
        if (m_currentClipIndex != -1)
        {
            var clip = ((AnimationClipPlayable) m_mixer.GetInput(m_currentClipIndex));
            if (!clip.IsNull())
            {
                var aClip = clip.GetAnimationClip();
                var currentTime = (float) clip.GetTime();
                var endTime = (float) aClip.length;
                return $"{m_currentClipIndex}/{m_totalNormalClips}  {aClip.name} (loop:{aClip.isLooping}) {currentTime:n1}/{endTime:n1}";
            }
            return $"{m_currentClipIndex}/{m_totalNormalClips}  clip null";
        }
        return $"?/{m_totalNormalClips}";
    }
}

[RequireComponent(typeof (Animator))]
public class AnimationOverride : MonoBehaviour {
    public const float c_BlendingTime = 0f;

    List<AnimationClip> m_clipsToPlay = new List<AnimationClip>();
    List<System.Action<bool>> m_clipCallbacks = new List<System.Action<bool>>();
    List<List<AnimationAttachData>> m_clipAttaches = new List<List<AnimationAttachData>>();
    List<bool> m_clipLoopOverrides = new();
		List<float> m_clipBlendTimes = new();
    PlayableGraph m_playableGraph;
    PlayQueuePlayable m_playQueue;
    AnimationPlayableOutput m_output;
    bool m_usingRootMotion = false;
    bool m_destroyed = false;
    public bool IsDestroyed => m_destroyed;
    AnimationOverride m_interruptedAnimation;
    public AnimationOverride AddClip(AnimationClip _clip, List<AnimationAttachData> _attach, float _blendTime, bool _forceLoop) {
        m_clipsToPlay.Add(_clip);
        m_clipCallbacks.Add(null);
        m_clipAttaches.Add(_attach);
        m_clipLoopOverrides.Add(_forceLoop);
				m_clipBlendTimes.Add(_blendTime);
        return this;
    }
    public AnimationOverride AddClip(AnimationClip _clip, List<AnimationAttachData> _attach, float _blendTime, System.Action<bool> _callback, bool _forceLoop) {
        if (_clip != null) {
            m_clipsToPlay.Add(_clip);
            m_clipCallbacks.Add(_callback);
            m_clipAttaches.Add(_attach);
            m_clipLoopOverrides.Add(_forceLoop);
						m_clipBlendTimes.Add(_blendTime);
        } else if (_callback != null) {
            _callback(false);
        }
        return this;
    }
    void Play(bool setupInstant = false) {
        m_playableGraph = PlayableGraph.Create();
        var playQueuePlayable = ScriptPlayable<PlayQueuePlayable>.Create(m_playableGraph);
        m_playQueue = playQueuePlayable.GetBehaviour();
        m_playQueue.Initialize(gameObject, m_clipsToPlay.ToArray(), m_clipAttaches.ToArray(), m_clipCallbacks.ToArray(), m_clipLoopOverrides, m_clipBlendTimes, m_usingRootMotion, setupInstant, playQueuePlayable, m_playableGraph);
        var playableOutput = AnimationPlayableOutput.Create(m_playableGraph, "Animation", GetComponent<Animator>());
        playableOutput.SetSourcePlayable(playQueuePlayable);
        playableOutput.SetSourceInputPort(0);
        m_output = playableOutput;
        m_playableGraph.SetTimeUpdateMode(DirectorUpdateMode.GameTime);
        m_playQueue.m_onFinishEvent += OnFinish;
        m_playableGraph.Play();
    }
    public bool IsEnding()
    {
        return m_playQueue.m_isEnding;
    }
    void Pause() {
        m_playableGraph.Stop();
    }
    void Resume() {
        m_playableGraph.Play();
    }
    public void InsertClip(AnimationClip _clip, List<AnimationAttachData> _attach, System.Action<bool> _cb, float _blendTime, bool _forceLoop = false) {
        m_playQueue.InsertClip(_clip, _attach, _cb, _blendTime, _forceLoop);
    }

    public void InsertClip(string _clipName, System.Action<bool> _cb, bool _forceLoop = false)
    {
        List<AnimationAttachData> attach;
				float blendTime;
        var clip = LookupClip(_clipName, out attach, out blendTime);
        InsertClip(clip, attach, _cb, blendTime, _forceLoop);
    }

    public void Interrupt(bool _waitForEndOfLoop, bool _endBlending = false) {
        m_playQueue.Interrupt(_waitForEndOfLoop, _endBlending);
    }
    public void Cancel() {
        CleanShutDown(false);
    }

    public float GetFractionPlayed()
    {
        return m_playQueue?.GetFractionPlayed() ?? 0;
    }

    public float GetTimePlayed()
    {
        return m_playQueue.GetTimePlayed();
    }

    public AnimationOverride SetSpeed(float _speed) {
        m_playQueue.SetSpeed(_speed);
        return this;
    }
    public void OnFinish() {
        CleanShutDown(false);
    }
    
    public void CleanShutDown(bool _fromDestroy)
    {
        if (m_destroyed) return;
        m_destroyed = true;

        if (m_output.IsOutputValid())
            m_output.SetTarget(null);

        if (m_playableGraph.IsValid())
            m_playableGraph.Stop();
        
        if (_fromDestroy) FinishShutDown(false);
        else this.DoNextFrame(() => FinishShutDown(true));
        
        if (m_interruptedAnimation != null) {
            m_interruptedAnimation.Resume();
            m_interruptedAnimation = null;
        }
    }

    private void FinishShutDown(bool _destroy)
    {
        if (m_playableGraph.IsValid())
            m_playableGraph.Evaluate(0f);
        if (m_playQueue != null)
            m_playQueue.ClearAttach();
        if (m_playableGraph.IsValid())
            m_playableGraph.Destroy();
        if (_destroy)
            Destroy(this);
    }

    void OnDisable() {
        CleanShutDown(true);
    }
    public static AnimationOverride Create(GameObject _o) {
        return _o.AddComponent<AnimationOverride>();
    }
    public static AnimationOverride InsertClip(GameObject _o, string _clipName, System.Action<bool> _cb) {
        List<AnimationAttachData> _attach;
				float _blendTime;
        var clip = LookupClip(_clipName, out _attach, out _blendTime);
        return InsertClip(_o, clip, _attach, _cb, _blendTime);
    }

    private static AnimationOverride LastOverride(GameObject _o)
    {
        var overrides = _o.GetComponentsInChildren<AnimationOverride>();
        if (overrides == null || overrides.Length == 0) return null;
        return overrides[overrides.Length - 1];
    }
    public static AnimationOverride InsertClip(GameObject _o, AnimationClip _clip, List<AnimationAttachData> _attach, System.Action<bool> _cb, float _blendTime) {
        var ao = LastOverride(_o);
        if (ao == null) {
            Debug.LogError($"InsertClip attempted on object with no AO {_o.name}", _o.gameObject);
            return null;
        }
        ao.InsertClip(_clip, _attach, _cb, _blendTime);
        return ao;
    }
    public static AnimationOverride PlayClip(GameObject _o, AnimationClip _clip, List<AnimationAttachData> _attach, System.Action<bool> _cb, float _blendTime, bool _usingMotionExtraction = false, bool _forceLoop = false, bool _doNotInterruptPrevious = false) {
        if (_clip == null) return null;
        var prev = _doNotInterruptPrevious ? LastOverride(_o) : Stop(_o, true, true);
        bool cuttingOff = prev != null;

		var ao = Create(_o);
		if (_usingMotionExtraction)
		{
			var root = _o.transform.FindChildRecursiveByName(GlobalData.c_avatarRootName);
			if (root != null)
			{
				var motionExtractor = root.gameObject.GetComponent<SendPosToParent>();
				var oldCb = _cb;
				void newCB(bool b) { motionExtractor.StopMotionExtraction(); oldCb?.Invoke(b); }
				_cb = newCB;

				if (prev == null)
				{
					motionExtractor.garyFlag = true;
					Utility.After(_blendTime, () => motionExtractor.StartMotionExtraction(false));
				}
				else
					motionExtractor.StartMotionExtraction(false);
			}
		}
		ao.m_usingRootMotion = _usingMotionExtraction;
		ao.AddClip(_clip, _attach, _blendTime, _cb, _forceLoop);

        ao.Play(cuttingOff);
		return ao;
    }
    public static AnimationOverride PlayClip(GameObject _o, string _clip, System.Action<bool> _cb, bool _usingMotionExtraction = false, bool _forceLoop = false, bool _doNotInterruptPrevious = false) {
        List<AnimationAttachData> attach;
				float blendTime;
        var clip = LookupClip(_clip, out attach, out blendTime);
        return PlayClip(_o, clip, attach, _cb, blendTime, _usingMotionExtraction, _forceLoop, _doNotInterruptPrevious);
    }
    public static AnimationOverride PlayClip(GameObject _o, AnimationClip _in, List<AnimationAttachData> _attachIn, float _blendTimeIn, System.Action<bool> _inCb, 
        AnimationClip _loop, List<AnimationAttachData> _attachLoop, float _blendTimeLoop, System.Action<bool> _loopCb, AnimationClip _out, List<AnimationAttachData> _attachOut, float _blendTimeOut, System.Action<bool> _outCb, bool _forceLoop = false) {
        Stop(_o, true);
        var ao = Create(_o);
        ao.AddClip(_in, _attachIn, _blendTimeIn, _inCb, false);
        ao.AddClip(_loop, _attachLoop, _blendTimeLoop, _loopCb, _forceLoop);
        ao.AddClip(_out, _attachOut, _blendTimeOut, _outCb, false);
        ao.Play();
        return ao;
    }
    public static AnimationOverride Stop(GameObject _o, bool _instant = false, bool _endBlending = false) {
        var ao = LastOverride(_o);
        if ((ao != null) && !ao.IsEnding() && !ao.IsDestroyed)
            ao.Interrupt(!_instant, _endBlending);
        return ao;
    }
    public static AnimationOverride PlayClip(GameObject _o, string _in, System.Action<bool> _inCb, string _loop, System.Action<bool> _loopCb, string _out, System.Action<bool> _outCb, bool _forceLoop = false) {
        List<AnimationAttachData> attachIn, attachLoop, attachOut;
				float blendTimeIn, blendTimeLoop, blendTimeOut;
        var clipIn = LookupClip(_in, out attachIn, out blendTimeIn);
        var clipLoop = LookupClip(_loop, out attachLoop, out blendTimeLoop);
        var clipOut = LookupClip(_out, out attachOut, out blendTimeOut);
        return PlayClip(_o, clipIn, attachIn, blendTimeIn, _inCb, clipLoop, attachLoop, blendTimeLoop, _loopCb, clipOut, attachOut, blendTimeOut, _outCb, _forceLoop);
    }
    static AnimationClip LookupClip(string _s) {
        List<AnimationAttachData> _attach;
				float _blendTime;
        return LookupClip(_s, out _attach, out _blendTime);
    }
#if UNITY_EDITOR
    private static HashSet<string> s_missingClips = new HashSet<string>(); 
#endif
    public static AnimationClip LookupClip(string _s, out List<AnimationAttachData> _attach, out float _blendTime) {
        _attach = null;
				_blendTime = c_BlendingTime;
        if (string.IsNullOrEmpty(_s)) return null;
        var clipData = AnimationClipDataManager.Me.GetAnimationClipDataByName(_s);
        if (clipData != null) {
            _attach = clipData.AttachData;
						_blendTime = clipData.BlendTime;
#if UNITY_EDITOR
            if (clipData.Clip == null && s_missingClips.Add(_s))
                Debug.LogError($"Clip {_s} has data but clip missing at {clipData.ClipPath}");
#endif
            if (clipData.Clip == null) return GlobalData.Me.m_defaultAnimationClip;
            return clipData.Clip;
        }
#if UNITY_EDITOR
        if (s_missingClips.Add(_s))
            Debug.LogError($"Couldn't find animation clip {_s}");
#endif
        return GlobalData.Me.m_defaultAnimationClip;
    }

    public static void EnableMotionExtraction(GameObject _this, Transform _destination, string _rootBone)
    {
        var ao = LastOverride(_this);
        if (ao != null)
            ao.m_playQueue.EnableMotionExtraction(_destination, _this.transform.Find(_rootBone));
    }
    
    public static Vector3 GetAnimatedPosition(GameObject _this)
    {
        return _this.transform.position + (LastOverride(_this)?.m_playQueue?.MotionExtractionOffset ?? Vector3.zero);
    }
    
#if UNITY_EDITOR
    public void InspectorGUI()
    {
        string info = m_playQueue.GetDebugInfo();
        EditorGUILayout.TextArea(info, new GUIStyle(EditorStyles.textArea) { wordWrap = true }, GUILayout.MaxHeight(50));
    }
#endif
}
