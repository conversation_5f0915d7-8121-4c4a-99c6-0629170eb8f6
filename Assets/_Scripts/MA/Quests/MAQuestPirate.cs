using System;
using System.Collections;
using UnityEngine;

public class MAQuestPirate : MAQuestBase
{
    public enum QuestType
    {
        Wood = 0,
        Metal = 1,
        Cloth = 2,
        Count
    }

    public enum AnimState
    {
        PreWood = 0,
        PostWood = 1,
        PostMetal = 2,
        PostCloth = 3
    }

    public enum PirateOnShipState
    {
        Idle = 0,
        Song = 1
    }

    public AkSwitchHolder[] m_songMusic = new AkSwitchHolder[(int)QuestType.Count + 1] { new(), new(), new(), new() };
    public AkEventHolder[] m_buildAudio = new AkEventHolder[(int)QuestType.Count] { new(), new(), new() };
    public AkEventHolder[] m_depleteStockpileAudio = new AkEventHolder[(int)QuestType.Count] { new(), new(), new() };
    public AkEventHolder m_instrumentalMusic = new AkEventHolder();
    public Animator m_pirateShip, m_pirateShipMovement, m_mandolinAnim, m_concertinaAnim;
    public MAQuestCutscene m_buildSequence1, m_buildSequence2, m_buildSequence3, m_songSequence1, m_songSequence2, m_songSequence3;
    public Transform m_captainHolder, m_firstMateHolder, m_secondMateHolder, m_captainSongTransform, m_firstMateSongTransform, m_secondMateSongTransform,
        m_stockpileHolder, m_shipLookAtTransform, m_instrumentHolder, m_mandolinTransform, m_concertinaTransform;
    public SyncedAnimations m_songAnims = new SyncedAnimations();
    public SyncedAnimations m_idleAnims = new SyncedAnimations();

    // TODO: make settings public
    private float m_pirateTurnSpeed = 180.0f;
    private float[] m_depleteStockpileTimes = new float[(int)QuestType.Count] { 5.0f, 5.0f, 2.5f };
    private float[] m_buildTimes = new float[(int)QuestType.Count] { 12.5f, 11.5f, 5.5f };

    private MAFlowCharacter m_pirateCaptain, m_pirateFirstMate, m_pirateSecondMate;
    private BCStockIn m_stockpile;
    Coroutine m_buildShipCoroutine, m_playBuildSequenceCoroutine, m_playSongSequenceCoroutine;
    private int m_buildID;

    // saved data
    private QuestType m_questType = QuestType.Count;
    private bool m_isStockpileEnabled = false;
    private AnimState m_animState = AnimState.PreWood;
    private PirateOnShipState m_pirateOnShipState = PirateOnShipState.Idle;
    
    private bool m_doFinalDialogue = false;

    private bool m_useSyncedAnimations = true;

    protected override void Awake()
    {
        base.Awake();
        m_buildID = Animator.StringToHash("Build");
    }

    override protected void OnPostLoad()
    {
        // do not call base OnPostLoad(), .MOA file will create quest giver
        InitReferences();
    }

    override public void SetQuestStatus(QuestStatus _status)
    {
        base.SetQuestStatus(_status);

        if (m_status == QuestStatus.InProgress)
        {
        }
    }

    private void InitReferences()
    {
        if (m_pirateCaptain == null)
        {
            m_pirateCaptain = NGManager.Me.m_MACharacterList.Find(x => x.GetTypeInfo() == "PirateCaptain") as MAFlowCharacter;
        }

        if (m_pirateFirstMate == null)
        {
            m_pirateFirstMate = NGManager.Me.m_MACharacterList.Find(x => x.GetTypeInfo() == "PirateFirstMate") as MAFlowCharacter;
        }

        if (m_pirateSecondMate == null)
        {
            m_pirateSecondMate = NGManager.Me.m_MACharacterList.Find(x => x.GetTypeInfo() == "PirateSecondMate") as MAFlowCharacter;
        }

        if (m_pirateCaptain != null)
        {
            m_lipSyncers["PirateCaptain"] = m_pirateCaptain;

            var ls = m_pirateCaptain.SetLipSync(false);
            ls.m_deltaRot = -18.0f;
        }

        if (m_pirateFirstMate != null)
        {
            m_lipSyncers["PirateFirstMate"] = m_pirateFirstMate;

            var ls = m_pirateFirstMate.SetLipSync(false);
            ls.m_deltaRot = -18.0f;
        }

        if(m_stockpile == null)
        {
            MABuilding building = MABuilding.FindBuilding("QuestPirateStockpile", true);

            if(building != null)
            {
                var stocks = building.BuildingComponents<BCStockIn>();

                foreach(var stock in stocks)
                {
                    if(stock != null)
                    {
                        m_stockpile = stock;
                        break;
                    }
                }
            }

            SetStockpileEnabled(m_isStockpileEnabled);
        }

        ShowStockpile(m_pirateCaptain != null); //KW: hide the stockpile until the pirates are spawned

        if (m_animState >= AnimState.PostMetal)
        {
            SetPiratesOnShip(); //KW: this sets the stockpile on the ship and hides it so don't move this above ShowStockpile
            SetPirateOnShipState(m_pirateOnShipState);
        }
    }


    public override float QuestObjectiveValue(string _objective)
    {
        if (_objective.Contains("PlaySongComplete"))
        {
            return m_playSongSequenceCoroutine == null ? 0.0f : 1.0f;
        }

        if (_objective.Contains("BuildShipComplete"))
        {
            return m_buildShipCoroutine == null ? 0.0f : 1.0f;
        }
        
        if (_objective.Contains("BuildSequenceComplete"))
        {
            return m_playBuildSequenceCoroutine == null ? 0.0f : 1.0f;
        }
        
        if (_objective.Contains("DoFinalDialogue"))
        {
            return m_doFinalDialogue ? 0.0f : 1.0f;
        }
        
        return 1.0f;
    }

    public override void TriggerQuestEvent(string _event)
    {
        var split = _event.Split(';', '|', ':', '\n');

        if (split[0].Contains("InitReferences"))
        {
            InitReferences();
        }

        if (split[0].Contains("SetQuestType"))
        {
            QuestType questType = QuestType.Count;

            if(Enum.TryParse(split[1], out questType))
            {
                SetQuestType(questType);
            }
        }

        if (split[0].Contains("PlaySong"))
        {
            PlaySongSequence();  
        }

        if (split[0].Contains("EnableStockpile"))
        {
            bool enabled = false;

            if (bool.TryParse(split[1], out enabled))
            {
                SetStockpileEnabled(enabled);
            }
        }

        if (split[0].Contains("BuildShip"))
        {
            BuildShip();
        }
    }

    public void DoFinalDialogue()
    {
        m_doFinalDialogue = true;
    }

    private void SetQuestType(QuestType _questType)
    {
        m_questType = _questType;
    }

    public void PlaySongMusic()
    {
        if (m_questType < QuestType.Count)
        {
            m_songMusic[(int)m_questType].SetAsOverride();
            PlayPirateDance();
        }
    }

    public void PlaySongInstrumental()
    {
        m_songMusic[(int)QuestType.Count].SetAsOverride();
        m_instrumentalMusic.Play(m_pirateCaptain.gameObject);
    }

    private void SetStockpileEnabled(bool _enabled)
    {
        if (m_stockpile != null)
        {
            m_stockpile.GetStock().RemoveEmptyStockAndClearNeededToProduce();

            if (_enabled && m_questType < QuestType.Count)
            {
                m_stockpile.GetStock().AddOrCreateStock(GetResource(), 0, 1f);
            }
        }

        m_isStockpileEnabled = _enabled;
    }

    private void BuildShip()
    {
        if (m_buildShipCoroutine == null)
        {
            m_buildShipCoroutine = StartCoroutine(Co_BuildShip());
        }
    }

    private IEnumerator Co_BuildShip()
    {
        PlayBuildSequence();

        yield return new WaitForSeconds(0.5f);

        int stockCount = m_stockpile.GetStock().GetStock(GetResource());
        float depleteStockpileTime = m_depleteStockpileTimes[(int)m_questType];

        if (stockCount <= 0)
        {
            yield return new WaitForSeconds(depleteStockpileTime);
        }
        else
        {
            float depleteStockpileDelay = depleteStockpileTime / stockCount;

            while (DepleteStockpile())
            {
                m_depleteStockpileAudio[(int)m_questType]?.Play(m_stockpile.Building.gameObject);
                yield return new WaitForSeconds(depleteStockpileDelay);
            }
        }

        float buildTime = m_buildTimes[(int)m_questType] - depleteStockpileTime;

        yield return new WaitForSeconds(buildTime);

        if (m_questType == QuestType.Cloth)
        {
            m_pirateOnShipState = PirateOnShipState.Idle;
            yield return StartCoroutine(Co_MovePirates(
                Vector3.zero, Quaternion.identity,
                Vector3.zero, Quaternion.identity,
                Vector3.zero, Quaternion.identity));

            PlayPirateOnShipIdles();
            //PlaySongInstrumental();
        }

        while (m_playBuildSequenceCoroutine != null)
        {
            yield return null;
        }

        m_buildShipCoroutine = null;

        yield return null;
    }

    private void PlayBuildSequence()
    {
        if (m_playBuildSequenceCoroutine == null)
        {
            m_playBuildSequenceCoroutine = StartCoroutine(Co_PlayBuildSequence());
        }
    }

    private IEnumerator Co_PlayBuildSequence()
    {
        MAQuestCutscene buildSequence = m_questType == QuestType.Wood ? m_buildSequence1 : m_questType == QuestType.Metal ? m_buildSequence2 : m_buildSequence3;

        yield return buildSequence.Co_Play();

        m_playBuildSequenceCoroutine = null;

        yield return null;
    }

    private void PlaySongSequence()
    {
        if (m_playSongSequenceCoroutine == null)
        {
            m_playSongSequenceCoroutine = StartCoroutine(Co_PlaySongSequence());
        }
    }

    private IEnumerator Co_PlaySongSequence()
    {
        MAQuestCutscene songSequence = m_questType == QuestType.Wood ? m_songSequence1 : m_questType == QuestType.Metal ? m_songSequence2 : m_songSequence3;

        if (m_questType == QuestType.Cloth)
        {
            m_pirateOnShipState = PirateOnShipState.Song;
            StopPirateAnimations();
            yield return StartCoroutine(Co_MovePirates(
                m_captainSongTransform.localPosition, m_captainSongTransform.localRotation,
                m_firstMateSongTransform.localPosition, m_firstMateSongTransform.localRotation,
                m_secondMateSongTransform.localPosition, m_secondMateSongTransform.localRotation));
        }

        // PlaySongMusic();
        yield return songSequence.Co_Play(()=>
        {
            StopPirateAnimations();
        }
        );

        SetInstrument(m_concertinaTransform, null, m_concertinaAnim, 0);
        SetInstrument(m_mandolinTransform, null, m_mandolinAnim, 0);
        
        m_playSongSequenceCoroutine = null;

        yield return null;
    }

    public void SetPiratesOnShipWithFade()
    {
        Crossfade.Me.Fade(() =>
        {
            SetPiratesOnShip();
            PlayPirateOnShipIdles();
            return true;
        }, () => { });
    }

    private void SetPiratesOnShip()
    {
        SetPirateOnShip(m_pirateCaptain, m_captainHolder);
        SetPirateOnShip(m_pirateFirstMate, m_firstMateHolder);
        SetPirateOnShip(m_pirateSecondMate, m_secondMateHolder);
        SetStockpileOnShip();
        PiratesHeadTrackOff();
    }

    private void SetPirateOnShip(MAFlowCharacter _pirate, Transform _holder)
    {
        if (_pirate != null && _holder != null)
        {
            _pirate.transform.SetParent(_holder);
            _pirate.transform.ResetLocal();
            _pirate.SetState(NGMovingObject.STATE.MA_ANIMATOR_CONTROLLED);
        }
    }

    private void SetPirateOnShipState(PirateOnShipState _state)
    {
        if(_state == PirateOnShipState.Idle)
        {
            PlayPirateOnShipIdles();
        }
        else if(_state == PirateOnShipState.Song)
        {
            if (m_pirateCaptain != null)
            {
                m_pirateCaptain.transform.localPosition = m_captainSongTransform.localPosition;
                m_pirateCaptain.transform.localRotation = m_captainSongTransform.localRotation;
            }

            if (m_pirateFirstMate != null)
            {
                m_pirateFirstMate.transform.localPosition = m_firstMateSongTransform.localPosition;
                m_pirateFirstMate.transform.localRotation = m_firstMateSongTransform.localRotation;
            }

            if (m_pirateSecondMate != null)
            {
                m_pirateSecondMate.transform.localPosition = m_secondMateSongTransform.localPosition;
                m_pirateSecondMate.transform.localRotation = m_secondMateSongTransform.localRotation;
            }
        }
    }

    private void SetStockpileOnShip()
    {
        if(m_stockpile != null && m_stockpileHolder != null)
        {
            Transform stockpileTransform = m_stockpile.Building.transform;
            stockpileTransform.SetParent(m_stockpileHolder);
            stockpileTransform.ResetLocal();
            ShowStockpile(false);
        }
    }

    private void ShowStockpile(bool _show)
    {
        if (m_stockpile != null)
        {
            Transform renderers = m_stockpile.Building.transform.FindChildRecursiveByName("Renderers");

            if (renderers != null)
            {
                renderers.gameObject.SetActive(_show);
            }
        }
    }

    public void PlayShipAnimation()
    {
        if (m_pirateShip != null)
        {
            m_pirateShip.SetTrigger(m_buildID);
            m_buildAudio[(int)m_questType]?.Play(m_pirateShip.gameObject);

            switch (m_questType)
            {
                case QuestType.Wood:
                    m_animState = AnimState.PostWood;
                    break;
                case QuestType.Metal:
                    m_animState = AnimState.PostMetal;
                    break;
                case QuestType.Cloth:
                    m_animState = AnimState.PostCloth;
                    break;
                default:
                    break;
            }

            m_pirateShip.SetInteger(m_stateID, (int)m_animState);
        }
    }

    public void PlayShipMovementAnimation()
    {
        if (m_pirateShipMovement != null)
        {
            switch (m_questType)
            {
                case QuestType.Metal:
                case QuestType.Cloth:
                    m_pirateShipMovement.SetTrigger(m_buildID);
                    m_pirateShipMovement.SetInteger(m_stateID, (int)m_animState);
                    break;
                default:
                    break;
            }    
        }
    }

    public void PlayPirateGestures()
    {
        StartCoroutine(Co_PlayPirateGestures());
    }

    private IEnumerator Co_PlayPirateGestures()
    {
        // Previously called from QuestPirateMetal, now from timeline signal
        // Pirates gesture at built ship
        //PlaySingleAnimation(Character[PirateCaptain],"HandGestureReaction_ThumbsUp")
        //WaitForTime(0.2)
        //PlaySingleAnimation(Character[PirateFirstMate],"HandGestureReaction_ThumbsUp")
        //WaitForTime(0.4)
        //PlaySingleAnimation(Character[PirateSecondMate],"HandGestureReaction_ThumbsUp")

        if (m_pirateCaptain != null)
        {
            m_pirateCaptain.PlaySingleAnimation("HandGestureReaction_ThumbsUp", null);
        }

        yield return new WaitForSeconds(0.2f);

        if (m_pirateFirstMate != null)
        {
            m_pirateFirstMate.PlaySingleAnimation("HandGestureReaction_ThumbsUp", null);
        }

        yield return new WaitForSeconds(0.4f);

        if (m_pirateSecondMate != null)
        {
            m_pirateSecondMate.PlaySingleAnimation("HandGestureReaction_ThumbsUp", null);
        }

        yield return null;
    }

    private IEnumerator Co_MovePirates(Vector3 _captainPos, Quaternion _captainRot, Vector3 _firstMatePos, Quaternion _firstMateRot, Vector3 _secondMatePos, Quaternion _secondMateRot)
    {
        Vector3 captainStartPos = Vector3.zero, firstMateStartPos = Vector3.zero, secondMateStartPos = Vector3.zero;
        Quaternion captainStartRot = Quaternion.identity, firstMateStartRot = Quaternion.identity, secondMateStartRot = Quaternion.identity;

        if(m_pirateCaptain != null)
        {
            captainStartPos = m_pirateCaptain.transform.localPosition;
            captainStartRot = m_pirateCaptain.transform.localRotation;
        }

        if (m_pirateFirstMate != null)
        {
            firstMateStartPos = m_pirateFirstMate.transform.localPosition;
            firstMateStartRot = m_pirateFirstMate.transform.localRotation;
        }

        if (m_pirateSecondMate != null)
        {
            secondMateStartPos = m_pirateSecondMate.transform.localPosition;
            secondMateStartRot = m_pirateSecondMate.transform.localRotation;
        }

        float lerpValue = 0.0f;
        float lerpRate = 2.0f;

        while(lerpValue < 1.0f)
        {
            lerpValue += Time.deltaTime * lerpRate;
            lerpValue = Mathf.Clamp01(lerpValue);

            if (m_pirateCaptain != null)
            {
                m_pirateCaptain.transform.localPosition = Vector3.Lerp(captainStartPos, _captainPos, lerpValue);
                m_pirateCaptain.transform.localRotation = Quaternion.Slerp(captainStartRot, _captainRot, lerpValue);
            }

            if (m_pirateFirstMate != null)
            {
                m_pirateFirstMate.transform.localPosition = Vector3.Lerp(firstMateStartPos, _firstMatePos, lerpValue);
                m_pirateFirstMate.transform.localRotation = Quaternion.Slerp(firstMateStartRot, _firstMateRot, lerpValue);
            }

            if (m_pirateSecondMate != null)
            {
                m_pirateSecondMate.transform.localPosition = Vector3.Lerp(secondMateStartPos, _secondMatePos, lerpValue);
                m_pirateSecondMate.transform.localRotation = Quaternion.Slerp(secondMateStartRot, _secondMateRot, lerpValue);
            }

            yield return null;
        }

        yield return null;
    }

    private void PlayPirateDance()
    {
        if(CheckPirateMissing())
        {
            return;
        }

        SetInstrument(m_concertinaTransform, m_pirateFirstMate.transform, m_concertinaAnim, 1);
        SetInstrument(m_mandolinTransform, m_pirateSecondMate.transform, m_mandolinAnim, 1);

        if (m_useSyncedAnimations)
        {
            SetPirateAnimators(m_songAnims);
            m_songAnims.Play(this);
        }
        else
        {
            m_pirateCaptain.PlaySingleAnimation("PirateCaptain_Dance", null);
            m_pirateFirstMate.PlaySingleAnimation("PirateFirstMate_Concertina", null);
            m_pirateSecondMate.PlaySingleAnimation("PirateSecondMate_Mandolin", null);
        }
    }

    private void SetInstrument(Transform _instrument, Transform _parent, Animator _animator, int _state)
    {
        if(_parent != null)
        {
            _instrument.SetParent(_parent);
        }
        else
        {
            _instrument.SetParent(m_instrumentHolder);
        }

        _instrument.ResetLocal();

        // If using synced animations turn off game object and use empty animation state as default, otherwise turn on and use hidden as default
        if (m_useSyncedAnimations)
        {
            _instrument.gameObject.SetActive(_parent != null);
        }
        else
        {
            _animator.SetInteger(m_stateID, _state);
        }
    }

    private void PlayPirateOnShipIdles()
    {
        if (CheckPirateMissing())
        {
            return;
        }

        SetInstrument(m_mandolinTransform, m_pirateSecondMate.transform, m_mandolinAnim, 2);

        if (m_useSyncedAnimations)
        {
            SetPirateAnimators(m_idleAnims);
            m_idleAnims.Play(this);
        }
        else
        {
            m_pirateCaptain.PlayLoopAnimation("PirateCaptain_Idle", null);
            m_pirateFirstMate.PlayLoopAnimation("PirateFirstMate_Idle", null);
            m_pirateSecondMate.PlayLoopAnimation("PirateSecondMate_Idle", null);
        }
    }

    private void StopPirateAnimations()
    {
        SetInstrument(m_concertinaTransform, null, m_concertinaAnim, 0);
        SetInstrument(m_mandolinTransform, null, m_mandolinAnim, 0);

        if (m_useSyncedAnimations)
        {
            m_songAnims.Stop();
            m_idleAnims.Stop();
        }
        else
        {
            if (m_pirateCaptain != null)
            {
                m_pirateCaptain.StopWorkerLoopAnimation(true);
            }

            if (m_pirateFirstMate != null)
            {
                m_pirateFirstMate.StopWorkerLoopAnimation(true);        
            }

            if (m_pirateSecondMate != null)
            {
                m_pirateSecondMate.StopWorkerLoopAnimation(true); 
            }
        }
    }

    private bool DepleteStockpile()
    {
        if (m_stockpile != null && m_questType < QuestType.Count)
        {
            return m_stockpile.ConsumeStock(GetResource());
        }

        return false;
    }

    private NGCarriableResource GetResource()
    {
        switch (m_questType)
        {
            case QuestType.Wood:
                return NGCarriableResource.GetInfo("Timber");
            case QuestType.Metal:
                return NGCarriableResource.GetInfo("Metal");
            case QuestType.Cloth:
                return NGCarriableResource.GetInfo("Fabric");
            default:
                return null;
        }
    }

    private void PiratesLookAt(Vector3 _target)
    {
        m_pirateCaptain.LookAt(_target, m_pirateTurnSpeed);
        m_pirateFirstMate.LookAt(_target, m_pirateTurnSpeed);
        m_pirateSecondMate.LookAt(_target, m_pirateTurnSpeed);
    }

    public void PiratesHeadtrackCamera()
    {
        Transform cameraTransform = GameManager.Me.m_camera.transform;

        HeadTracker headtracker = m_pirateCaptain.GetComponent<HeadTracker>();
        headtracker.ForceUseAnimation(false);
        headtracker.Override(cameraTransform);
        headtracker = m_pirateFirstMate.GetComponent<HeadTracker>();
        headtracker.ForceUseAnimation(false);
        headtracker.Override(cameraTransform);
        headtracker = m_pirateSecondMate.GetComponent<HeadTracker>();
        headtracker.ForceUseAnimation(false);
        headtracker.Override(cameraTransform);
    }
    
    public void PiratesHeadTrackOff()
    {
        HeadTracker headtracker = m_pirateCaptain.GetComponent<HeadTracker>();
        headtracker.ForceUseAnimation(true);
        headtracker = m_pirateFirstMate.GetComponent<HeadTracker>();
        headtracker.ForceUseAnimation(true);
        headtracker = m_pirateSecondMate.GetComponent<HeadTracker>();
        headtracker.ForceUseAnimation(true);
    }

    private bool CheckPirateMissing()
    {
        if (m_pirateCaptain == null)
        {
            Debug.LogError("MAPirateQuest: Captain is missing");
            return true;
        }

        if (m_pirateFirstMate == null)
        {
            Debug.LogError("MAPirateQuest: First Mate is missing");
            return true;
        }

        if (m_pirateSecondMate == null)
        {
            Debug.LogError("MAPirateQuest: Second Mate is missing");
            return true;
        }

        return false;
    }

    private void SetPirateAnimators(SyncedAnimations _anims)
    {
        _anims.m_syncedAnims[0].m_animator = m_pirateCaptain.m_anim;
        _anims.m_syncedAnims[1].m_animator = m_pirateFirstMate.m_anim;
        _anims.m_syncedAnims[2].m_animator = m_pirateSecondMate.m_anim;
    }

    public class SaveLoadQuestPirateContainer : SaveLoadQuestBaseContainer
    {
        public SaveLoadQuestPirateContainer() : base() { }
        public SaveLoadQuestPirateContainer(MAQuestBase _base) : base(_base) { }
        [Save] public QuestType m_questType;
        [Save] public int m_isStockpileEnabled;
        [Save] public AnimState m_animState;
        [Save] public PirateOnShipState m_pirateOnShipState;
    }

    public override SaveLoadQuestBaseContainer Save()
    {
        var saveContainer = new SaveLoadQuestPirateContainer(this);

        saveContainer.m_questType = m_questType;
        saveContainer.m_isStockpileEnabled = m_isStockpileEnabled ? 1 : 0;
        saveContainer.m_animState = m_animState;
        saveContainer.m_pirateOnShipState = m_pirateOnShipState;

        return saveContainer;
    }

    public override void Load(SaveLoadQuestBaseContainer _l)
    {
        base.Load(_l);

        var saveContainer = _l as SaveLoadQuestPirateContainer;
        if (saveContainer != null)
        {
            SetQuestType(saveContainer.m_questType);
            m_isStockpileEnabled = saveContainer.m_isStockpileEnabled > 0;
            m_animState = saveContainer.m_animState;
            m_pirateOnShipState = saveContainer.m_pirateOnShipState;

            if (m_pirateShip != null)
            {
                m_pirateShip.SetInteger(m_stateID, (int)m_animState);
            }

            if (m_pirateShipMovement != null)
            {
                m_pirateShipMovement.SetInteger(m_stateID, (int)m_animState);
            }
        }
    }
}
