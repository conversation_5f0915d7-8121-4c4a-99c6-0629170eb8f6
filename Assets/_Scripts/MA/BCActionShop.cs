using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class BCActionShop : BCActionOrderBase
{
    [KnackField] public float m_workerSellSpeed = 1;
    public override int MaxCardSlots { get { return 1; } }
    public override float SlotLockDuration { get { return 0; } }
    

    public override SpecialHandlingAction GetSpecialHandlingAction(NGMovingObject _obj, SpecialHandlingAction _restrictedAction)
    {
        MAWorker worker = _obj as <PERSON><PERSON><PERSON><PERSON>;
        if (worker != null)
        {
            if (_restrictedAction == null || _restrictedAction == SpecialHandlingAction.CollectClothing)
                if (HasCollectable(worker))
                    return SpecialHandlingAction.CollectClothing;
        }
        return null;
    }

    virtual public void SetDesign(MACharacterBase _character, GameState_Design _design)
    {
        _character.SetArmourDesign(_design);
    }

    protected bool HasCollectable(MACharacterBase _character)
    {
        return GetSuitableResource(_character) != null;
        return false;//m_stock.GetTotalStock() > 0;
    }
    
    public NGCarriableResource GetSuitableResource(MACharacterBase _character)
    {
        foreach(var item in m_stock.Items)
        {
            if(item.m_stock <= 0) continue;
            
            var resource = item.Resource;
            var product = resource.GetProduct();
            if(product == null) continue;
            product.GetLinkedOrder(out var order);
            if(order.IsNullOrEmpty() || order.OrderInfo == null) continue;
            
            var collectType = "";
            if(order.OrderInfo.m_collectType.IsNullOrWhiteSpace() == false)
            {
                collectType = order.OrderInfo.m_collectType.ToLower();
            }
            
            switch(collectType)
            {
                case "maleworker":
                    if(_character as MAWorker && _character.Gender == MAWorkerInfo.WorkerGender.Male)
                        return resource;
                    break;
                
                case "femaleworker":
                    if(_character as MAWorker && _character.Gender == MAWorkerInfo.WorkerGender.Female)
                        return resource;
                    break;
                    
                case "malehero":
                    if(_character as MAHeroBase && _character.Gender == MAWorkerInfo.WorkerGender.Male)
                        return resource;
                    break;
                
                case "femalehero":
                    if(_character as MAHeroBase && _character.Gender == MAWorkerInfo.WorkerGender.Female)
                        return resource;
                    break;
                    
                default:
                    if(_character as MAHeroBase)
                        return resource;
                    break;
            }
        }
        return null;
    }

    public virtual bool AutoStartSequence => true;
    public virtual bool RepeatSequence => true;
    
    protected virtual string OrderInfoBlockName => "ShopOrder";
    
    [Save] bool m_hasLoadedSequence = false;
    [Save] bool m_sequenceStarted = false;
    private bool m_runOutOfOrders = false;
    
    //override public List<NGCarriableResource> GetInputActionStockType() => new List<NGCarriableResource>(){NGCarriableResource.GetInfo("Product:30")};
    
    public void StartSequence()
    {
        m_sequenceStarted = true;
    }
    
    public override void OnOrderSequenceEmpty()
    {
        if(AutoStartSequence == false && m_sequenceStarted == false)
            return;
        if(m_hasLoadedSequence && RepeatSequence == false)
            return;
            
        m_hasLoadedSequence = true;
        m_sequenceStarted = true;
        
        var orderInfos = MAOrderInfo.GetInfosByBlockName(OrderInfoBlockName);
        if(orderInfos == null || orderInfos.Count == 0 || m_runOutOfOrders)
            return;
        
        // Find unused orders
        foreach(var orderInfo in orderInfos)
        {
            // Skip orders we have already completed
            bool completed = false;
            foreach(var historicOrder in GameManager.Me.m_state.m_orderDataHistory)
            {
                if(orderInfo.m_name.Equals(historicOrder.OrderInfoIndexer))
                {
                    completed = true;
                    break;
                }
            }
            if(!completed)
                Sequence.Insert(orderInfo, false);
        }
        
        m_runOutOfOrders = true;
    }

    virtual public bool Collect(NGMovingObject _o)
    {
        var character = _o as MACharacterBase;

        var suitableResource = GetSuitableResource(character);
        if(suitableResource == null || ConsumeStock(suitableResource) == false)
            return false;

        var design = suitableResource.GetProduct()?.Design;
        if (design == null)
            return false;

        SetDesign(character, design);
        if(character is MAWorker)
            (character as MAWorker).SetDefaultAction();
        return true;
    }
    
    override public (string id, System.Func<BCUIPanel> create) GetUIPanelInfo() => (m_info?.id, () => new BCShopPanel(m_info));
    public override bool CombineGUI => false;
}

public class BCShopPanel : BCUIPanel
{
    protected BCActionShop m_shop;
    
    private string m_blockID;
    public override bool ShowWarning => m_shop?.ShowWarning ?? false;
    public override string SpriteBlockID => m_blockID;
    public override string GetPrimaryText()
    {
        var desc = "";
        
        if(desc.IsNullOrWhiteSpace() == false) desc += "\n\n";
        
        desc += m_shop?.m_info?.m_description;
        
        if(desc.IsNullOrWhiteSpace() == false) desc += "\n\n";
        desc += $"<b>Stock</b>";
        
        bool hasItems = false;
        foreach(var item in m_shop.GetStock().Items)
        {
            if(item.m_stock <= 0) continue;
            
            var res = item.Resource;
            
            var product = res.GetProduct();
            if(product == null || product.GetLinkedOrder(out var order) == false)
                continue;
            
            desc += $"\n{item.m_stock} x {order.OrderInfo.m_name}";
            
            hasItems = true;
        }
        
        if(hasItems == false) desc += $"\nNone";
        
        return desc; 
    }
    public BCShopPanel(MAComponentInfo _info) : base(_info) { }

    public override void AddComponent(BCBase _component)
    {
        if(_component is BCActionShop)
        {
            m_blockID = _component.Block.BlockID;
            m_shop = _component as BCActionShop;
            m_all.Add(_component);
        }
    }
    
    public override string GetDescription()
    {
        int validOrders = 0;
        foreach(var slot in m_shop.Slots)
        {
            if(slot.m_order.IsNullOrEmpty() == false)
            {
                validOrders++;
            }
        }
        return $"{validOrders} Order(s) Available";
    }
}
