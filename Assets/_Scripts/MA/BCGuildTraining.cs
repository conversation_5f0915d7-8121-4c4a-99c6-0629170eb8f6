using UnityEngine;
using System.Collections.Generic;

public class BCGuildTraining : BCBase
{
    [KnackField] public float m_heroTrainingPerSecond = 1f;
    [KnackField] public int m_heroesToTrain = 1;
    private GameObject m_heroBlockVisual;
    public int m_doneThisFrame = 0;
    
    public bool TryTrain(MAHeroBase _hero, float _multiplier)
    {
        if(m_doneThisFrame < m_heroesToTrain)
        {
            TrainHero(_hero, _multiplier);
            m_doneThisFrame++;
            return true;
        }
        return false;
    }
    
    public static bool RequiresTraining(MAHeroBase _hero)
    {
        return _hero.CharacterGameState.RequiresTraining;
    }

    private void SetupBlockHeroCharacter()
    {
        var character = GetComponentInChildren<CharacterVisuals>();
        if(character != null)
        {
            m_heroBlockVisual = character.gameObject;
            SetHeroVisible(false);
        }
    }
    
    public override void Activate(int _indexOfComponentType, int _quantityInBuilding)
    {
        SetupBlockHeroCharacter();
        
        base.Activate(_indexOfComponentType, _quantityInBuilding);
    }
    
    private void SetHeroVisible(bool _value)
    {
        if(m_heroBlockVisual == null || m_heroBlockVisual.activeSelf == _value)
            return;
        
        m_heroBlockVisual.SetActive(_value);
    }
    
    public override void UpdateInternal(BuildingComponentsState _state)
    {
        SetHeroVisible(m_doneThisFrame > 0);
        m_doneThisFrame = 0;
    }
    
    public void TrainHero(MAHeroBase _hero, float _multiplier)
    {
        _hero.AddExperience(m_heroTrainingPerSecond * _multiplier * Time.deltaTime, "Training");
    }
    
    override public (string id, System.Func<BCUIPanel> create) GetUIPanelInfo() => (m_info?.id, () => new BCTrainingPanel(m_info));
    
    public class BCTrainingPanel : BCUIPanel
    {
        protected BCGuildTraining m_component;
        private string m_blockID;
        public override string SpriteBlockID => m_blockID;
        public override string GetPrimaryText() =>  m_component?.m_info?.m_description;

        public BCTrainingPanel(MAComponentInfo _info) : base(_info) { }

        public override void AddComponent(BCBase _component)
        {
            if(_component is BCGuildTraining)
            {
                m_blockID = _component.Block.BlockID;
                m_component = _component as BCGuildTraining;
                base.AddComponent(_component);
            }
        }
    }
}
