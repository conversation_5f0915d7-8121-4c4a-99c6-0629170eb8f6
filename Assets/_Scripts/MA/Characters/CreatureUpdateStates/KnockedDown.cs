using System;
using UnityEngine;

// public class ZombieAttack : Attack
// {
// 	public ZombieAttack(MACreatureBase.CREATURE_STATE _state, MACharacterBase _character) : base(_state, _character) { }
//
// 	public override void OnEnter() { base.OnEnter(); }
//
// 	public override void OnUpdate() {
// 		base.OnUpdate();
// 		//m_creature.StateCreatureAttack();
// 	}
// 	public override void OnExit() { base.OnExit(); }
// }
namespace MACharacterStates
{
	[Serializable]
	public class KnockedDown : CommonState
	{
		public KnockedDown(string _state, MACharacterBase _character) : base(_state, _character)
		{
		}
		
		public override void OnEnter()
		{
			base.OnEnter();

			m_character.BlendAnimatorLayerWeight("Combat", 0);
			m_character.m_nav.PushPause("KnockedDown", false, true);
			m_character.SetCanBePushed(false);
			
			m_character.ActivateRagDoll(null, OnRagdollDone);
		}

		public void OnRagdollDone(bool _interrupted)
		{
			ApplyState(CharacterStates.StandUp);
		}
		
		public override void OnUpdate()
		{
			base.OnUpdate();
			if(m_gameStateData.m_timeInState > 8f)
			{
				OnRagdollDone(true);
			}
		}

		public override void OnExit()
		{
			base.OnExit();

			m_character.SetCanBePushed(false == (m_character.m_isQuestCharacter && m_character.m_state == NGMovingObject.STATE.MA_DECIDE_WHAT_TO_DO));
			m_character.m_nav.PopPause("KnockedDown");
		}
	}
}