using System;
using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;
using DG.Tweening;
public class MAGameFlowDialog : MonoBehaviour
{
    // This is the dialog that appears when the player has a new objective or a new envelope
    //
    public GameObject m_decisionHolder;
    public TMP_Text m_decisionText;
    public Image m_decisionAdvisorImage;
    public Transform m_decisionAdvisorHolder;
    
    bool m_showingEnvelope;
    public GameObject m_giftMessageHolder;
    public TMP_Text m_giftMessageText;
    public DOTweenAnimation m_objectiveAnimation;
    public bool m_showDebugFlow = false;
    private Animator m_animator;
    private MAGameFlow m_flow;
    private MAParserSection m_maParserSection;
    private NGBusinessAdvisor m_advisor;
    private float m_reminderInterval = -1;
    private float m_timeUntilNextReminder = 0;
    public Action m_clickedDecisionAction = null;
    
    private bool ShowAdvisor
    {
        get => m_decisionAdvisorHolder.gameObject.activeSelf;
        set { m_decisionAdvisorHolder.gameObject.SetActive(value); gameObject.SetActive(true);}
    }
    private bool ShowDecision 
    { 
        get => m_decisionHolder.activeSelf;
        set { m_decisionHolder.SetActive(value); gameObject.SetActive(true);}
    }
    private bool ShowEnvelope
    {
        get => m_showingEnvelope; 
        set { m_showingEnvelope = value; gameObject.SetActive(true);}
    }
    public bool ShowNothing
    {
        get => ShowEnvelope|ShowDecision|ShowGiftMessage;
        set
        {
            ShowDecision = false;
            ShowEnvelope = false;
            ShowGiftMessage = false;
            ShowAdvisor = false;
            gameObject.SetActive(false);
        }
    }
    public bool ShowGiftMessage
    {
        get=> m_giftMessageHolder.activeSelf;
        set { m_giftMessageHolder.SetActive(value); gameObject.SetActive(true);}
    }

    private void Start()
    {
        m_animator = GetComponent<Animator>();
    }

    void Update()
    {
        if(ShowDecision)
            CheckTheDecision();
        var layoutElement = GetComponent<LayoutElement>();
        if (layoutElement != null)
        {
            layoutElement.ignoreLayout = DesignTableManager.Me.IsInRealDesignGlobally | DesignTableManager.Me.IsInProductMode;
        } 
    }

    public void TriggerTheDecision()
    {
        if (m_flow != null && m_flow.m_decisions.Count == 0)
        {
            var debugString = $"Flow {m_flow.m_blockName}:[{m_flow.m_blockIndex}] has no decision";
            Debug.LogError(debugString);
            m_decisionText.text = debugString;
        }
        else if(m_maParserSection != null && (MAParserManager.CurrentDecision == null))
        {
            var debugString = $"Flow {m_maParserSection.m_name}:[{m_maParserSection.m_currentLine}] has no decision";
            Debug.LogError(debugString);
            m_decisionText.text = m_maParserSection.Lines[m_maParserSection.m_currentLine].m_line;
        }
        else
        {
            CheckTheDecision();
            Sprite aImage = null;
            if(m_flow != null)
                aImage = m_flow.GetAdvisorSprite();
            else if(m_advisor != null)
                aImage = m_advisor.PortaitSprite;
                
            m_decisionAdvisorImage.sprite = aImage;
        }
        ShowNothing = true;
        ShowDecision = true;
        ShowAdvisor = true;
        if(m_animator)
            m_animator?.SetTrigger("TriggerObjective");
        m_objectiveAnimation?.DOFlip();
    }
    public void TriggerTheEnvelope()
    {
        ShowNothing = true;
        ShowEnvelope = true;
        MARewardEnvelope.Create(this, m_flow.GiftsCount);
    }
    public void TriggerGiftMessage()
    {
        ShowNothing = true;
        ShowGiftMessage = true;
        m_giftMessageText.text = $"Buy the cards to move on";
    }
    
    void CheckTheDecision()
    {
        List<NGBusinessDecision> decisions = null;
        if(m_flow != null)
            decisions = m_flow.m_decisions;
        else if(m_maParserSection.m_currentDecision != null)
            decisions = new List<NGBusinessDecision>() {m_maParserSection.m_currentDecision};
        else
        {
            Debug.LogError("No decision to show");
            return;
        }
        if (decisions.Count == 0)
            return;
        var decisionText = "";
        foreach (var d in decisions)
        {
            if(d.GetValue() <= 0)
                decisionText+="<sprite=6>";
            decisionText += $"{d.GetCurrentExplainText()}\n";
        }
        var debugText =$"";
        if (m_showDebugFlow)
        {
            if(m_flow != null)
                debugText += $" <size=12>{m_flow.m_blockName}[{m_flow.m_flow.m_blockIndex}]</size>";
            else
                debugText += $" <size=12>{m_maParserSection.m_name}[{m_maParserSection.m_currentLine}]</size>";
        }
        m_decisionText.text = $"{decisionText.TrimEnd('\n')}{debugText}";
        
        UpdateReminder();
    }
    
    private void UpdateReminder()
    {
        if(m_reminderInterval < 0) return;
        
        m_timeUntilNextReminder -= Time.deltaTime;
        
        if(m_timeUntilNextReminder <= 0)
        {
            if(m_animator)
                m_animator?.SetTrigger("RemindPlayer");
            }
            m_timeUntilNextReminder = m_reminderInterval;
        }
    
    
    public void ClickedEnvelope()
    {
        ShowNothing = true;
        if(m_flow != null)
            m_flow.ClickedEnvelope();
        TriggerGiftMessage();
    }
    
    public void ClickedMe()
    {
        if (m_clickedDecisionAction != null)
        {
            m_clickedDecisionAction();
            return;            
        }
        if(m_maParserSection != null && m_maParserSection.m_currentDecision != null)
        {
            if (m_maParserSection.m_currentDecision.m_type.Equals("TapHereToBegin"))
            {
                m_maParserSection.m_currentDecision.m_initialValue = 0;
                return;
            }
            if (m_advisor != null)
            {
                MAAdvisorDialog.Create(m_advisor, m_maParserSection.m_currentDecision.GetCurrentExplainText());
                return;
            }
        }
        if(m_advisor != null)
            MAAdvisorDialog.Create(m_advisor, m_advisor.m_info);
    }
    void Activate (MAGameFlow _flow)
    {
        DecisionGUIHolder.Me.OnAddedNew(transform);
        m_flow = _flow;
        m_maParserSection = null;
        m_animator = GetComponent<Animator>();
    }
    void Activate (string _advisor, MAParserSection _maParserSection, float _reminderInterval = -1)
    {
        DecisionGUIHolder.Me.OnAddedNew(transform);
        m_advisor = NGBusinessAdvisor.GetInfo(_advisor);
        m_maParserSection = _maParserSection;
        m_flow = null;
        m_animator = GetComponent<Animator>();
        m_reminderInterval = _reminderInterval;
        m_timeUntilNextReminder = _reminderInterval;
    }
    public void DestroyMe()
    {
        Destroy(gameObject);
    }
    public void SetClickAction(Action _action)
    {
        m_clickedDecisionAction = _action;
    }
    public static MAGameFlowDialog Create(MAGameFlow _flow)
    {
        var prefab = MAPrefabLoader.Me.LoadPrefab<MAGameFlowDialog>("_Prefabs/Dialogs/MAGameFlowDialogV2");
        //var instance = Instantiate(prefab, NGManager.Me.m_topLeftHolder);
        var instance = Instantiate(prefab, DecisionGUIHolder.Me.transform);
        instance.Activate(_flow);
        return instance;
    }
    public static MAGameFlowDialog Create(string _advisor, MAParserSection _maParserSection, float _reminderInterval = -1)
    {
        var prefab = MAPrefabLoader.Me.LoadPrefab<MAGameFlowDialog>("_Prefabs/Dialogs/MAGameFlowDialogV2");
        //var instance = Instantiate(prefab, NGManager.Me.m_topLeftHolder);
        var instance = Instantiate(prefab, DecisionGUIHolder.Me.transform);
        instance.Activate(_advisor, _maParserSection, _reminderInterval);
        return instance;
    }
    
}
