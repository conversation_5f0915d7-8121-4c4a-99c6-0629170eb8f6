using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.VFX;

public class BCChimney : BCBase
{
    public MAParticule m_smoke;
    public VisualEffect[] m_effects;
    private bool m_isPlaying;
    public float m_switchTimer = 0;
    
    override public (string id, System.Func<BCUIPanel> create) GetUIPanelInfo() => (BCGeneralInfoPanel.PanelID, () => new BCGeneralInfoPanel());
    
    protected override void Awake()
    {
        m_smoke = GetComponent<MAParticule>();
        if(m_smoke == null)
        {
            m_smoke = gameObject.AddComponent<MAParticule>();
            m_smoke.Start();
        }
        
        if (m_smoke)
        {
            m_smoke.Toggle(false);
        }
        
        if(m_effects == null || m_effects.Length == 0)
            m_effects = GetComponentsInChildren<VisualEffect>(true);

        m_isPlaying = true;// Done to force a stop
        SetParticleActive(false);
        base.Awake();
    }

    protected override void Deactivate(MABuilding _previousOwner, int _quantityRemainingInBuilding, BlockDragAction _action)
    {
        SetParticleActive(false);
        base.Deactivate(_previousOwner, _quantityRemainingInBuilding, _action);
    }
    
    private void SetParticleActive(bool _value)
    {
        if (m_smoke)
        {
            m_smoke.Toggle(_value);
        }
        if(m_effects != null)
        {
            foreach(var e in m_effects)
            {
                if(m_isPlaying && _value == false)
                {
                    e.Stop();
                    m_isPlaying = false;
                }
                
                if(m_isPlaying == false && _value)
                {
                    e.Play();
                    m_isPlaying = true;
                }
            }
        }
    }
    
    override public void LateUpdateInternal()
    {
        m_switchTimer = Mathf.Max(0, m_switchTimer - Time.deltaTime);
           
        if(m_switchTimer > 0) return;
        
        if (m_building.m_chimneySmokeThisUpdate)
        {
            m_building.SwitchVisuals(true);
            m_switchTimer = 1f;
        }
        else
        {
            m_building.SwitchVisuals(false);
        }
        
        SetParticleActive(m_building.m_chimneySmokeThisUpdate);
    }
}
