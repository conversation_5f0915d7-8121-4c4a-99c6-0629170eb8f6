using System;
using UnityEngine;

public class MAQuickTipRoadBuildButton : MAQuickTipButton
{
    private void Start()
    {
        Activate(Name);
    }
    
    private void Update()
    {
        var showButton = MAUnlocks.Me.m_designWalls || MAUnlocks.Me.m_designRoads;
        var target = showButton ? 1 : 0.001f;
        transform.localScale = Vector3.Lerp(transform.localScale, new Vector3(target, target, target), 0.1f);
        if (m_isHighlighting == false)
        {
            m_highLight.SetActive(RoadManager.Me.RoadBuildMode);
        }
    }
}
