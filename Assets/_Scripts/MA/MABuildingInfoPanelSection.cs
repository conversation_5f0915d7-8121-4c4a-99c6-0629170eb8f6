using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class MABuildingInfoPanelSection : NGBuildingInfoGUIComponentBase
{
    public GameObject m_expandedHolder = null;
    
    public Transform m_expandedIcon = null;
    
    // Heading Section
    public TMP_Text m_titleDescriptionText;
    
    // Primary Section
    public GameObject m_primarySection = null;
    public Image m_primaryImage;
    public TMP_Text m_primaryText;
    
    // Table Section
    public TMP_Text m_infoTableHeading;
    public Transform m_infoTableHolder;
    
    private BCUIPanel m_panelInfo;
    
    private int m_lastHash;
    private float m_nextUpdateTime;
    
    public GameObject m_actionsHolder;
    public Button m_showRangeButton;
    public Button m_highlightButton;
    
    private void UpdateExpandedIcon()
    {
        m_expandedIcon.rotation = Quaternion.Euler(0,0,m_expandedHolder.activeSelf ? 180 : 0);
    }
    
    public void SetExpanded(bool _value)
    {
        m_expandedHolder?.SetActive(_value);
        UpdateExpandedIcon();
    }
    
    public void ToggleExpand()
    {
        if(m_expandedHolder == null) return;
        
        SetExpanded(!m_expandedHolder.activeSelf);
    }
    
    public void Update()
    {
        if(Time.realtimeSinceStartup > m_nextUpdateTime)
            RefreshView();
    }

    private void LoadBlockImage(string _blockID)
    {
        NGBlockInfo info = null;
        
        if(_blockID.IsNullOrWhiteSpace() == false)
            info = NGBlockInfo.GetInfo(_blockID);
        
        m_primaryImage.gameObject.SetActive(info != null);
        if(info == null)
        {
            return;
        }
        
        Block.Create(info.m_prefabName,null, Vector3.zero,  (block) =>
        {
            var icon = CaptureObjectImage.Me.Capture(block, CaptureObjectImage.Use.Product);    
            m_primaryImage.sprite = icon;
            Destroy(block);
        });
    }
    
    public void Activate(BCUIPanel _panelInfo)
    {
        SetExpanded(_panelInfo.ExpandByDefault);
        
        m_panelInfo = _panelInfo;
        
        LoadBlockImage(_panelInfo.SpriteBlockID);
        
        RefreshView();
    }
    
    public void OnHighlight()
    {
        if(m_panelInfo == null) return;
        
        m_panelInfo.GetHighlightAction()?.Invoke();
    }
    
    public void OnShowRange()
    {
        if(m_panelInfo == null) return;
        
        m_panelInfo.GetShowRangeAction()?.Invoke();
    }
    
    void RefreshView()
    {
        m_nextUpdateTime = Time.realtimeSinceStartup + 1f;
        m_title.text = m_panelInfo.Title;
        m_titleDescriptionText.text = m_panelInfo.GetDescription();
        m_infoTableHeading.text = m_panelInfo.TableHeading;
        m_infoTableHeading.gameObject.SetActive(m_panelInfo.TableHeading.IsNullOrWhiteSpace() == false);
        m_infoTableHolder.DestroyChildren();
        
        var highlightAction = m_panelInfo.GetHighlightAction();
        var rangeAction = m_panelInfo.GetShowRangeAction();
        
        bool showHighlightButton = highlightAction != null;
        bool showRangeButton = rangeAction != null; 
        bool enableActionHolder = showHighlightButton || showRangeButton;
        if(m_actionsHolder.activeSelf != enableActionHolder)
        {
            m_actionsHolder.SetActive(enableActionHolder);
        }
        
        if(m_highlightButton.gameObject.activeSelf != showHighlightButton)
        {
            m_highlightButton.gameObject.SetActive(showHighlightButton);
        }
        
        if(m_showRangeButton.gameObject.activeSelf != showRangeButton)
        {
            m_showRangeButton.gameObject.SetActive(showRangeButton);
        }
        
        var primaryText = m_panelInfo.GetPrimaryText();
        bool showPrimary = (primaryText.IsNullOrWhiteSpace() == false || m_panelInfo.SpriteBlockID.IsNullOrWhiteSpace() == false);
        m_primarySection.SetActive(showPrimary);
        m_primaryText.text = primaryText;
        
        var vlg = m_infoTableHolder.GetComponent<VerticalLayoutGroup>();
        if(vlg)
        {
            vlg.spacing = m_panelInfo.TableSpacing;
        }
        
        foreach(var createItem in m_panelInfo.CreateTableLines(m_infoTableHolder))
        {
            if(createItem != null)
            {
                createItem();
            }
        }
        
        
        var canvasRT = GetComponentInParent<Canvas>().GetComponent<RectTransform>();
        var rt = GetComponent<RectTransform>();
        LayoutRebuilder.ForceRebuildLayoutImmediate(rt);
        
        gameObject.SetActive(m_panelInfo.DisablePanel == false);
    }
    
    public static MABuildingInfoPanelSection Create(Transform _holder, BCUIPanel _panelInfo)
    {
        var prefab = Resources.Load<MABuildingInfoPanelSection>("_Prefabs/Dialogs/MABuildingInfoPanelSection");
        var instance = Instantiate(prefab, _holder);
        instance.Activate(_panelInfo);
        return instance; 
    } 
}