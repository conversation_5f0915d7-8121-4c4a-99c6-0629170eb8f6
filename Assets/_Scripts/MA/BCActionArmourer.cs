using UnityEngine;

public class BCActionArmourer : BC<PERSON>ctionWeaponsmith
{
    public override int MaxCardSlots { get { return 2; } }
    protected override string OrderInfoBlockName => "ArmourOrder";

    public override SpecialHandlingAction GetSpecialHandlingAction(NGMovingObject _obj, SpecialHandlingAction _restrictedAction)
    {
        MAHeroBase hero = _obj as MAHeroBase;
        if(hero != null)
        {
            if(_restrictedAction == null || _restrictedAction == SpecialHandlingAction.CollectArmour)
                if(<PERSON><PERSON>oll<PERSON><PERSON>(hero))
                    return SpecialHandlingAction.CollectArmour;
        }
        return null;
    }
    
    override public void SetDesign(<PERSON>hara<PERSON>Base _character, GameState_Design _design)
    {
        _character.SetArmourDesign(_design);
    }
}
