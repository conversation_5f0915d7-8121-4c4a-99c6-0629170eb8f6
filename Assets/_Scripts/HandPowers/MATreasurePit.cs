using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public class MATreasurePit : MonoBehaviour
{
    public Animator m_chestAnimator;
    public string m_unearthedTrigger;
    public ParticleSystem m_glowEffect;
    public ParticleSystem m_unearthedEffect;
    public string m_onUnearthedAudio;
    public Image m_progressBar;
    public float m_unearthEffectPlayAfter = .5f;
    private GameState_TreasurePit m_state;
    private bool m_isUnearthed = false;
    private float m_unearthedTime = 0;
    private CanvasGroup m_barCanvasGroup;
    public void Activate(GameState_TreasurePit _state)
    {
        _state.Object = this;
        m_state = _state;
        transform.position = m_state.m_position;
        transform.rotation = Quaternion.Euler(0, m_state.m_rotation, 0);
        UpdateProgress();
        m_barCanvasGroup = m_progressBar.GetComponentInParent<CanvasGroup>();
        m_barCanvasGroup.alpha = 0;

        if (m_state.m_depth > 0)
            MAPowerEffectDig.DigAtPoint(transform.position, -m_state.m_depth, -1, CurrentProgress, 3);
    }
    
    public float CurrentDepth => m_state.m_depth;
    public float CurrentProgress => m_state.m_depth / c_digDepthToUnearth;

    void UpdateProgress()
    {
        m_progressBar.fillAmount = CurrentProgress;
    }

    float m_lastDigTime = 0;
    
    const float c_buryDepth = 3;
    public const float c_digDepthToUnearth = 2;
    private const float c_digDepthMargin = .1f; // allow for rounding errors
    public bool Dig()
    {
        if (m_isUnearthed) return false;
        m_lastDigTime = Time.time;
        float terrainY = transform.position.GroundPosition().y;
        float originalTerrainY = m_state.m_position.y + c_buryDepth;
        float depth = originalTerrainY - terrainY;
        m_state.m_depth = depth;
        UpdateProgress();
        if (depth + c_digDepthMargin >= c_digDepthToUnearth)
        {
            m_isUnearthed = true;
            if (m_chestAnimator != null)
                m_chestAnimator.SetTrigger(m_unearthedTrigger);
            m_glowEffect.Stop();
            if (string.IsNullOrEmpty(m_onUnearthedAudio) == false)
                AudioClipManager.Me.PlaySound(m_onUnearthedAudio, gameObject);
            return true;
        }
        return false;
    }

    void GiveGiftContents()
    {
        List<NGBusinessGift> gifts = new();
        
        foreach (var giftString in m_state.m_content.Split('\n', '|', ';'))
        {
            var gift = NGBusinessGift.GetNonFlowGift(giftString);
            if (gift != null)
                gifts.Add(gift);
        }
        if (gifts.Count > 0)
            NGBusinessGiftsPanel.CreateOrCall(gifts);
    }

    void UpdateBury()
    {
        float lower = Mathf.Max(0, m_unearthedTime - 3) * 2;
        float raise = m_state.m_depth;
        transform.position = m_state.m_position + Vector3.up * (raise - lower);
    }

    void Update()
    {
        const float c_digShowUIDuration = 1;
        var timeSinceLastDig = Time.time - m_lastDigTime;
        bool dugRecently = timeSinceLastDig < c_digShowUIDuration;
        var targetAlpha = dugRecently ? 1 : 0;
        m_chestAnimator.SetBool("Digging", dugRecently); 
        m_barCanvasGroup.alpha = Mathf.Lerp(m_barCanvasGroup.alpha, targetAlpha, .5f);
        var enableBar = m_barCanvasGroup.alpha > .01f;
        if (enableBar != m_barCanvasGroup.gameObject.activeSelf)
            m_barCanvasGroup.gameObject.SetActive(enableBar);
        
        UpdateBury();
        if (m_isUnearthed)
        {
            float lastTime = m_unearthedTime;
            m_unearthedTime += Time.deltaTime;
            if (m_unearthedTime > m_unearthEffectPlayAfter && lastTime <= m_unearthEffectPlayAfter)
            {
                m_unearthedEffect.Play();
                GiveGiftContents();
            }
            if (m_unearthedTime > 5 && m_unearthedEffect.particleCount == 0)
            {
                GameManager.Me.m_state.m_treasurePits.Remove(m_state);
                Destroy(gameObject);
            }
        }
    }

    public static void LoadAll()
    {
        foreach (var state in GameManager.Me.m_state.m_treasurePits)
        {
            Create(state);
        }
    }

    public static MATreasurePit Create(Vector3 _position, float _yRotation, string _content)
    {
        _position = _position.GroundPosition(-c_buryDepth);
        var state = new GameState_TreasurePit() { m_position = _position, m_rotation = _yRotation, m_depth = 0, m_content = _content };
        GameManager.Me.m_state.m_treasurePits.Add(state);
        return Create(state);
    }

    public static MATreasurePit Create(GameState_TreasurePit _state)
    {
        var obj = Instantiate(Resources.Load<GameObject>("PowerEffects/TreasurePit"), PlayerHandManager.Me.transform);
        var tp = obj.GetComponent<MATreasurePit>();
        tp.Activate(_state);
        return tp;
    }
}
