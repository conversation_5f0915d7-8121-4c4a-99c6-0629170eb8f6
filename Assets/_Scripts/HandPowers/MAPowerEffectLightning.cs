using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class MAPowerEffectLightning : MAPowerEffectBase
{
    public LineRenderer m_line;
    public ParticleSystem m_particles;
    public bool m_animate = false;
    public float m_lengthPerSegment = .5f;
    public float m_randomDistance = .6f;
    public float m_arcHeight = .15f;
    public int m_forkLengthMin = 4;
    public int m_forkLengthMax = 8;
    public float m_noiseScale = 1f;

    private bool m_created = false;
    private bool m_targetSystemStarted = false;
    
    private bool m_haveInflictedDamage = false;
    
    const float c_strikeDuration = .4f;
    const float c_chainTime = c_strikeDuration * .1f;
    const float c_impactPointRaise = 1.0f;

    void Start()
    {
        if (SpendMana() == false)
            m_couldNotAfford = true;
        if (c_isContinuous == false)
            StopCasterAnimation();
        SetLightDamping(true);
    }

    Vector3 m_targetPosition;
    
    const bool c_isContinuous = true;
    void LateUpdate()
    {
        if (!HasTarget && m_time < c_strikeDuration * .5f)
            m_time = c_strikeDuration * .5f;
        else if (HasTarget && ((c_isContinuous && IsPowerContinuing && SpendManaContinuous(Time.deltaTime)) || m_targetPosition.sqrMagnitude < .001f*.001f))
        {
            if (m_targets[m_baseIndex] != null && (m_targets[m_baseIndex].GetComponent<MACharacterBase>()?.Health ?? 999) > 0)
            {
                m_targetPosition = IStrikePoint.GetStrikePoint(m_targets[m_baseIndex], 1f);
                if (m_targetPosition.sqrMagnitude > 0)
                    m_time = 0;
            }
        }

        TickTime();
        float lightIntensityTarget = 1;
        if (m_time >= c_strikeDuration)
        {
            PlayerHandManager.Me.SetPowerActivateAnimationState(false);
            SetLightDamping(false);
            lightIntensityTarget = 0;
        }
        UpdateLightIntensity(lightIntensityTarget);
        
        if (m_time >= c_strikeDuration && m_particles.particleCount == 0 && m_lightIntensity < .001f)
        {
            if (c_isContinuous)
                StopCasterAnimation();
            PlayFinishAudio();
            StopTargetAnimationLoop(TargetObject);
            Destroy(gameObject);
            return;
        }

        if (HasTarget && m_time < c_strikeDuration)
        {
            PlayFireAudio();
            if (c_isContinuous || m_haveInflictedDamage == false)
            {
                var tgt = m_targets[m_baseIndex];
                var obj = tgt != null ? tgt.GetComponent<NGMovingObject>() : null;
                if (obj != null)
                    ApplyDamage(IDamageReceiver.DamageSource.HandPower, obj, c_isContinuous);
                if (m_haveInflictedDamage == false)
                    PlayImpactAudioOnObject(obj);
                m_haveInflictedDamage = true;
            }
        }
        if (HasTarget && m_realTime > c_chainTime)
            FireNext(true);

        float fracMin = c_isContinuous ? .5f : 0;
        float frac = Mathf.Clamp(m_realTime / c_strikeDuration, fracMin, 1);
        float intensity = 1;//Mathf.Min(1, (1 - frac) * 8);
        if (m_time >= c_strikeDuration) intensity = 0;
        float intensityEnd = intensity;//Mathf.Min(frac * 1.5f, 1) * intensity;

        if (frac > .25f && m_targetSystemStarted == false && HasTarget)
        {
            StartTargetAnimationLoop(TargetObject);
            //PlayTargetAnimation();
            m_targetSystemStarted = true;
            m_particles.Play();
            PlayImpactAudio(0);
            PlayerHandManager.Me.SetPowerActivateAnimationState(true);
        }
        
        if (HasTarget && m_targets[m_baseIndex] != null)
            m_particles.transform.position = m_targetPosition + Vector3.up * c_impactPointRaise;

        const float c_baseWidth = .2f;
        if ((m_created == false || (m_animate && PauseManager.IsPaused == false)) && m_time < c_strikeDuration && m_source != null)
        {
            m_created = true;
            var sourcePosition = IStrikePoint.GetStrikePoint(m_source);
            var floatMod = m_couldNotAfford ? .1f : .5f; 
            var targetPosition = HasTarget == false ? sourcePosition + (m_source.forward * 0 + Vector3.up * 5) * floatMod : m_targetPosition + Vector3.up * 1.5f;
            FillLineWithLightning(sourcePosition, targetPosition, m_line, gameObject.GetInstanceID(), frac, m_lengthPerSegment, m_randomDistance, m_arcHeight, m_noiseScale, m_light, m_lightIntensityBase);
        }
        
        for (int i = 0; i < m_line.transform.childCount; ++i)
        {
            var clr = m_line.transform.GetChild(i).GetComponent<LineRenderer>();
            clr.startWidth = c_baseWidth * intensity;
            clr.endWidth = c_baseWidth * intensityEnd;
        }
        m_line.startWidth = c_baseWidth * intensity;
        m_line.endWidth = c_baseWidth * intensityEnd;
    }

    private float m_lightIntensity = 0;
    private void UpdateLightIntensity(float _target)
    {
        m_lightIntensity = Mathf.Lerp(m_lightIntensity, _target, .1f.TCLerp());
        SetLightIntensity(m_lightIntensity);
    }

    private void SetLightIntensity(float _lightIntensity)
    {
        if (m_light != null)
            m_light.SetIntensity(_lightIntensity * m_lightIntensityBase);
    }

    public static void FillLineWithLightning(Vector3 _sourcePosition, Vector3 _targetPosition, LineRenderer _line, int _seed, float _lightIntensity, float _lengthPerSegment, float _randomDistance, float _arcHeight, float _noiseScale, Light _light = null, float _lightIntensityBase = 0)
    {
        var along = _targetPosition - _sourcePosition;
        var distance = along.magnitude;
        float arcHeight = distance * _arcHeight * Random.Range(.8f, 1f);
        float randomTurn = (Utility.XorShift01(_seed) - .5f) * Mathf.PI;
        var arcUp = Vector3.up.RotateAbout(along.normalized, randomTurn);

        if (_light != null)
        {
            _light.transform.position = (_sourcePosition + _targetPosition) * .5f + arcUp * arcHeight + UnityEngine.Random.onUnitSphere * .3f;
            float lightIntensity = 1 - Mathf.Abs((.5f - _lightIntensity) * (1.0f / .5f));
            _light.SetIntensity(lightIntensity * _lightIntensityBase);
        }
        
        var fwd = along / distance;
        var side = Vector3.Cross(fwd, fwd.y * fwd.y > .9f * .9f ? Vector3.right : Vector3.up).normalized;
        var up = Vector3.Cross(side, fwd).normalized;
        int segments = Mathf.CeilToInt(distance / _lengthPerSegment);
        if (segments < 2) segments = 2;
        var positions = new Vector3[segments];
        for (int i = 0; i < segments; ++i)
        {
            float t = (float) i / (segments - 1);
            float noiseScale = 1 - (t - .5f) * (t - .5f) * (1.0f / (.5f * .5f));
            float arcScale = 1 - (t - .5f) * 2 * (t - .5f) * 2;
            arcScale *= arcScale;
            positions[i] = Vector3.Lerp(_sourcePosition, _targetPosition, (float) i / (segments - 1));
            if (i > 0 && i < segments - 1)
            {
                var fwdOffset = Random.Range(-1f, 1f) * (_lengthPerSegment * .4f) * noiseScale;
                var sideOffset = Random.Range(-1f, 1f) * _randomDistance * noiseScale;
                var upOffset = Random.Range(-1f, 1f) * _randomDistance * noiseScale + noiseScale * arcHeight * arcScale;
                positions[i] += fwd * fwdOffset + side * sideOffset + up * upOffset;
            }
        }
        
        int maxForks = Mathf.Min(_line.transform.childCount, _line.transform.childCount);
        int forkSegments = Mathf.CeilToInt(distance / (_lengthPerSegment * 8));
        var forkSeed = (uint)(_seed + Time.frameCount * 777); // remove frameCount for deterministic forks
        int forks = Mathf.Min(maxForks, Utility.XorShiftRange(ref forkSeed, forkSegments / 2, forkSegments));
        const float c_minForkRatio = .6f;
        const float c_maxForkRatio = .9f;
        for (int i = 0; i < forks; ++i)
        {
            var child = _line.transform.GetChild(i);
            child.gameObject.SetActive(true);
            var childLine = child.GetComponent<LineRenderer>();
            var rootIndex = Utility.XorShiftRange(ref forkSeed, 1, segments - 1);
            var forkLength = (int)(Utility.XorShiftRange(ref forkSeed, c_minForkRatio, c_maxForkRatio) * segments);
            if (forkLength < 3) forkLength = 3; // length 2 forks are not interesting
            var forkPositions = new Vector3[forkLength];
            forkPositions[0] = positions[rootIndex];
            var forkRotAngle = Utility.XorShiftRange(ref forkSeed, -8.0f, 8.0f);
            if (forkRotAngle < 0) forkRotAngle -= 8.0f; else forkRotAngle += 8.0f; // 8 to 16
            var forkFwd = fwd.RotateAbout(up, forkRotAngle * Mathf.Deg2Rad);
            var forkSide = side.RotateAbout(up, forkRotAngle * Mathf.Deg2Rad);
            for (int j = 1; j < forkLength; ++j)
            {
                var fwdOffset = Random.Range(.7f, 1f) * (_lengthPerSegment * .4f) * _noiseScale;
                var sideOffset = Random.Range(-1f, 1f) * _randomDistance * _noiseScale;
                var upOffset = Random.Range(-1f, 1f) * _randomDistance * _noiseScale;
                forkPositions[j] = forkPositions[j - 1] + forkFwd * fwdOffset + forkSide * sideOffset + up * upOffset;
            }
            childLine.positionCount = forkLength;
            childLine.SetPositions(forkPositions);
            //childLine.widthMultiplier = .5f;
        }
        for (int i = forks; i < maxForks; ++i)
            _line.transform.GetChild(i).gameObject.SetActive(false);
            
        _line.positionCount = segments;
        _line.SetPositions(positions);
        //_line.widthMultiplier = .5f;
    }    
}
