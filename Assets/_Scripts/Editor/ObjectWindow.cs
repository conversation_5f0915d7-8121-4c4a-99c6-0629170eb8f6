using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using UnityEditor.IMGUI.Controls;
using UnityEngine.SceneManagement;

class ObjectTreeView : TreeView {
	public ObjectTreeView(TreeViewState treeViewState) : base(treeViewState) {
		m_baseStyle = new GUIStyle(GUI.skin.label); m_baseStyle.richText = true;
		Reload();
	}

	protected override TreeViewItem BuildRoot() {
		return new TreeViewItem { id = 0, depth = -1 };
	}

	public enum DisplayMode {
		Objects,
		DisabledObjects,
		Renderers,
	}
	public static DisplayMode s_renderMode = DisplayMode.Objects;

	GUIStyle m_baseStyle;

	int[] m_layerCounts = new int[32];
	int[] m_layerMax = new int[32];
	void IncCount(int _layer) {
		m_layerCounts[_layer]++;
		m_layerMax[_layer] = Mathf.Max(m_layerCounts[_layer], m_layerMax[_layer]);
	}
	void CountLayers(GameObject _go) {
		IncCount(_go.layer);
		foreach (Transform t in _go.transform) {
			CountLayers(t.gameObject);
		}
	}
	public void ResetMax() {
		for (int i = 0; i < 32; i ++) m_layerMax[i] = 0;
		Reload();
	}
	protected override IList<TreeViewItem> BuildRows(TreeViewItem root) {
		var rows = GetRows() ?? new List<TreeViewItem>(200);

		rows.Clear();

		for (int i = 0; i < 32; i ++) m_layerCounts[i] = 0;
		for (int i = 0; i < SceneManager.sceneCount; i ++) {
			Scene scene = SceneManager.GetSceneAt(i);

			var sceneItemId = -1 - i;

			Counts sceneItemCount = new Counts();
			var gameObjectRoots = scene.GetRootGameObjects();
			foreach (var go in gameObjectRoots) {
				sceneItemCount += CountChildren(go.transform);
				CountLayers(go);
			}

			var sceneItem = new TreeViewItem(sceneItemId, -1, "Scene: " + scene.name + " - " + sceneItemCount);
			root.AddChild(sceneItem);
			rows.Add(sceneItem);
			sceneItem.children = new List<TreeViewItem>();

			if (IsExpanded(sceneItemId)) {
				// We use the GameObject instanceIDs as ids for items as we want to select the game objects and not the transform components.
				foreach (var gameObject in gameObjectRoots) {
					var item = CreateTreeViewItemForGameObject(gameObject);
					sceneItem.children.Add(item);
					rows.Add(item);
					if (gameObject.transform.childCount > 0) {
						if (IsExpanded(item.id)) {
							AddChildrenRecursive(gameObject, item, rows);
						} else {
							item.children = CreateChildListForCollapsedParent();
						}
					}
				}
			} else {
				sceneItem.children = CreateChildListForCollapsedParent();
			}
		}

		int layersId = -100000 + 1;
		var layersItem = new TreeViewItem(layersId, -1, "<color=#ffc020>Layers</color>");
		root.AddChild(layersItem);
		rows.Add(layersItem);
		layersItem.children = new List<TreeViewItem>();
		if (IsExpanded(layersId)) {
			for (int i = 0; i < 32; i++) {
				var layerItemId = -100000 - i;
				var str = i.ToString() + ": " + LayerMask.LayerToName(i);
				var suff = " - " + m_layerCounts[i] + " max " + m_layerMax[i];
				var clr = (m_layerCounts[i] == 0) ? (m_layerMax[i] == 0 ? "<color=#808080>" : "<color=#ff4040>") : "<color=#ffff40>";
				var layerItem = new TreeViewItem(layerItemId, -1, clr + str + "</color>" + suff);
				layersItem.children.Add(layerItem);
				rows.Add(layerItem);
			}
		} else {
			layersItem.children = CreateChildListForCollapsedParent();
		}

		SetupDepthsFromParentsAndChildren(root);
		return rows;
	}

	void AddChildrenRecursive(GameObject go, TreeViewItem item, IList<TreeViewItem> rows) {
		int childCount = go.transform.childCount;

		long[] childChildCounts = new long[childCount];
		for (int i = 0; i < childCount; i ++) {
			var childTransform = go.transform.GetChild(i);
			childChildCounts[i] = ((long)CountChildren(childTransform).Count << 32) | (uint)i;
		}
		System.Array.Sort<long>(childChildCounts);

		item.children = new List<TreeViewItem>(childCount);
		for (int ii = 0; ii < childCount; ++ii) {
			int i = (int)childChildCounts[childCount-1-ii];
			var childTransform = go.transform.GetChild(i);
			var childItem = CreateTreeViewItemForGameObject(childTransform.gameObject);
			item.AddChild(childItem);
			rows.Add(childItem);

			if (childTransform.childCount > 0) {
				if (IsExpanded(childItem.id)) {
					AddChildrenRecursive(childTransform.gameObject, childItem, rows);
				} else {
					childItem.children = CreateChildListForCollapsedParent();
				}
			}
		}
	}

	static string s_enabledColour = "<color=#80ff80>";
	static string s_disabledColour = "<color=#ff8080>";
	static string s_noneColour = "<color=#8080ff>";
	static TreeViewItem CreateTreeViewItemForGameObject(GameObject gameObject) {
		// We can use the GameObject instanceID for TreeViewItem id, as it ensured to be unique among other items in the tree.
		// To optimize reload time we could delay fetching the transform.name until it used for rendering (prevents allocating strings 
		// for items not rendered in large trees)
		// We just set depth to -1 here and then call SetupDepthsFromParentsAndChildren at the end of BuildRootAndRows to set the depths.
		var children = CountChildren(gameObject.transform);
		var clr = gameObject.activeInHierarchy ? s_enabledColour : s_disabledColour;
		if (children.Count == 0) clr = s_noneColour;
		return new TreeViewItem(gameObject.GetInstanceID(), -1, clr + gameObject.name + "</color> - " + children.ToString());
	}

	struct Counts {
		public int m_total;
		public int m_renderers;
		public int m_disabled;
		public Counts(Transform _t) {
			m_total = 1;
			m_disabled = _t.gameObject.activeInHierarchy ? 0 : 1;
			m_renderers = (_t.GetComponent<MeshRenderer>() == null) ? 0 : 1;
		}
		public Counts(int _t, int _d, int _r) { m_total = _t; m_disabled = _d; m_renderers = _r; }
		public static Counts operator+(Counts _a, Counts _b) {
			return new Counts(_a.m_total+_b.m_total, _a.m_disabled+_b.m_disabled, _a.m_renderers+_b.m_renderers);
		}
		public override string ToString() {
			switch (s_renderMode) {
				case DisplayMode.Renderers:					
					return m_renderers.ToString() + " [" + m_total.ToString() + "]";
				case DisplayMode.DisabledObjects:
					return m_disabled.ToString() + " [" + m_total.ToString() + "]";
				default:
					return m_total.ToString();
			}
		}

		public int Count { 
			get {
				switch (s_renderMode) {
					case DisplayMode.Renderers:					
						return m_renderers;
					case DisplayMode.DisabledObjects:
						return m_disabled;
					default:
						return m_total;
				}
			}
		}
	}

	static Counts CountChildren(Transform _t) {
		Counts count = new Counts(_t);
		foreach (Transform t in _t) {
			count += CountChildren(t);
		}
		return count;
	}

	protected override IList<int> GetAncestors(int id) {
		// The backend needs to provide us with this info since the item with id
		// may not be present in the rows
		var transform = GetGameObject(id).transform;

		List<int> ancestors = new List<int>();
		while (transform.parent != null) {
			ancestors.Add(transform.parent.gameObject.GetInstanceID());
			transform = transform.parent;
		}

		return ancestors;
	}

	protected override IList<int> GetDescendantsThatHaveChildren(int id) {
		Stack<Transform> stack = new Stack<Transform>();

		var start = GetGameObject(id).transform;
		stack.Push(start);

		var parents = new List<int>();
		while (stack.Count > 0) {
			Transform current = stack.Pop();
			parents.Add(current.gameObject.GetInstanceID());
			for (int i = 0; i < current.childCount; ++i) {
				if (current.childCount > 0)
					stack.Push(current.GetChild(i));
			}
		}

		return parents;
	}

	GameObject GetGameObject(int instanceID) {
		return (GameObject)EditorUtility.InstanceIDToObject(instanceID);
	}

	protected override void RowGUI(RowGUIArgs args) {
		base.showAlternatingRowBackgrounds = true;
		var rect = args.rowRect;
		rect.x += baseIndent + foldoutWidth + depthIndentWidth * args.item.depth;
		EditorGUI.LabelField(rect, args.label, m_baseStyle);
		//base.RowGUI(args);
	}

	/*protected override void RowGUI(RowGUIArgs args) {
		Event evt = Event.current;
		extraSpaceBeforeIconAndLabel = 18f;

		// GameObject isStatic toggle 
		var gameObject = GetGameObject(args.item.id);
		if (gameObject == null)
			return;

		Rect toggleRect = args.rowRect;
		toggleRect.x += GetContentIndent(args.item);
		toggleRect.width = 16f;

		// Ensure row is selected before using the toggle (usability)
		if (evt.type == EventType.MouseDown && toggleRect.Contains(evt.mousePosition))
			SelectionClick(args.item, false);

		EditorGUI.BeginChangeCheck();
		bool isStatic = EditorGUI.Toggle(toggleRect, gameObject.isStatic);
		if (EditorGUI.EndChangeCheck())
			gameObject.isStatic = isStatic;

		// Text
		base.RowGUI(args);
	}*/



	protected override void SelectionChanged (IList<int> selectedIds)
	{
		Selection.instanceIDs = (int[])selectedIds;
	}
}

public class ObjectWindow : EditorWindow {

	[SerializeField] TreeViewState m_treeViewState;
	ObjectTreeView m_treeView;

	void OnEnable() {
		if (m_treeViewState == null)
			m_treeViewState = new TreeViewState();
	}

	void OnGUI() {
		if (m_treeView == null)
			m_treeView = new ObjectTreeView(m_treeViewState);
		
		var oldMode = ObjectTreeView.s_renderMode;
		EditorGUI.LabelField(new Rect(0, 0, 60, 20), "Display:");
		ObjectTreeView.s_renderMode = (ObjectTreeView.DisplayMode)EditorGUI.EnumPopup(new Rect(60, 0, 200, 20), oldMode);
		if (GUI.Button(new Rect(60+200+40, 0, 80, 20), "Refresh"))
			m_treeView.Reload();
		if (GUI.Button(new Rect(60+200+40 + 80 + 20, 0, 80, 20), "Reset Max"))
			m_treeView.ResetMax();

		if (ObjectTreeView.s_renderMode != oldMode) m_treeView.Reload();

		m_treeView.OnGUI(new Rect(0, 20, position.width, position.height-20));
	}

	[MenuItem("Window/Objects")]
	static void ShowWindow() {
		var window = GetWindow<ObjectWindow>();
		window.titleContent = new GUIContent("Objects");
		window.Show();
	}
}
