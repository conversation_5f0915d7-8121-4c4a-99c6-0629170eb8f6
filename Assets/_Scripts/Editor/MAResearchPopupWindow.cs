using UnityEditor;
using UnityEngine;

public class MAResearchPopupWindow : MonoSing<PERSON><MAResearchPopupWindow>
{

    public static MAResearchPopupWindow Create(Transform _holder, Vector3 _pos)
    {
        var prefab = Resources.Load<MAResearchPopupWindow>("_Prefabs/Research/MAResearchPopupWindow");
        var instance = Instantiate(prefab, _holder);
        instance.transform.localPosition = _pos;
        //instance.Activate();
        return instance;
    }  
}
