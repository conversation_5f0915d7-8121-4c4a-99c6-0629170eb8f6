using UnityEngine;
using UnityEditor;
using System.IO;

public class TransformStateSaver : MonoBehaviour
{
    private const string SaveFileName = "SavedTransformState.json";

    [MenuItem("22Cans/Misc/Direct Child Transforms - Save")]
    public static void SaveTransformState()
    {
        if (Selection.activeTransform == null)
        {
            Debug.LogWarning("Select a GameObject in the hierarchy.");
            return;
        }

        Transform root = Selection.activeTransform;
        TransformStateCollection stateCollection = new TransformStateCollection();

        foreach (Transform child in root)
        {
            TransformState state = new TransformState
            {
                name = child.name,
                position = child.position,
                rotation = child.rotation
            };
            stateCollection.children.Add(state);
        }

        string json = JsonUtility.ToJson(stateCollection, true);
        File.WriteAllText(GetSavePath(), json);

        Debug.Log("Transform states saved to " + GetSavePath());
    }

    [MenuItem("22Cans/Misc/Direct Child Transforms - Restore")]
    public static void RestoreTransformState()
    {
        if (Selection.activeTransform == null)
        {
            Debug.LogWarning("Select a GameObject in the hierarchy.");
            return;
        }

        if (!File.Exists(GetSavePath()))
        {
            Debug.LogError("Saved transform file not found at: " + GetSavePath());
            return;
        }

        string json = File.ReadAllText(GetSavePath());
        TransformStateCollection stateCollection = JsonUtility.FromJson<TransformStateCollection>(json);

        Transform root = Selection.activeTransform;
        foreach (TransformState state in stateCollection.children)
        {
            Transform child = root.Find(state.name);
            if (child != null)
            {
                Undo.RecordObject(child, "Restore Transform State");
                child.position = state.position;
                child.rotation = state.rotation;
            }
        }

        Debug.Log("Transform states restored from " + GetSavePath());
    }

    private static string GetSavePath()
    {
        return Path.Combine(Application.dataPath, SaveFileName);
    }
}
