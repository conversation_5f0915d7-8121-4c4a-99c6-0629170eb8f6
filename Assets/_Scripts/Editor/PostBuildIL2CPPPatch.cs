// Assets/Editor/PostBuildIL2CPPPatch.cs
using UnityEngine;
using UnityEditor;
using UnityEditor.Callbacks;
using System.IO;
using System.Text;
using System.Text.RegularExpressions;

public class PostBuildIL2CPPPatch
{
    [PostProcessBuild]
    public static void OnPostprocessBuild(BuildTarget target, string buildPath)
    {
        var roots = new System.Collections.Generic.List<string>();

        // 1. Built player Data folder
        string dataFolder = GetDataFolder(buildPath, target);
        if (Directory.Exists(dataFolder))
            roots.Add(dataFolder);

        // 2. Bee artifacts (Editor IL2CPP staging)
        string projectDir = Path.GetDirectoryName(Application.dataPath);
        string beeRoot = Path.Combine(projectDir, "Library", "Bee", "artifacts");
        if (Directory.Exists(beeRoot))
            roots.Add(beeRoot);

        if (roots.Count == 0)
        {
            Debug.LogWarning("[IL2CPPPatch] No candidate roots found for il2cppOutput.");
            return;
        }

        // 3. For each root, find all il2cppOutput folders
        foreach (var root in roots)
        {
            foreach (var il2cppDir in Directory.GetDirectories(root, "il2cppOutput", SearchOption.AllDirectories))
                PatchAllCppIn(il2cppDir);
        }
    }

    static string GetDataFolder(string buildPath, BuildTarget target)
    {
        if (target == BuildTarget.StandaloneWindows || target == BuildTarget.StandaloneWindows64)
        {
            var dir = Path.GetDirectoryName(buildPath);
            var name = Path.GetFileNameWithoutExtension(buildPath);
            return Path.Combine(dir, name + "_Data");
        }
        else if (target == BuildTarget.StandaloneOSX)
        {
            return Path.Combine(buildPath, "Contents", "Resources", "Data");
        }
        else
        {
            var parent = Path.GetDirectoryName(buildPath);
            var name = Path.GetFileNameWithoutExtension(buildPath);
            return Path.Combine(parent, name + "_Data");
        }
    }

    static void PatchAllCppIn(string il2cppDir)
    {
        var cppFiles = Directory.GetFiles(il2cppDir, "*.cpp", SearchOption.AllDirectories);
        foreach (var file in cppFiles)
            PatchIfContainsFinalizerInvoke(file);
    }

    static void PatchIfContainsFinalizerInvoke(string cppFilePath)
    {
        string code = File.ReadAllText(cppFilePath, Encoding.UTF8);

        // look for the finalizer invocation call
        var pattern = @"\bGC_InvokeFinalizers\s*\(\s*\)\s*;";
        if (!Regex.IsMatch(code, pattern))
        {
            // no invocation here
            return;
        }

        // wrap all occurrences
        var replacement =
@"try
{
    $0
}
catch(...)
{
    // Swallow IL2CPP finalizer exception to prevent abort
    il2cpp::vm::Debug::LogError(il2cpp::vm::String::New(""[IL2CPPPatch] Caught exception in finalizer thread""));
}";
        // $0 refers to the entire match (the call and semicolon)
        string newCode = Regex.Replace(code, pattern, replacement);

        File.WriteAllText(cppFilePath, newCode, Encoding.UTF8);
        Debug.Log($"[IL2CPPPatch] Patched {Path.GetFileName(cppFilePath)}");
    }
}
