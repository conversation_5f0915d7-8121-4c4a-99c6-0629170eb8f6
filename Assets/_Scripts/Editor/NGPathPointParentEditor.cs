#if CHOICES
using System.Collections.Generic;
using UnityEngine;
using UnityEditor.SceneManagement;
using UnityEngine.UIElements;
using UnityEditor;
using UnityEditor.VersionControl;

[CanEditMultipleObjects]
[CustomEditor(typeof(NGPathPointParent))]
public class NGPathPointParentEditor : Editor
{
    public override VisualElement CreateInspectorGUI()
    {
        NGPathPointParent pathPointParent = (NGPathPointParent)target;
        List<Vector3> v3s = pathPointParent.PathControlPoints; //will simply add the sibling indices to the transform names
        return base.CreateInspectorGUI();
    }

    private string m_currentSelectedObjectScenePath = "";

    private int m_iSelectedPathPoint = -1;
    private Vector3 m_selectedPathPointPos = Vector3.zero;
    public override void OnInspectorGUI()
    {
        base.OnInspectorGUI();
        
        NGPathPointParent pathPointParent = (NGPathPointParent)target;
        
        bool drawGizmos = GUILayout.Toggle(pathPointParent.DrawEditorGizmos, $"Draw gizmos");
        if(pathPointParent.DrawEditorGizmos != drawGizmos)
        {
            pathPointParent.DrawEditorGizmos = drawGizmos;
        }

        GameObject[] selectedObjects = UnityEditor.Selection.gameObjects;
        Transform[] currentScenePathPointTransforms = pathPointParent.PathPointTransforms;
        bool isRoot = System.Array.Find(selectedObjects, x => x == pathPointParent.gameObject) != null;
        
        bool changesAvailable = false;
        
        if(GUILayout.Button($"Find Path of Containing Prefab to Save to {(!string.IsNullOrEmpty(pathPointParent.m_parentPrefabPath) ? ($"Currently '{pathPointParent.m_parentPrefabPath}'") : (""))}"))
        {
            m_currentSelectedObjectScenePath = Selection.activeGameObject.name;
            Debug.Log($"NGPathPointParentEditor - currentSelectedObjectPath {m_currentSelectedObjectScenePath}");
            GameObject linkedPrefab = PrefabUtility.GetNearestPrefabInstanceRoot(Selection.activeGameObject);
            if(linkedPrefab != null)
            {
                UnityEngine.Object linkedPrefabParent = PrefabUtility.GetPrefabParent(linkedPrefab);
                if(linkedPrefabParent == null) Debug.Log($"NGPathPointParentEditor - linkedPrefabParent null");
                
                PrefabStage prefabStage = PrefabStageUtility.GetCurrentPrefabStage();
                string assetPath = null;
                if(prefabStage != null)
                {
                    assetPath = prefabStage.assetPath;
                    Debug.Log($"NGPathPointParentEditor - In Prefab Stage for root {prefabStage.prefabContentsRoot.name}, using asset path: {prefabStage.assetPath}");
                }
                else
                {
                    linkedPrefab = PrefabUtility.GetNearestPrefabInstanceRoot(Selection.activeGameObject);
                    assetPath = AssetDatabase.GetAssetPath(linkedPrefabParent);
                    Debug.Log($"NGPathPointParentEditor - GetNearestPrefabInstanceRoot {linkedPrefab.name}");
                }
                Debug.Log($"NGPathPointParentEditor - containingPrefabPath found: {assetPath}");
                
                string searchForStart = "Resources/", searchForEnd = ".prefab";
                int iResourcesStringEnd = assetPath.LastIndexOf(searchForStart) + searchForStart.Length;
                int iTypeEnd = assetPath.IndexOf(searchForEnd);
                string containingPrefabPath = assetPath.Substring(iResourcesStringEnd, iTypeEnd - iResourcesStringEnd);
                Debug.Log($"NGPathPointParentEditor - Current Prefab Resources Path: {containingPrefabPath}");

                pathPointParent = (NGPathPointParent)target;
                if(!string.IsNullOrEmpty(containingPrefabPath) &&
                   pathPointParent.m_parentPrefabPath != containingPrefabPath)
                {
                    if(prefabStage != null)
                    {
                        NGPathPointParent pathPointParentToSave = prefabStage.prefabContentsRoot.GetComponentInChildren<NGPathPointParent>(true);
                        pathPointParentToSave.m_parentPrefabPath = containingPrefabPath;
                        if(PrefabUtility.SaveAsPrefabAsset(prefabStage.prefabContentsRoot, assetPath, out bool savedSuccessfully))
                        {
                            Debug.Log($"NGPathPointParentEditor - prefab savedSuccessfully: {savedSuccessfully}");
                        }
                    }
                    else
                    {
                        GameObject loadedAsset = Resources.Load<GameObject>(containingPrefabPath);
                        GameObject prefabParent = linkedPrefabParent as GameObject;
                        NGPathPointParent pathPointParentToSave = prefabParent.GetComponentInChildren<NGPathPointParent>(true);

                        string pathToSaveTo = loadedAsset != null ? containingPrefabPath : assetPath;
                        
                        Debug.Log($"NGPathPointParentEditor - Attempting Changes to prefab: {pathToSaveTo}");
                        
                        EditorUtility.SetDirty(pathPointParentToSave);
                        pathPointParentToSave.m_parentPrefabPath = containingPrefabPath;
                        AssetDatabase.SaveAssets();
                        EditorUtility.ClearDirty(pathPointParentToSave);
                        
                        TryReloadScene();

                        GameObject foundPreviousObject = GameObject.Find(m_currentSelectedObjectScenePath);
                        Selection.activeGameObject = foundPreviousObject;
                    
                        // else
                        // {
                        //     Debug.LogError("NGPathPointParentEditor - This Game Object is not linked to a prefab");
                        // }
                    }
                }
            }
        }

        if(!string.IsNullOrEmpty(pathPointParent.m_parentPrefabPath))
        {
            if(GUILayout.Button("Save Path Point Changes"))
            {
                pathPointParent = (NGPathPointParent)target;

                Object loadedAsset = Resources.Load<Object>(pathPointParent.m_parentPrefabPath);;
                if(loadedAsset == null)
                {
                    loadedAsset = AssetDatabase.LoadAssetAtPath<Object>(pathPointParent.m_parentPrefabPath);
                }

                if(loadedAsset == null)
                {
                    Debug.Log($"NGPathPointParentEditor - Resources.Load {pathPointParent.m_parentPrefabPath} is null");
                    return;
                }

                NGPathPointParent currentScenePathPointParent =
                    Selection.activeGameObject.GetComponent<NGPathPointParent>();
                Transform[] newPathPointTransforms = currentScenePathPointParent.PathPointTransforms;
        
                GameObject prefabParent = loadedAsset as GameObject;
                NGPathPointParent pathPointAssetToSave = System.Array.Find(
                    prefabParent.GetComponentsInChildren<NGPathPointParent>(true), x => x.name == pathPointParent.name);
                if(pathPointAssetToSave == null)
                {
                    Debug.Log(
                        $"NGPathPointParentEditor - prefab at path {pathPointParent.m_parentPrefabPath} has no gameObject named {pathPointParent.name}");
                    return;
                }

                EditorUtility.SetDirty(prefabParent);

                Transform[] assetPathPoints = pathPointAssetToSave.PathPointTransforms;
                int i = 0;
                for(; i < newPathPointTransforms.Length && i < assetPathPoints.Length; i++)
                {
                    if(!assetPathPoints[i].position.Equals(newPathPointTransforms[i].position))
                    {
                        Debug.Log($"PathPoints - re-applying pathPoint index {i} {assetPathPoints[i].position} = {newPathPointTransforms[i].position}");
                        assetPathPoints[i].position = newPathPointTransforms[i].position;
                        changesAvailable = true;
                    }
                }

                for(; i < newPathPointTransforms.Length; i++)
                {
                    Debug.LogError($"Error - Cannot Save Prefab! Found a new Path Point Transform AT POSITION: {newPathPointTransforms[i].position} / index: {i}, please go into prefab edit mode to add them & save. A script can't add gameObjects + save a prefab");
                    changesAvailable = true;
                }

                for(; i < assetPathPoints.Length; i++)
                {
                    Transform trToDelete = pathPointAssetToSave.transform.GetChild(i);
                    Debug.LogError($"Error - Cannot Save Prefab! Found missing Path Point Transform at child index: {i} / position: {trToDelete.position} compared to the original prefab, please go into prefab edit mode to remove transforms & save. A script can't remove gameObjects from + save a prefab");
                    changesAvailable = true;
                }

                if(changesAvailable)
                {
                    AssetDatabase.SaveAssets();
                    if(NGBoatControl.Me != null)
                    {
                        List<NGBoat> boatsWithPath = NGManager.Me.m_NGBoatList.FindBoats(x => x.CurrentPath == currentScenePathPointParent);
                        foreach(NGBoat boat in boatsWithPath)
                        {
                            boat.SetPath(currentScenePathPointParent);
                        }
                    }
                }

                EditorUtility.ClearDirty(prefabParent);

                TryReloadScene();
            }
        }
    }

    private bool TryReloadScene()
    {
        if(!Application.isPlaying)
        {
            PrefabStage prefabStage = PrefabStageUtility.GetCurrentPrefabStage();
            if(prefabStage == null)
            {
                Debug.Log($"NGPathPointParentEditor - Reloading Scene to discard Scene changes");

                string scenePath = EditorSceneManager.GetActiveScene().path;
                EditorSceneManager.OpenScene(scenePath);
                return true;
            }
        }

        return false;
    }
}
#endif