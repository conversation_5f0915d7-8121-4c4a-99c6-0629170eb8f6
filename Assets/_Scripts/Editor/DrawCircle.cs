using System;
using UnityEngine;

// JS - 2024-11-16
// This script is used to calculate the segments used for the line renderer.
// This is only needed at design time, it should be disabled after use. 

[RequireComponent(typeof(LineRenderer))]
public class CircleDrawer : MonoBehaviour
{
    [SerializeField, Range(0.1f, 10f)]
    private float radius = 1f; // Radius of the circle

    [SerializeField, Range(3, 100)]
    private int segments = 32; // Number of segments for the circle
    
    public bool isNoisy = false;
    public float noiseMagnitude = 0.7f;
    public float noiseFrequency = 2f;
    
    public LineRenderer lineRenderer;

    private void Awake()
    {
        if (isNoisy)
        {
            DrawNoisyCircle(noiseMagnitude, noiseFrequency);   
        }
        else
        {
            DrawCircle();
        }
    }

    private void DrawCircle()
    {
        // Set the number of positions in the LineRenderer
        lineRenderer.positionCount = segments + 1;

        // Calculate positions for the circle
        for (int i = 0; i <= segments; i++)
        {
            float angle = (float)i / segments * Mathf.PI * 2f;
            float x = Mathf.Cos(angle) * radius;
            float z = Mathf.Sin(angle) * radius;

            lineRenderer.SetPosition(i, new Vector3(x, 0f, z));
        }

        // Close the loop
        lineRenderer.loop = true;
    }
    
    public void DrawNoisyCircle(float noiseMagnitude, float noiseFrequency)
    {
        // Set the number of positions in the LineRenderer
        lineRenderer.positionCount = segments + 1;

        // Calculate positions for the noisy circle
        for (int i = 0; i <= segments; i++)
        {
            float angle = (float)i / segments * Mathf.PI * 2f;

            // Calculate base circle positions
            float x = Mathf.Cos(angle) * radius;
            float z = Mathf.Sin(angle) * radius;

            // Apply Perlin noise to the radius
            float noise = Mathf.PerlinNoise(
                Mathf.Cos(angle) * noiseFrequency,
                Mathf.Sin(angle) * noiseFrequency
            ) * noiseMagnitude;
            
            float noisyHieght = noise - noiseMagnitude/2;

            lineRenderer.SetPosition(i, new Vector3(x, noisyHieght, z));
        }

        // Close the loop
        lineRenderer.loop = true;
    }

    // Update the circle in real-time if radius or segments are changed in the inspector
    private void OnValidate()
    {
        if (lineRenderer == null) return;
        
        if (isNoisy)
        {
            DrawNoisyCircle(noiseMagnitude, noiseFrequency);   
        }
        else
        {
            DrawCircle();
        }
    }
}