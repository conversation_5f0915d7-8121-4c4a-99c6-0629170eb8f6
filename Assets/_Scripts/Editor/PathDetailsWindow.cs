using System.Collections.Generic;
using UnityEngine;
using UnityEditor;

public class PathDetailsWindow : EditorWindow
{
    Vector2 m_scroller;
    void OnGUI()
    {
        var runCommand = false;
        var normalClr = new Color(1, 1, .85f);
        var constructionClr = new Color(1, .65f, .65f);
        var playerClr = new Color(1, .65f, 1);
        
        var windowWidth = position.width;
        var windowHeight = position.height;

        const float c_colWidth_Index = 30;
        const float c_colWidth_Length = 40;
        const float c_colWidth_Constructed = 40;
        const float c_colWidth_IsPlayer = 30;
        const float c_colWidth_Set = 150;
        const float c_lineHeight = 15;
            
        var entryStyle = new GUIStyle(EditorStyles.label);
        entryStyle.richText = true;
        
        var pathset = RoadManager.Me.m_pathSet.CurrentPaths;
        GUILayout.BeginHorizontal();
        GUILayout.Label("#", entryStyle, GUILayout.Width(c_colWidth_Index));
        GUILayout.Label("Length", entryStyle, GUILayout.Width(c_colWidth_Length));
        GUILayout.Label("Built %", entryStyle, GUILayout.Width(c_colWidth_Constructed));
        GUILayout.Label("\U0001F6B6", entryStyle, GUILayout.Width(c_colWidth_IsPlayer));
        GUILayout.Label("RoadSet", entryStyle, GUILayout.Width(c_colWidth_Set));
        GUILayout.EndHorizontal();
        m_scroller = GUILayout.BeginScrollView(m_scroller);
        for (int i = 0; i < pathset.Count; ++i)
        {
            var path = pathset[i];
            path.UpdateSmoothed();
            var length = path.TotalLength();
            var constructionPercent = path.GetCompletedPercent() * 100;
            
            GUILayout.BeginHorizontal();
            bool pushed = false;
            
            GUI.color = Color.white;
            pushed |= GUILayout.Button($"{i}", entryStyle, GUILayout.Width(c_colWidth_Index), GUILayout.Height(c_lineHeight));

            GUI.color = path.m_isPlayerPath ? playerClr : (constructionPercent < 100 ? constructionClr : normalClr);

            pushed |= GUILayout.Button($"{length:n1}", entryStyle, GUILayout.Width(c_colWidth_Length), GUILayout.Height(c_lineHeight));
            pushed |= GUILayout.Button($"{constructionPercent:n1}", entryStyle, GUILayout.Width(c_colWidth_Constructed), GUILayout.Height(c_lineHeight));
            
            GUI.color = Color.white;
            if (GUILayout.Button(new GUIContent(path.m_isPlayerPath ? "\U0001F6B6" : "", "Toggle player path"), GUILayout.Width(c_colWidth_IsPlayer), GUILayout.Height(c_lineHeight)))
                path.m_isPlayerPath = !path.m_isPlayerPath;

            GUI.color = path.m_isPlayerPath ? playerClr : (constructionPercent < 100 ? constructionClr : normalClr);

            if (GUILayout.Button(path.Set.name, entryStyle, GUILayout.Width(c_colWidth_Set), GUILayout.Height(c_lineHeight)))
                Selection.activeObject = path.Set;
            
            GUI.color = Color.white;
            if (GUILayout.Button(new GUIContent("\U0001F3D7", "Toggle between fully constructed and fully unconstructed"), GUILayout.Width(20), GUILayout.Height(c_lineHeight)))
            {
                path.SetCompletedPercent(path.GetCompletedPercent() < .5f ? 1 : 0);
            }
            GUILayout.EndHorizontal();

            if (pushed)
            {
                var handle = path.GetHandle(true);
                if (handle != null)
                    GameManager.Me.FocusCamera(handle.transform, 1);
            }
        }
        GUILayout.EndScrollView();
    }
    
    [MenuItem("Window/Path Details Window")]
    static void ShowWindow()
    {
        var window = GetWindow<PathDetailsWindow>();
        window.titleContent = new GUIContent("Paths");
        window.minSize = new Vector2(300, 80);
        window.maxSize = new Vector2(800, 800);
        window.Show();
    }
}
