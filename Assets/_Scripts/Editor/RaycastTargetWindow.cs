using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using UnityEditor.IMGUI.Controls;
using UnityEngine.SceneManagement;

class RaycastTargetTreeView : TreeView {
	private bool m_showInactive = true;
	private bool m_showImages = true;
	private bool m_onlyContainedTargets = false;
	private bool m_expandAll = false;

	public void ForceExpandAll()
	{
		m_expandAll = true;
		Reload();
	}

	public bool ShowInactive
	{
		get { return m_showInactive; }
		set
		{
			if (value != m_showInactive)
			{
				m_showInactive = value;
				Reload();
			}
		}
	}
	public bool ShowImages {
		get { return m_showImages; }
		set {
			if (value != m_showImages)
			{
				m_showImages = value;
				Reload();
			}
		}
	}
	public bool OnlyContainedTargets
	{
		get { return m_onlyContainedTargets; }
		set
		{
			if (value != m_onlyContainedTargets)
			{
				m_onlyContainedTargets = value;
				Reload();
			}
		}
	}

	public RaycastTargetTreeView(TreeViewState treeViewState) : base(treeViewState) {
		m_baseStyle = new GUIStyle(GUI.skin.label); m_baseStyle.richText = true;
		Reload();
	}

	protected override TreeViewItem BuildRoot() {
		return new TreeViewItem { id = 0, depth = -1 };
	}

	public enum DisplayMode {
		Objects,
		DisabledObjects,
		Renderers,
	}
	public static DisplayMode s_renderMode = DisplayMode.Objects;

	GUIStyle m_baseStyle;
	
	public void SelectAll()
	{
		int bestCount = 0;
		List<int> bestList = null;
		foreach (var kvp in m_allEntries)
		{
			if (kvp.Value.Count > bestCount)
			{
				bestCount = kvp.Value.Count;
				bestList = kvp.Value;
			}
		}
		if (bestList != null)
			Selection.instanceIDs = bestList.ToArray();
	}

	public string GetAllLabel()
	{
		int bestCount = 0;
		string bestType = null;
		foreach (var kvp in m_allEntries)
		{
			if (kvp.Value.Count > bestCount)
			{
				bestCount = kvp.Value.Count;
				bestType = GetTypeShort(kvp.Key);
			}
		}
		return $"{bestType} x {bestCount}";
	}

	private static string GetTypeShort(System.Type _t)
	{
		var type = _t.ToString();
		return type[(type.LastIndexOf(".") + 1)..];
	}

	private static string GetTypeLabel(System.Type _t)
	{
		var type = GetTypeShort(_t);
		if (type is "Image" or "RawImage") return "I";
		if (type.StartsWith("TextMeshPro")) return "T";
		return type;
	}

	public class PathEntry
	{
		public Transform m_transform;
		public TreeViewItem m_item;
		public List<PathEntry> m_children;
		public bool m_isGraphic = false;
		public int m_numImages = 0, m_numNonImages = 0;
	}
	Dictionary<int, PathEntry> m_pathEntries = new();
	Dictionary<System.Type, List<int>> m_allEntries = new();

	private bool CheckExpanded(int _id)
	{
		bool isExpanded = IsExpanded(_id);
		if (m_expandAll && !isExpanded)
		{
			SetExpanded(_id, true);
			return true;
		}
		return isExpanded;
	}

	protected override IList<TreeViewItem> BuildRows(TreeViewItem root) {
		void AddItem(UnityEngine.UI.Graphic _entry, string _type, string _clr, bool _isImage)
		{
			if (m_allEntries.TryGetValue(_entry.GetType(), out var list) == false)
				list = m_allEntries[_entry.GetType()] = new List<int>();
			list.Add(_entry.gameObject.GetInstanceID());
			var transforms = new List<Transform>();
			var trans = _entry.transform;
			while (trans != null)
			{
				transforms.Add(trans);
				trans = trans.parent;
			}
			PathEntry parentEntry = null;
			for (int i = transforms.Count - 1; i >= 0; --i)
			{
				var id = transforms[i].gameObject.GetInstanceID();
				var isExpanded = i == 0 || CheckExpanded(id);
				int depth = transforms.Count - 1 - i;
				if (m_pathEntries.TryGetValue(id, out var pathEntry) == false)
				{
					var item = new TreeViewItem(id, depth, $"<color=#c0c0a0>{transforms[i].name}</color>");
					pathEntry = new PathEntry {
						m_transform = transforms[i],
						m_children = new List<PathEntry>(),
						m_item = item,
					};
					m_pathEntries[id] = pathEntry;
					if (i == transforms.Count - 1)
						root.AddChild(pathEntry.m_item);
				}
				if (i == 0)
				{
					pathEntry.m_item.displayName = $"<color=#{_clr}>[{_type}] {transforms[i].name}</color>";
					pathEntry.m_isGraphic = true;
				}
				if (isExpanded == false && pathEntry.m_item.children == null)
					pathEntry.m_item.children = CreateChildListForCollapsedParent();
				if (i > 0)
				{
					if (_isImage) ++pathEntry.m_numImages;
					else ++pathEntry.m_numNonImages;
				}
				if (parentEntry != null)
				{
					if (parentEntry.m_children.Contains(pathEntry) == false)
					{
						parentEntry.m_children.Add(pathEntry);
						parentEntry.m_item.AddChild(pathEntry.m_item);
					}
				}
				if (isExpanded == false)
					break;
				parentEntry = pathEntry;
			}
		}

		var rows = GetRows() ?? new List<TreeViewItem>(200);
		void AddOpenItems(PathEntry _entry)
		{
			rows.Add(_entry.m_item);
			if (IsExpanded(_entry.m_item.id))
				foreach (var child in _entry.m_children)
					AddOpenItems(child);
		}

		if (root.children != null) root.children.Clear();
		rows.Clear();
		m_allEntries.Clear();
		m_pathEntries.Clear();
		UnityEngine.UI.Graphic[] entries;
		if (UnityEditor.SceneManagement.PrefabStageUtility.GetCurrentPrefabStage() != null)
			entries = UnityEditor.SceneManagement.PrefabStageUtility.GetCurrentPrefabStage().prefabContentsRoot.GetComponentsInChildren<UnityEngine.UI.Graphic>(true);
		else
			entries = Transform.FindObjectsByType<UnityEngine.UI.Graphic>(FindObjectsInactive.Include, FindObjectsSortMode.None);
		foreach (var entry in entries)
		{
			if (entry.raycastTarget == false) continue;
			if (entry.enabled == false && m_showInactive == false) continue;
			if (m_onlyContainedTargets)
			{
				if (entry.transform.parent == null) continue;
				var parentGraphic = entry.transform.parent.GetComponent<UnityEngine.UI.Graphic>();
				if (parentGraphic == null) continue;
				if (parentGraphic.enabled == false || parentGraphic.raycastTarget == false) continue;
			}
			var type = GetTypeLabel(entry.GetType());
			var isImage = type == "I";
			var clr = isImage ? "ffffff" : "ffc0c0";
			if (entry.enabled == false) clr = "a0a0c0";
			if (isImage == false || m_showImages)
				AddItem(entry, type, clr, isImage);
		}
		foreach (var kvp in m_pathEntries)
			if (kvp.Value.m_numImages > 0 || kvp.Value.m_numNonImages > 0)
				kvp.Value.m_item.displayName = $"{kvp.Value.m_item.displayName}  <color=#c0c0c0>{NumStr(kvp.Value.m_numImages)}</color> <color=#c0a0a0>{NumStr(kvp.Value.m_numNonImages)}</color>";
		// now put the items into rows
		foreach (var item in root.children)
			AddOpenItems(m_pathEntries[item.id]);
		m_expandAll = false;
		return rows;
	}

	private static string NumStr(int _n)
	{
		if (_n == 0) return "";
		return $"{_n}";
	}

	protected override IList<int> GetAncestors(int id) {
		// The backend needs to provide us with this info since the item with id
		// may not be present in the rows
		var transform = GetGameObject(id).transform;

		List<int> ancestors = new List<int>();
		while (transform.parent != null) {
			ancestors.Add(transform.parent.gameObject.GetInstanceID());
			transform = transform.parent;
		}

		return ancestors;
	}

	protected override IList<int> GetDescendantsThatHaveChildren(int id) {
		Stack<Transform> stack = new Stack<Transform>();

		var start = GetGameObject(id).transform;
		stack.Push(start);

		var parents = new List<int>();
		while (stack.Count > 0) {
			Transform current = stack.Pop();
			parents.Add(current.gameObject.GetInstanceID());
			for (int i = 0; i < current.childCount; ++i) {
				if (current.childCount > 0)
					stack.Push(current.GetChild(i));
			}
		}

		return parents;
	}

	GameObject GetGameObject(int instanceID) {
		return (GameObject)EditorUtility.InstanceIDToObject(instanceID);
	}

	protected override void RowGUI(RowGUIArgs args) {
		base.showAlternatingRowBackgrounds = true;
		var rect = args.rowRect;
		rect.x += baseIndent + foldoutWidth + depthIndentWidth * args.item.depth;
		EditorGUI.LabelField(rect, args.label, m_baseStyle);
	}

	protected override void SelectionChanged (IList<int> selectedIds)
	{
		Selection.instanceIDs = (int[])selectedIds;
	}
}

public class RaycastTargetWindow : EditorWindow {

	[SerializeField] TreeViewState m_treeViewState;
	RaycastTargetTreeView m_treeView;

	void OnEnable() {
		if (m_treeViewState == null)
			m_treeViewState = new TreeViewState();
	}

	void OnGUI() {
		if (m_treeView == null)
			m_treeView = new RaycastTargetTreeView(m_treeViewState);
		if (GUI.Button(new Rect(10, 0, 80, 20), "Refresh"))
			m_treeView.Reload();
		if (GUI.Button(new Rect(10 + 80 + 10, 0, 120, 20), "Expand All"))
			m_treeView.ForceExpandAll();
		if (GUI.Button(new Rect(10 + 80 + 10 + 120 + 10, 0, 240, 20), $"Select All ({m_treeView.GetAllLabel()})"))
			m_treeView.SelectAll();
		m_treeView.ShowInactive = GUI.Toggle(new Rect(10, 20, 100, 20), m_treeView.ShowInactive, "Show Inactive");
		m_treeView.ShowImages = GUI.Toggle(new Rect(10 + 100 + 10, 20, 100, 20), m_treeView.ShowImages, "Show Images");
		m_treeView.OnlyContainedTargets = GUI.Toggle(new Rect(10 + 100 + 10 + 100 + 10, 20, 100, 20), m_treeView.OnlyContainedTargets, "Only Contained");
		m_treeView.OnGUI(new Rect(0, 40, position.width, position.height-40));
	}

	[MenuItem("Window/RaycastTargets")]
	static void ShowWindow() {
		var window = GetWindow<RaycastTargetWindow>();
		window.titleContent = new GUIContent("RaycastTargets");
		window.Show();
	}
}
