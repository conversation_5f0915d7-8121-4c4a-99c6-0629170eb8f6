using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;

class FBXPostProcess : AssetPostprocessor
{
    void RemovePrefixRecursive(Transform _t, string _prefix)
    {
        if (_t.name.StartsWith(_prefix))
            _t.name = _t.name[_prefix.Length..];
        foreach (Transform child in _t)
            RemovePrefixRecursive(child, _prefix);
    }
    
    private void OnPostprocessAnimation(GameObject root, AnimationClip clip)
    {
        var assetPathLower = assetPath.ToLower();
        if (assetPathLower.EndsWith(".fbx") && assetPathLower.StartsWith("assets/_art/characters/"))
        {
            var bindings = AnimationUtility.GetCurveBindings(clip);
            int rootNameIndex = bindings[0].path.IndexOf(GlobalData.c_avatarRootName);
            if (rootNameIndex > 0)
            {
                var prefix = bindings[0].path[0..rootNameIndex];
                foreach (var binding in bindings)
                {
                    if (binding.path.StartsWith(prefix))
                    {
                        var newBinding = binding;
                        newBinding.path = binding.path.Replace(prefix, "");
                        var curve = AnimationUtility.GetEditorCurve(clip, binding);
                        AnimationUtility.SetEditorCurve(clip, binding, null);
                        AnimationUtility.SetEditorCurve(clip, newBinding, curve);
                    }
                }
            }
        }
    }

    private static string[] s_processPaths = {
        "assets/_art/characters/",
        "assets/_art/products/_ma_armour/",
        "assets/_art/products/_ma_clothes/",
    };
    private bool IsInProcessPath(string _pathLower)
    {
        foreach (var path in s_processPaths)
            if (_pathLower.StartsWith(path))
                return true;
        return false;
    }
    
    void OnPostprocessModel(GameObject go)
    {
        var assetPathLower = assetPath.ToLower();
        if (assetPathLower.EndsWith(".fbx") && IsInProcessPath(assetPathLower))
        {
            ModelImporter modelImporter = (ModelImporter)assetImporter;

            for (int i = 0; i < go.transform.childCount; ++i)
            {
                var potentialRoot = go.transform.GetChild(i);
                int rootNameIndex = potentialRoot.name.IndexOf(GlobalData.c_avatarRootName);
                if (rootNameIndex > 0)
                {
                    var prefix = potentialRoot.name[0..rootNameIndex];
                    Debug.Log($"Found root with prefix - {prefix} - on child {i}");
                    RemovePrefixRecursive(potentialRoot, prefix);
                }
            }

            var root = go.transform.Find(GlobalData.c_avatarRootName);
            if (root != null)
            {
                modelImporter.motionNodeName = GlobalData.c_avatarRootName;
                //var humanDescription = modelImporter.humanDescription;
                //humanDescription.
            }

            FindAndAttach(go.transform, "Hand_Attach_Right", "HandAttach_R");
            FindAndAttach(go.transform, "Hand_Attach_Left", "HandAttach_L");
            FindAndAttach(go.transform, "Head_Attach", "Head");
            FindAndAttach(go.transform, "mixamorig:Spine2", "Chest");
            return;
            var armature = go.transform.Find("Armature");
            if (armature != null)
            {
                foreach (Transform t in armature)
                    t.SetParent(armature.parent, true);
                Object.DestroyImmediate(armature.gameObject);
                // now add attach objects
            }

        }
    }
    void FindAndAttach(Transform _t, string _name, string _attachName)
    {
        if (_t.name == _name)
        {
            var go = new GameObject(_attachName);
            go.transform.SetParent(_t, false);
            return;
        }
        foreach (Transform child in _t)
            FindAndAttach(child, _name, _attachName);
    }
}
