using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using UnityEditor.IMGUI.Controls;
using UnityEngine.SceneManagement;

class ShaderTreeView : TreeView {
	private bool m_showInactive = true;
	public bool ShowInactive {
		get { return m_showInactive; }
		set {
			if (value != m_showInactive)
			{
				m_showInactive = value;
				Reload();
			}
		}
	}
	public ShaderTreeView(TreeViewState treeViewState) : base(treeViewState) {
		m_baseStyle = new GUIStyle(GUI.skin.label); m_baseStyle.richText = true;
		Reload();
	}

	protected override TreeViewItem BuildRoot() {
		return new TreeViewItem { id = 0, depth = -1 };
	}

	public enum DisplayMode {
		Objects,
		DisabledObjects,
		Renderers,
	}
	public static DisplayMode s_renderMode = DisplayMode.Objects;

	GUIStyle m_baseStyle;

	Dictionary<string, Material> m_allMaterials = null;
	void CollectAllMaterials()
	{
		if (m_allMaterials != null) return;
		m_allMaterials = new();
		string[] guids = AssetDatabase.FindAssets("t:Material");
		foreach (string guid in guids)
		{
			string path = AssetDatabase.GUIDToAssetPath(guid);
			Material material = AssetDatabase.LoadAssetAtPath<Material>(path);
			m_allMaterials[material.name] = material;
		}
	}

	Material DeInstanceMaterial(Material _mat)
	{
		var originalName = _mat.name;
		if (originalName.EndsWith(" (Instance)"))
			originalName = originalName.Replace(" (Instance)", "");
		CollectAllMaterials();
		if (m_allMaterials.TryGetValue(originalName, out Material original))
			return original;
		return _mat;
	}

	protected override IList<TreeViewItem> BuildRows(TreeViewItem root) {
		var rows = GetRows() ?? new List<TreeViewItem>(200);

		m_allMaterials = null;
		rows.Clear();
		
		var shaders = new List<Shader>();
		var found = new Dictionary<Shader, List<Material>>();
		var foundMaterials = new Dictionary<Material, List<Renderer>>();
		var shadersEnabled = new HashSet<Shader>();
		var materialsEnabled = new HashSet<Material>();
		var renderersEnabled = new HashSet<Renderer>();
		var renderers = Object.FindObjectsByType<Renderer>(m_showInactive ? FindObjectsInactive.Include : FindObjectsInactive.Exclude, FindObjectsSortMode.None);
		for (int i = 0; i < renderers.Length; ++i)
		{
			var renderer = renderers[i];
			bool isActive = renderer.gameObject.activeInHierarchy;
			var materials = renderer.sharedMaterials;
			for (int j = 0; j < materials.Length; ++j)
			{
				var material = materials[j];
				if (material == null) continue;
				material = DeInstanceMaterial(material);
				if (foundMaterials.ContainsKey(material) == false)
					foundMaterials.Add(material, new List<Renderer>());
				foundMaterials[material].AddUnique(renderer);
				var shader = material.shader;
				if (shader == null) continue;
				shaders.AddUnique(shader);
				if (found.ContainsKey(shader) == false)
					found.Add(shader, new List<Material>());
				found[shader].AddUnique(material);
				if (isActive)
				{
					shadersEnabled.Add(shader);
					materialsEnabled.Add(material);
					renderersEnabled.Add(renderer);
				}
			}
		}
		shaders.Sort((a, b) => a.name.CompareTo(b.name));

		const string s_shaderColour = "<color=#a0ffa0>";
		const string s_materialColour = "<color=#ffffa0>";
		const string s_rendererColour = "<color=#ffa0a0>";
		const string s_shaderDisabledColour = "<color=#80b080>";
		const string s_materialDisabledColour = "<color=#b0b080>";
		const string s_rendererDisabledColour = "<color=#b08080>";
		const string s_countColour = "<color=#ffffff>";
		
		foreach (var shader in shaders)
		{
			var items = found[shader];
			items.Sort((a, b) => a.name.CompareTo(b.name));
			var item = new TreeViewItem(shader.GetInstanceID(), 0, $"{(shadersEnabled.Contains(shader) ? s_shaderColour : s_shaderDisabledColour)}{shader.name}</color>   {s_countColour}{items.Count}</color>");
			root.AddChild(item);
			rows.Add(item);
			if (IsExpanded(item.id) == false)
				item.children = CreateChildListForCollapsedParent();
			else
			{
				item.children = new List<TreeViewItem>();
				for (int i = 0; i < items.Count; ++i)
				{
					var material = items[i];
					var rendererList = foundMaterials[material];
					rendererList.Sort((a, b) => a.name.CompareTo(b.name));
					var item2 = new TreeViewItem(material.GetInstanceID(), 1, $"{(materialsEnabled.Contains(material) ? s_materialColour : s_materialDisabledColour)}{material.name}</color>   {s_countColour}{rendererList.Count}</color>");
					item.AddChild(item2);
					rows.Add(item2);
					if (IsExpanded(item2.id) == false)
						item2.children = CreateChildListForCollapsedParent();
					else
					{
						item2.children = new List<TreeViewItem>();
						for (int j = 0; j < rendererList.Count; ++j)
						{
							var renderer = rendererList[j];
							var item3 = new TreeViewItem(renderer.gameObject.GetInstanceID(), 2, $"{(renderersEnabled.Contains(renderer) ? s_rendererColour : s_rendererDisabledColour)}{renderer.name}</color>");
							item2.AddChild(item3);
							rows.Add(item3);
						}
					}
				}
			}
		}
		return rows;
	}
	
	protected override IList<int> GetAncestors(int id) {
		// The backend needs to provide us with this info since the item with id
		// may not be present in the rows
		var transform = GetGameObject(id).transform;

		List<int> ancestors = new List<int>();
		while (transform.parent != null) {
			ancestors.Add(transform.parent.gameObject.GetInstanceID());
			transform = transform.parent;
		}

		return ancestors;
	}

	protected override IList<int> GetDescendantsThatHaveChildren(int id) {
		Stack<Transform> stack = new Stack<Transform>();

		var start = GetGameObject(id).transform;
		stack.Push(start);

		var parents = new List<int>();
		while (stack.Count > 0) {
			Transform current = stack.Pop();
			parents.Add(current.gameObject.GetInstanceID());
			for (int i = 0; i < current.childCount; ++i) {
				if (current.childCount > 0)
					stack.Push(current.GetChild(i));
			}
		}

		return parents;
	}

	GameObject GetGameObject(int instanceID) {
		return (GameObject)EditorUtility.InstanceIDToObject(instanceID);
	}

	protected override void RowGUI(RowGUIArgs args) {
		base.showAlternatingRowBackgrounds = true;
		var rect = args.rowRect;
		rect.x += baseIndent + foldoutWidth + depthIndentWidth * args.item.depth;
		EditorGUI.LabelField(rect, args.label, m_baseStyle);
	}

	protected override void SelectionChanged (IList<int> selectedIds)
	{
		Selection.instanceIDs = (int[])selectedIds;
	}
}

public class ShaderWindow : EditorWindow {

	[SerializeField] TreeViewState m_treeViewState;
	ShaderTreeView m_treeView;

	void OnEnable() {
		if (m_treeViewState == null)
			m_treeViewState = new TreeViewState();
	}

	void OnGUI() {
		if (m_treeView == null)
			m_treeView = new ShaderTreeView(m_treeViewState);
		if (GUI.Button(new Rect(10, 0, 80, 20), "Refresh"))
			m_treeView.Reload();
		m_treeView.ShowInactive = GUI.Toggle(new Rect(10+80+10, 0, 150, 20), m_treeView.ShowInactive, "Show Inactive");
		m_treeView.OnGUI(new Rect(0, 20, position.width, position.height-20));
	}

	[MenuItem("Window/Shaders")]
	static void ShowWindow() {
		var window = GetWindow<ShaderWindow>();
		window.titleContent = new GUIContent("Shaders");
		window.Show();
	}
}
