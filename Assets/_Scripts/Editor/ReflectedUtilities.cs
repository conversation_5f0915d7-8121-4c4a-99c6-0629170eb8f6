using System;
using System.Reflection;
using UnityEditor;
using UnityEngine;

public class ReflectSceneViewOverlay
{
	private static readonly Assembly kAssembly = Assembly.GetAssembly( typeof(EditorWindow) );
	private static readonly Type kSceneViewOverlay = kAssembly.GetType( "UnityEditor.SceneViewOverlay" );
	private static readonly MethodInfo	kWindow = kSceneViewOverlay.GetMethods(BindingFlags.Static | BindingFlags.Public ).Find( (x) => x.Name == "Window" && x.GetParameters().Length == 4 );
	private static readonly Type kSceneViewDelegate = kSceneViewOverlay.GetNestedType("WindowFunction");
	private static readonly object[] kParams = new object[4] { null, null, null, 2 };

	public static void Window( string title, System.Action<UnityEngine.Object, SceneView> sceneViewFunc, int order )
	{
		// Title
		kParams[0] = new GUIContent(title);

		// Drawing callback
		kParams[1] = Delegate.CreateDelegate( kSceneViewDelegate, sceneViewFunc.Target, sceneViewFunc.Method );

		// Order to draw the windows.
		kParams[2] = order;

		// Create the window.
		kWindow.Invoke( null, kParams );
	}
}