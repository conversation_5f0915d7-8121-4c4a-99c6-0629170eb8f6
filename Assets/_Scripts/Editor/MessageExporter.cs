using System.Collections.Generic;
using System.IO;
using System.Text.RegularExpressions;
using UnityEditor;
using UnityEngine;

public class MessageExporter : EditorWindow
{
    private List<string> directoriesToScan = new List<string>();
    private Vector2 scrollPos;
    private string outputCsvPath = "Assets/MessagesOutput.csv";
    
    private string exportTime = "";
    private int messageCount = 0;
    private int wordCount = 0;

    private const string DirsKey = "MessageExporter_Directories";
    private const string OutputKey = "MessageExporter_OutputPath";

    [MenuItem("22Cans/Misc/Message Exporter")]
    public static void ShowWindow()
    {
        GetWindow<MessageExporter>("Message Exporter");
    }
    
    private void OnEnable()
    {
        LoadPrefs();
    }

    private void OnGUI()
    {
        GUILayout.Label("Message Exporter", EditorStyles.boldLabel);
        GUILayout.Space(5);

        GUILayout.Label("Directories to Scan (.MOA files):", EditorStyles.label);

        scrollPos = EditorGUILayout.BeginScrollView(scrollPos, GUILayout.Height(120));
        for (int i = 0; i < directoriesToScan.Count; i++)
        {
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.SelectableLabel(directoriesToScan[i], GUILayout.Height(18));
            if (GUILayout.Button("Remove", GUILayout.Width(70)))
            {
                directoriesToScan.RemoveAt(i);
                SavePrefs();
                break;
            }
            EditorGUILayout.EndHorizontal();
        }
        EditorGUILayout.EndScrollView();

        if (GUILayout.Button("Add Directory"))
        {
            string selected = EditorUtility.OpenFolderPanel("Select Directory to Scan", Application.dataPath, "");
            if (!string.IsNullOrEmpty(selected) && !directoriesToScan.Contains(selected))
            {
                directoriesToScan.Add(selected);
                SavePrefs();
            }
        }

        GUILayout.Space(10);
        GUILayout.Label("CSV Output File Path:", EditorStyles.label);

        EditorGUILayout.BeginHorizontal();
        EditorGUILayout.SelectableLabel(outputCsvPath, GUILayout.Height(18));
        if (GUILayout.Button("Choose File", GUILayout.Width(100)))
        {
            string selected = EditorUtility.SaveFilePanel("Select CSV Output File", Application.dataPath, "MessagesOutput", "csv");
            if (!string.IsNullOrEmpty(selected))
            {
                outputCsvPath = selected;
                SavePrefs();
            }
        }
        EditorGUILayout.EndHorizontal();

        GUILayout.Space(20);
        if (GUILayout.Button("Export Messages", GUILayout.Height(40)))
        {
            ExportMessages();
        }
        
        GUILayout.Space(10);
        if (!string.IsNullOrEmpty(exportTime))
        {
            GUILayout.Label($"Export Time: {exportTime}");
            GUILayout.Label($"Message Count: {messageCount}");
            GUILayout.Label($"Word Count: {wordCount}");
        }
    } 
    private void ExportMessages()
    {
        if (directoriesToScan.Count == 0)
        {
            Debug.LogWarning("No directories selected to scan.");
            return;
        }

        List<string> outputLines = new List<string>();
        outputLines.Add("Folder,Script Name,Character,Message,Audio");

        Regex messageRegex = new Regex(
            @"Message(?:Hold)?\s*\(\s*([^,]+?)\s*,\s*(?:[^""]*?,\s*)?[^""]*?""(.*?)""\s*,\s*(\w+|Null)?",
            RegexOptions.Compiled);

        messageCount = 0;
        wordCount = 0;

        foreach (var dir in directoriesToScan)
        {
            if (!Directory.Exists(dir))
            {
                Debug.LogWarning($"Directory not found: {dir}");
                continue;
            }

            string[] moaFiles = Directory.GetFiles(dir, "*.MOA", SearchOption.AllDirectories);
            foreach (var file in moaFiles)
            {
                string content = File.ReadAllText(file);
                MatchCollection matches = messageRegex.Matches(content);

                foreach (Match match in matches)
                {
                    string fullMatch = match.Value;

                    // Skip if the line contains Special:QuestDialog before the message string
                    int quoteIndex = fullMatch.IndexOf('"');
                    if (quoteIndex > -1)
                    {
                        string beforeQuote = fullMatch.Substring(0, quoteIndex);
                        if (beforeQuote.Contains("Special:QuestDialog"))
                        {
                            continue;
                        }
                    }

                    string folderName = Path.GetFileName(Path.GetDirectoryName(file));
                    string fileName = Path.GetFileName(file);
                    string character = match.Groups[1].Value.Trim();
                    string messageText = match.Groups[2].Value.Replace("\"", "\"\"");
                    string audioParam = match.Groups[3].Success ? match.Groups[3].Value : "";

                    messageCount++;
                    wordCount += messageText.Split(new[] { ' ', '\n', '\r', '\t' }, System.StringSplitOptions.RemoveEmptyEntries).Length;

                    outputLines.Add($"{folderName},{fileName},{character},\"{messageText}\",{audioParam}");
                }
            }
        }

        try
        {
            File.WriteAllLines(outputCsvPath, outputLines);
            AssetDatabase.Refresh();

            exportTime = System.DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            Debug.Log($"Message export complete. Saved to: {outputCsvPath}");
        }
        catch (IOException ex)
        {
            Debug.LogError($"Failed to write CSV file: {ex.Message}");
        }
    }


    [System.Serializable]
    private class FolderListWrapper
    {
        public List<string> folders = new List<string>();
    }

    private void SavePrefs()
    {
        try
        {
            FolderListWrapper wrapper = new FolderListWrapper { folders = directoriesToScan };
            string dirJson = JsonUtility.ToJson(wrapper);
            EditorPrefs.SetString(DirsKey, dirJson);
            EditorPrefs.SetString(OutputKey, outputCsvPath);
        }
        catch (System.Exception ex)
        {
            Debug.LogError($"[MessageExporter] Failed to save preferences: {ex.Message}");
        }
    }

    private void LoadPrefs()
    {
        try
        {
            if (EditorPrefs.HasKey(DirsKey))
            {
                string dirJson = EditorPrefs.GetString(DirsKey);
                FolderListWrapper wrapper = JsonUtility.FromJson<FolderListWrapper>(dirJson);
                if (wrapper != null && wrapper.folders != null)
                {
                    directoriesToScan = wrapper.folders;
                }
            }

            if (EditorPrefs.HasKey(OutputKey))
            {
                outputCsvPath = EditorPrefs.GetString(OutputKey);
            }
        }
        catch (System.Exception ex)
        {
            Debug.LogError($"[MessageExporter] Failed to load preferences: {ex.Message}");
        }
    }
}
