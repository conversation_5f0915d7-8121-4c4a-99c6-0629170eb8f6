using System.Collections.Generic;
using System.Linq;
using Unity.Mathematics;
using UnityEditor;
using UnityEngine;

public class TextureUsageWindow : EditorWindow
{
    private ORTextureUsageTab.TextureUsageInfo _textureUsageData;
    private Texture2D _originalTexture;
    private Texture2D _usageHeatmapTexture;
    
    private Vector2 _leftPanelScroll;
    private Vector2 _rightPanelScroll;
    
    // Visualization settings
    private bool _showOverlayMode = true;
    private bool _showAtlasLabels = true;
    // Duplication factor range for heatmap
    private float _maxDuplicationFactor = 1f;
    
    // Mouse interaction
    private Vector2 _selectedPixelCoord = Vector2.zero;
    private bool _hasSelectedPixel = false;
    private List<ORTextureUsageTab.AtlasUsageInfo> _atlasesAtSelectedPixel = new();

    [MenuItem("Art Tools/Texture Usage Analyzer")]
    public static void ShowWindow()
    {
        GetWindow<TextureUsageWindow>("Texture Usage Analyzer");
    }

    public void SetTextureUsageData(ORTextureUsageTab.TextureUsageInfo textureUsageData)
    {
        _textureUsageData = textureUsageData;
        _originalTexture = textureUsageData?.GetTexture();

        if (_usageHeatmapTexture != null)
        {
            DestroyImmediate(_usageHeatmapTexture);
            _usageHeatmapTexture = null;
        }

        _hasSelectedPixel = false;

        // Calculate max duplication factor for heatmap scaling
        CalculateMaxDuplicationFactor();

        Repaint();
    }

    private void OnGUI()
    {
        if (_textureUsageData == null)
        {
            EditorGUILayout.HelpBox("No texture usage data loaded. Please select a texture from the Texture Usage tab.", MessageType.Info);
            return;
        }

        DrawHeader();
        DrawControls();
        
        EditorGUILayout.BeginHorizontal(GUILayout.ExpandHeight(true));

        var keyWidth = 80f;
        var panelWidth = (position.width - keyWidth) * 0.5f - 5f;

        DrawColorKey(keyWidth);

        DrawLeftPanel(panelWidth);
        DrawRightPanel(panelWidth);

        EditorGUILayout.EndHorizontal();
    }

    private void DrawHeader()
    {
        EditorGUILayout.BeginVertical(EditorStyles.helpBox);
        EditorGUILayout.LabelField("Texture Usage Analysis", EditorStyles.boldLabel);
        EditorGUILayout.LabelField($"Texture: {_textureUsageData.textureName}", EditorStyles.label);
        EditorGUILayout.LabelField($"Path: {_textureUsageData.texturePath}", EditorStyles.miniLabel);
        
        EditorGUILayout.BeginHorizontal();
        EditorGUILayout.LabelField($"Net Pixel Area: {_textureUsageData.netPixelArea:F0} pixels", EditorStyles.label);
        EditorGUILayout.LabelField($"Duplication Factor: {_textureUsageData.duplicationFactor:F2}x", EditorStyles.label);
        EditorGUILayout.LabelField($"Used in {_textureUsageData.atlasCount} atlases", EditorStyles.label);
        EditorGUILayout.EndHorizontal();
        
        EditorGUILayout.EndVertical();
        EditorGUILayout.Space();
    }

    private void DrawControls()
    {
        EditorGUILayout.BeginHorizontal(EditorStyles.toolbar);
        
        var newOverlayMode = GUILayout.Toggle(_showOverlayMode, "Show All Overlays", EditorStyles.toolbarButton);
        if (newOverlayMode != _showOverlayMode)
        {
            _showOverlayMode = newOverlayMode;
            if (_usageHeatmapTexture != null)
            {
                DestroyImmediate(_usageHeatmapTexture);
                _usageHeatmapTexture = null;
            }
        }
        
        _showAtlasLabels = GUILayout.Toggle(_showAtlasLabels, "Show Atlas Labels", EditorStyles.toolbarButton);
        
        GUILayout.FlexibleSpace();
        
        if (GUILayout.Button("Regenerate Heatmap", EditorStyles.toolbarButton))
        {
            if (_usageHeatmapTexture != null)
            {
                DestroyImmediate(_usageHeatmapTexture);
                _usageHeatmapTexture = null;
            }
        }
        
        EditorGUILayout.EndHorizontal();
    }

    private void DrawLeftPanel(float width)
    {
        EditorGUILayout.BeginVertical(EditorStyles.helpBox, GUILayout.Width(width));
        EditorGUILayout.LabelField("Original Texture with Usage Overlay", EditorStyles.centeredGreyMiniLabel);
        
        _leftPanelScroll = EditorGUILayout.BeginScrollView(_leftPanelScroll);

        if (_originalTexture != null)
        {
            float aspect = (float)_originalTexture.width / _originalTexture.height;
            float displayHeight = width / aspect;
            
            Rect displayRect = GUILayoutUtility.GetRect(width, displayHeight);
            
            if (_showOverlayMode)
            {
                DrawUsageOverlay(displayRect);
            }
            else
            {
                GUI.DrawTexture(displayRect, _originalTexture, ScaleMode.ScaleToFit);
            }

            // Handle mouse interaction
            HandleMouseInteraction(displayRect);
            
            // Draw selected pixel indicator
            if (_hasSelectedPixel)
            {
                DrawPixelIndicator(displayRect, _selectedPixelCoord, Color.yellow);
            }
        }
        else
        {
            EditorGUILayout.HelpBox("Could not load original texture.", MessageType.Warning);
        }

        EditorGUILayout.EndScrollView();
        EditorGUILayout.EndVertical();
    }

    private void DrawRightPanel(float width)
    {
        EditorGUILayout.BeginVertical(EditorStyles.helpBox, GUILayout.Width(width));
        EditorGUILayout.LabelField("Atlas Usage Details", EditorStyles.centeredGreyMiniLabel);
        
        _rightPanelScroll = EditorGUILayout.BeginScrollView(_rightPanelScroll);

        // Draw atlas list
        DrawAtlasList();
        
        // Draw selected pixel information
        if (_hasSelectedPixel)
        {
            DrawSelectedPixelInfo();
        }

        EditorGUILayout.EndScrollView();
        EditorGUILayout.EndVertical();
    }

    private void DrawAtlasList()
    {
        EditorGUILayout.LabelField("Atlases Using This Texture:", EditorStyles.boldLabel);

        for (int i = 0; i < _textureUsageData.atlasUsages.Count; i++)
        {
            var atlasUsage = _textureUsageData.atlasUsages[i];

            EditorGUILayout.BeginHorizontal(EditorStyles.helpBox);

            GUILayout.Space(25);

            EditorGUILayout.BeginVertical();
            // Show full atlas name including shader variant
            var fullAtlasName = TextureUsageUtils.GetFullAtlasName(atlasUsage.visData);
            EditorGUILayout.LabelField(fullAtlasName, EditorStyles.boldLabel);
            EditorGUILayout.LabelField($"Usage: {atlasUsage.usagePercentage:F1}% ({atlasUsage.pixelArea:F0} pixels)");
            EditorGUILayout.LabelField($"Regions: {atlasUsage.mappings.Count}");
            EditorGUILayout.EndVertical();

            EditorGUILayout.BeginVertical(GUILayout.Width(100));

            if (GUILayout.Button("Open Atlas", EditorStyles.miniButton))
            {
                var window = GetWindow<AtlasVisualiserWindow>("Atlas Visualiser");
                window.SetVisData(atlasUsage.visData);
                window.Show();
            }

            EditorGUILayout.EndVertical();

            EditorGUILayout.EndHorizontal();
        }
    }

    private void DrawSelectedPixelInfo()
    {
        EditorGUILayout.Space();
        EditorGUILayout.LabelField("Selected Pixel Information:", EditorStyles.boldLabel);

        EditorGUILayout.LabelField($"Coordinate: ({_selectedPixelCoord.x:F0}, {_selectedPixelCoord.y:F0})");

        if (_atlasesAtSelectedPixel.Count > 0)
        {
            // Calculate actual duplication factor accounting for scaling
            float totalDuplicationFactor = CalculatePixelDuplicationFactor(_selectedPixelCoord);

            EditorGUILayout.LabelField($"Duplication Factor: {totalDuplicationFactor:F2}x", EditorStyles.boldLabel);
            EditorGUILayout.LabelField($"Used in {_atlasesAtSelectedPixel.Count} atlas(es):");

            foreach (var atlasUsage in _atlasesAtSelectedPixel)
            {
                EditorGUILayout.BeginHorizontal(EditorStyles.helpBox);
                var fullName = TextureUsageUtils.GetFullAtlasName(atlasUsage.visData);
                var path = TextureUsageUtils.GetAtlasPath(atlasUsage.visData);

                var duplicationFactor = AtlasDuplicationFactorForPixel(atlasUsage, _selectedPixelCoord);
                
                EditorGUILayout.BeginVertical();
                EditorGUILayout.LabelField($"• {fullName} (Duplication Factor: {duplicationFactor:F2}x)");
                EditorGUILayout.LabelField($"Path: {path}", EditorStyles.miniLabel);
                EditorGUILayout.EndVertical();

                if (GUILayout.Button("Open", EditorStyles.miniButton, GUILayout.Width(50)))
                {
                    var window = GetWindow<AtlasVisualiserWindow>("Atlas Visualiser");
                    window.SetVisData(atlasUsage.visData);
                    window.Show();
                }
                EditorGUILayout.EndHorizontal();
            }
        }
        else
        {
            EditorGUILayout.LabelField("This pixel is not used in any atlas.");
        }
    }

    private float CalculatePixelDuplicationFactor(Vector2 pixelCoord)
    {
        float totalFactor = 0f;
        int pixelX = Mathf.FloorToInt(pixelCoord.x);
        int pixelY = Mathf.FloorToInt(pixelCoord.y);

        // Use the same logic as heatmap generation to ensure consistency
        foreach (var atlasUsage in _textureUsageData.atlasUsages)
        {
            foreach (var mapping in atlasUsage.mappings)
            {
                float scalingFactor = TextureUsageUtils.CalculateAccurateScalingFactor(mapping);
                var trueOriginalRect = TextureUsageUtils.ConvertToTrueOriginalCoordinates(mapping, _originalTexture);

                int startX = Mathf.Max(0, Mathf.FloorToInt(trueOriginalRect.x));
                int endX = Mathf.Min(_originalTexture.width - 1, Mathf.CeilToInt(trueOriginalRect.x + trueOriginalRect.width));
                int startY = Mathf.Max(0, Mathf.FloorToInt(trueOriginalRect.y));
                int endY = Mathf.Min(_originalTexture.height - 1, Mathf.CeilToInt(trueOriginalRect.y + trueOriginalRect.height));

                // Check if the clicked pixel is within this mapping's range
                if (pixelX >= startX && pixelX <= endX && pixelY >= startY && pixelY <= endY)
                {
                    totalFactor += scalingFactor;
                }
            }
        }

        return totalFactor;
    }

    private float AtlasDuplicationFactorForPixel(ORTextureUsageTab.AtlasUsageInfo atlasUsage, Vector2 pixelCoord)
    {
        float totalScalingFactor = 0f;
        int pixelX = Mathf.FloorToInt(pixelCoord.x);
        int pixelY = Mathf.FloorToInt(pixelCoord.y);

        foreach (var mapping in atlasUsage.mappings)
        {
            float scalingFactor = TextureUsageUtils.CalculateAccurateScalingFactor(mapping);
            var trueOriginalRect = TextureUsageUtils.ConvertToTrueOriginalCoordinates(mapping, _originalTexture);

            int startX = Mathf.Max(0, Mathf.FloorToInt(trueOriginalRect.x));
            int endX = Mathf.Min(_originalTexture.width - 1, Mathf.CeilToInt(trueOriginalRect.x + trueOriginalRect.width));
            int startY = Mathf.Max(0, Mathf.FloorToInt(trueOriginalRect.y));
            int endY = Mathf.Min(_originalTexture.height - 1, Mathf.CeilToInt(trueOriginalRect.y + trueOriginalRect.height));

            // Check if the clicked pixel is within this mapping's range
            if (pixelX >= startX && pixelX <= endX && pixelY >= startY && pixelY <= endY)
            {
                totalScalingFactor += scalingFactor;
            }
        }
        return totalScalingFactor;
    }

    private void DrawUsageOverlay(Rect displayRect)
    {
        if (_usageHeatmapTexture == null)
            GenerateUsageHeatmap();

        if (_usageHeatmapTexture != null)
            GUI.DrawTexture(displayRect, _usageHeatmapTexture, ScaleMode.ScaleToFit);
    }

    private void GenerateUsageHeatmap()
    {
        if (_originalTexture == null) return;

        int width = _originalTexture.width;
        int height = _originalTexture.height;

        _usageHeatmapTexture = new Texture2D(width, height, TextureFormat.RGBA32, false)
        {
            filterMode = FilterMode.Point
        };
        var pixels = new Color[width * height];
        var duplicationFactors = new float[width * height];

        for (int i = 0; i < pixels.Length; i++)
        {
            pixels[i] = Color.clear;
            duplicationFactors[i] = 0f;
        }

        foreach (var atlasUsage in _textureUsageData.atlasUsages)
        {
            foreach (var mapping in atlasUsage.mappings)
            {
                float scalingFactor = TextureUsageUtils.CalculateAccurateScalingFactor(mapping);
                var trueOriginalRect = TextureUsageUtils.ConvertToTrueOriginalCoordinates(mapping, _originalTexture);

                int startX = Mathf.Max(0, Mathf.FloorToInt(trueOriginalRect.x));
                int endX = Mathf.Min(width - 1, Mathf.CeilToInt(trueOriginalRect.x + trueOriginalRect.width));
                int startY = Mathf.Max(0, Mathf.FloorToInt(trueOriginalRect.y));
                int endY = Mathf.Min(height - 1, Mathf.CeilToInt(trueOriginalRect.y + trueOriginalRect.height));

                for (int y = startY; y <= endY; y++)
                {
                    for (int x = startX; x <= endX; x++)
                    {
                        int index = y * width + x;
                        duplicationFactors[index] += scalingFactor;
                    }
                }
            }
        }

        for (int i = 0; i < pixels.Length; i++)
        {
            if (duplicationFactors[i] > 0f)
            {
                float normalizedDuplication = duplicationFactors[i] / _maxDuplicationFactor;
                pixels[i] = GetHeatmapColor(normalizedDuplication);
                pixels[i].a = 0.7f; // Semi-transparent overlay
            }
        }

        _usageHeatmapTexture.SetPixels(pixels);
        _usageHeatmapTexture.Apply();
    }

    private Color GetHeatmapColor(float normalizedValue)
    {
        // Blue to Red heatmap (blue = low duplication, red = high duplication)
        normalizedValue = Mathf.Clamp01(normalizedValue);

        return normalizedValue switch
        {
            < 0.25f => Color.Lerp(Color.blue, Color.cyan, normalizedValue * 4f),
            < 0.5f => Color.Lerp(Color.cyan, Color.green, (normalizedValue - 0.25f) * 4f),
            < 0.75f => Color.Lerp(Color.green, Color.yellow, (normalizedValue - 0.5f) * 4f),
            _ => Color.Lerp(Color.yellow, Color.red, (normalizedValue - 0.75f) * 4f)
        };
    }

    private void CalculateMaxDuplicationFactor()
    {
        if (_textureUsageData == null || _originalTexture == null) return;

        int width = _originalTexture.width;
        int height = _originalTexture.height;
        var duplicationFactors = new float[width * height];

        // Calculate duplication factor for each pixel, accounting for scaling
        foreach (var atlasUsage in _textureUsageData.atlasUsages)
        {
            foreach (var mapping in atlasUsage.mappings)
            {
                // Calculate true scaling factor using accurate scaling information
                float scalingFactor = TextureUsageUtils.CalculateAccurateScalingFactor(mapping);

                // Convert upscaled coordinates to true original texture coordinates
                var trueOriginalRect = TextureUsageUtils.ConvertToTrueOriginalCoordinates(mapping, _originalTexture);

                int startX = Mathf.Max(0, Mathf.FloorToInt(trueOriginalRect.x));
                int endX = Mathf.Min(width - 1, Mathf.CeilToInt(trueOriginalRect.x + trueOriginalRect.width));
                int startY = Mathf.Max(0, Mathf.FloorToInt(trueOriginalRect.y));
                int endY = Mathf.Min(height - 1, Mathf.CeilToInt(trueOriginalRect.y + trueOriginalRect.height));

                for (int y = startY; y <= endY; y++)
                {
                    for (int x = startX; x <= endX; x++)
                    {
                        int index = y * width + x;
                        duplicationFactors[index] += scalingFactor;
                    }
                }
            }
        }

        _maxDuplicationFactor = 1f;
        foreach (var t in duplicationFactors)
        {
            if (t > _maxDuplicationFactor)
                _maxDuplicationFactor = t;
        }
    }

    private void HandleMouseInteraction(Rect displayRect)
    {
        var currentEvent = Event.current;

        if (currentEvent.type == EventType.MouseDown && currentEvent.button == 0 && displayRect.Contains(currentEvent.mousePosition))
        {
            var relativePos = currentEvent.mousePosition - displayRect.position;
            var normalizedPos = new float2(relativePos.x / displayRect.width, relativePos.y / displayRect.height);

            // Convert to texture coordinates (flip Y)
            _selectedPixelCoord = math.ceil(new float2(normalizedPos.x * _originalTexture.width,
                (1f - normalizedPos.y) * _originalTexture.height)) - 0.5f;

            _hasSelectedPixel = true;

            // Find which atlases use this pixel
            FindAtlasesAtPixel(_selectedPixelCoord);

            currentEvent.Use();
            Repaint();
        }
    }

    private void FindAtlasesAtPixel(float2 pixelCoord)
    {
        _atlasesAtSelectedPixel.Clear();

        foreach (var atlasUsage in _textureUsageData.atlasUsages)
        {
            foreach (var mapping in atlasUsage.mappings)
            {
                var trueOriginalRect = TextureUsageUtils.ConvertToTrueOriginalCoordinates(mapping, _originalTexture);
                if (trueOriginalRect.Contains(pixelCoord))
                {
                    _atlasesAtSelectedPixel.Add(atlasUsage);
                    break;
                }
            }
        }
    }

    private void DrawPixelIndicator(Rect displayRect, Vector2 pixelCoord, Color color)
    {
        var normalizedPos = new Vector2(
            pixelCoord.x / _originalTexture.width,
            1f - (pixelCoord.y / _originalTexture.height) // Flip Y
        );

        var screenPos = new Vector2(
            displayRect.x + normalizedPos.x * displayRect.width,
            displayRect.y + normalizedPos.y * displayRect.height
        );

        var oldColor = GUI.color;
        GUI.color = color;

        // Draw crosshair
        var crosshairSize = 10f;
        GUI.DrawTexture(new Rect(screenPos.x - crosshairSize/2, screenPos.y - 1, crosshairSize, 2), Texture2D.whiteTexture);
        GUI.DrawTexture(new Rect(screenPos.x - 1, screenPos.y - crosshairSize/2, 2, crosshairSize), Texture2D.whiteTexture);

        GUI.color = oldColor;
    }

    private void DrawColorKey(float width)
    {
        EditorGUILayout.BeginVertical(EditorStyles.helpBox, GUILayout.Width(width), GUILayout.MaxWidth(width));

        var keyHeight = 200f;
        var keyRect = GUILayoutUtility.GetRect(width - 10, keyHeight);
        
        if (_showOverlayMode)
        {
            GUILayout.Label("Duplication", EditorStyles.centeredGreyMiniLabel, GUILayout.Width(width - 10));
            GUILayout.Label("Heatmap", EditorStyles.centeredGreyMiniLabel, GUILayout.Width(width - 10));
            
            var gradientRect = new Rect(keyRect.x + 5, keyRect.y + 20, 20, keyHeight - 40);
            DrawGradientBar(gradientRect);

            var labelStyle = new GUIStyle(EditorStyles.miniLabel) { alignment = TextAnchor.MiddleLeft };
            var labelWidth = width - gradientRect.width - 10;

            GUI.Label(new Rect(gradientRect.xMax + 2, gradientRect.y - 10, labelWidth, 20),
                $"{_maxDuplicationFactor:F1}x", labelStyle);
            GUI.Label(new Rect(gradientRect.xMax + 2, gradientRect.yMax - 10, labelWidth, 20),
                "0x", labelStyle);
        }

        EditorGUILayout.EndVertical();
    }

    private void DrawGradientBar(Rect rect)
    {
        var steps = 50;
        var stepHeight = rect.height / steps;

        for (int i = 0; i < steps; i++)
        {
            var normalizedValue = 1f - (float)i / (steps - 1); // Top to bottom: high to low
            var color = GetHeatmapColor(normalizedValue);
            color.a = 1f; // Full opacity for legend

            var stepRect = new Rect(rect.x, rect.y + i * stepHeight, rect.width, stepHeight + 1);

            var oldColor = GUI.color;
            GUI.color = color;
            GUI.DrawTexture(stepRect, Texture2D.whiteTexture);
            GUI.color = oldColor;
        }

        // Draw border
        var borderColor = Color.black;
        var oldColor2 = GUI.color;
        GUI.color = borderColor;

        // Top, Bottom, Left, Right borders
        GUI.DrawTexture(new Rect(rect.x - 1, rect.y - 1, rect.width + 2, 1), Texture2D.whiteTexture);
        GUI.DrawTexture(new Rect(rect.x - 1, rect.yMax, rect.width + 2, 1), Texture2D.whiteTexture);
        GUI.DrawTexture(new Rect(rect.x - 1, rect.y - 1, 1, rect.height + 2), Texture2D.whiteTexture);
        GUI.DrawTexture(new Rect(rect.xMax, rect.y - 1, 1, rect.height + 2), Texture2D.whiteTexture);

        GUI.color = oldColor2;
    }
}
