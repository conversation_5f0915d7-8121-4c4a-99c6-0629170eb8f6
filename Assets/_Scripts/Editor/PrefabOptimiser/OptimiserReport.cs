using System;
using System.Collections.Generic;
using System.Diagnostics;
using UnityEditor;
using UnityEngine;
using UnityEngine.Serialization;
using static PrefabOptimiseController;
using Debug = UnityEngine.Debug;
using Object = UnityEngine.Object;

[CreateAssetMenu(fileName = "OptimiserReport", menuName = "Prefab Optimiser/Report")]
public class OptimiserReport : ScriptableObject
{
    private static OptimiserReport s_current;
    
    [Serializable]
    public struct FolderInfo
    {
        public string folderFrom;
        public string folderTo;
    }
    
    [Serializable]
    public struct SectionInfo
    {
        [Serializable]
        public struct ErrorInfo
        {
            public int severity; // 0 = Exception, 1 = Error, 2 = Warning, 3 = Info
            public string message;
            public Object context;
            public void Deconstruct(out int _severity, out string _message, out Object _context)
            {
                _severity = severity;
                _message = message;
                _context = context;
            }
        }
        
        public string section;
        public string description;
        public float time;
        public List<ErrorInfo> errors;
    }

    public List<FolderInfo> folderInfos = new();
    public List<SectionInfo> sectionInfos = new();
    
    private string currentSection = "";
    private Dictionary<string, (SectionInfo info, Stopwatch sw)> allSections = new();

    public static OptimiserReport Create(List<Configuration> _configs, string _filePath)
    {
        s_current = CreateInstance<OptimiserReport>();
        AssetDatabase.CreateAsset(s_current, _filePath);
        AssetDatabase.SaveAssets();
        s_current.Setup(_configs);
        
        return s_current;
    }

    private void Setup(List<Configuration> _configs)
    {
        foreach (var folder in _configs)
        {
            var folderInfo = new FolderInfo
            {
                folderFrom = folder.directoryFrom,
                folderTo = folder.directoryTo
            };
            folderInfos.Add(folderInfo);
        }
    }
    
    public static void StartSection(string section, string description) => s_current.BeginSection(section, description);
    private void BeginSection(string section, string description)
    {
        currentSection += $"/{section}";

        if (!allSections.TryGetValue(currentSection, out var tup))
        {
            var timeInfo = new SectionInfo
            {
                section = currentSection[1..],
                description = description,
                time = 0f,
                errors = new()
            };
            
            allSections[currentSection] = (timeInfo, Stopwatch.StartNew());
        }
        else
        {
            tup.sw.Restart();
        }
    }
    
    public static void EndSection(string section) => s_current.FinishSection(section);
    private void FinishSection(string section) //Don't need parameter but good sanity check
    {
        var (info, sw) = allSections[currentSection];
        sw.Stop();
        info.time += sw.ElapsedMilliseconds / 1000f;
        allSections[currentSection] = (info, sw);
        
        currentSection = currentSection[..currentSection.LastIndexOf('/')];
    }

    public static void FinishReport() => s_current.FinishUp();
    private void FinishUp()
    {
        foreach (var (_, (section, _)) in allSections)
            sectionInfos.Add(section);
        
        EditorUtility.SetDirty(this);
        AssetDatabase.SaveAssets();
        EditorGUIUtility.PingObject(this);
        
        s_current = null;
    }
    
    public static void LogException(Exception e, Object context = null)
    {
        s_current.Log(0, $"{e.Message}\n{e.StackTrace}", context);
        Debug.LogException(e, context);
    }
    
    public static void LogError(string message, Object context = null)
    {
        if (s_current != null)
            s_current.Log(1, message, context);
        Debug.LogError(message, context);
    }
    
    public static void LogWarning(string message, Object context = null)
    {
        if (s_current != null)
            s_current.Log(2, message, context);
        Debug.LogWarning(message, context);
    }
    
    public static void LogInfo(string message, Object context = null)
    {
        if (s_current != null)
            s_current.Log(3, message, context);
        Debug.Log(message, context);
    }
    
    private void Log(int level, string message, Object context)
    {
        var (info, sw) = allSections[currentSection];
        info.errors.Add(new SectionInfo.ErrorInfo { severity = level, message = message, context = context });
        allSections[currentSection] = (info, sw);
    }
}