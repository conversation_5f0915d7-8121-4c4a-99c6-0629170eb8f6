using System.IO;
using UnityEditor;
using UnityEngine;

public static class TextureUsageUtils
{
    public static string GetAtlasName(AtlasVisualisationData atlasData)
    {
        var assetPath = AssetDatabase.GetAssetPath(atlasData);
        var fileName = Path.GetFileNameWithoutExtension(assetPath);
        var parts = fileName.Replace("_VisData", "").Split('_');

        return parts.Length >= 1 ? parts[0] : "Unknown";
    }

    public static string GetFullAtlasName(AtlasVisualisationData atlasData)
    {
        var assetPath = AssetDatabase.GetAssetPath(atlasData);
        var fileName = Path.GetFileNameWithoutExtension(assetPath);

        if (fileName.EndsWith("_VisData"))
            fileName = fileName[..^"_VisData".Length];

        return fileName;
    }
    
    public static string GetAtlasPath(AtlasVisualisationData atlasData)
    {
        var assetPath = AssetDatabase.GetAssetPath(atlasData);
        var fileName = Path.GetFileName(assetPath);

        return assetPath[..^(fileName.Length + "/Atlases/Editor/".Length)];
    }

    public static float CalculateTrueOriginalArea(AtlasRectMapping mapping)
    {
        if (mapping.trueOriginalTextureSize.x > 0 && mapping.upscaledTextureSize.x > 0)
        {
            float scaleX = mapping.trueOriginalTextureSize.x / mapping.upscaledTextureSize.x;
            float scaleY = mapping.trueOriginalTextureSize.y / mapping.upscaledTextureSize.y;

            float trueWidth = mapping.originalPixelRect.width * scaleX;
            float trueHeight = mapping.originalPixelRect.height * scaleY;

            return trueWidth * trueHeight;
        }
        return mapping.originalPixelRect.width * mapping.originalPixelRect.height;
    }

    public static float CalculateAccurateScalingFactor(AtlasRectMapping mapping)
    {
        if (mapping.trueOriginalTextureSize.x > 0 && mapping.upscaledTextureSize.x > 0)
        {
            float atlasArea = mapping.atlasPixelRect.width * mapping.atlasPixelRect.height;

            float scaleX = mapping.trueOriginalTextureSize.x / mapping.upscaledTextureSize.x;
            float scaleY = mapping.trueOriginalTextureSize.y / mapping.upscaledTextureSize.y;
            float trueOriginalMappingArea = mapping.originalPixelRect.width * scaleX * mapping.originalPixelRect.height * scaleY;

            return trueOriginalMappingArea > 0 ? atlasArea / trueOriginalMappingArea : 1f;
        }
        else
        {
            float originalArea = mapping.originalPixelRect.width * mapping.originalPixelRect.height;
            float atlasArea = mapping.atlasPixelRect.width * mapping.atlasPixelRect.height;
            return originalArea > 0 ? atlasArea / originalArea : 1f;
        }
    }

    public static Rect ConvertToTrueOriginalCoordinates(AtlasRectMapping mapping, Texture2D originalTexture)
    {
        if (mapping.trueOriginalTextureSize.x > 0 && mapping.upscaledTextureSize.x > 0)
        {
            float scaleX = mapping.trueOriginalTextureSize.x / mapping.upscaledTextureSize.x;
            float scaleY = mapping.trueOriginalTextureSize.y / mapping.upscaledTextureSize.y;

            return new Rect(
                mapping.originalPixelRect.x * scaleX,
                mapping.originalPixelRect.y * scaleY,
                mapping.originalPixelRect.width * scaleX,
                mapping.originalPixelRect.height * scaleY
            );
        }
        return mapping.originalPixelRect;
    }

    public static float CalculateNetPixelArea(float duplicationFactor, float totalPixelArea)
    {
        return (duplicationFactor - 1f) * totalPixelArea;
    }
}
