using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using UnityEditor;
using UnityEngine;

public class ORAtlasSimilarityTab : OptimiserReportTab
{
    private enum SortMode
    {
        SimilarityPercentage,
        AtlasName1,
        AtlasName2,
        SharedTextureCount,
        TotalFootprintOverlap
    }

    private class AtlasFootprint
    {
        public string atlasName;
        public string shaderName;
        public AtlasVisualisationData visData;
        public string assetPath;
        public Dictionary<string, float> textureFootprints = new(); // texture path -> area used
        public float totalFootprintArea;
        public HashSet<string> texturePathsSet = new(); // Fast lookup for shared texture checking
    }

    private class AtlasSimilarityPair
    {
        public AtlasFootprint atlas1;
        public AtlasFootprint atlas2;
        public float similarityPercentage;
        public int sharedTextureCount;
        public float totalFootprintOverlap;
        public List<string> sharedTextures = new();
    }

    private class FolderSimilarityPair
    {
        public string folder1Path;
        public string folder2Path;
        public string folder1Name;
        public string folder2Name;
        public float weightedSimilarityPercentage;
        public int totalShaderVariants;
        public int matchingShaderVariants;
        public List<ShaderVariantComparison> shaderComparisons = new();
        public float totalAtlasSize; // Combined size of all atlases in both folders
    }

    private class ShaderVariantComparison
    {
        public string shaderVariant;
        public AtlasFootprint atlas1; // null if doesn't exist in folder1
        public AtlasFootprint atlas2; // null if doesn't exist in folder2
        public float similarityPercentage;
        public float weightContribution; // How much this comparison contributes to overall similarity
    }

    private List<AtlasFootprint> _atlasFootprints;
    private List<AtlasSimilarityPair> _similarityPairs;
    private List<FolderSimilarityPair> _folderSimilarityPairs;
    private OptimiserReport _cachedReport;
    private Vector2 _scrollPosition;
    private SortMode _sortMode = SortMode.SimilarityPercentage;
    private bool _sortAscending = false;
    private bool _perFolderMode = false;

    // Performance caching
    private List<AtlasSimilarityPair> _sortedPairsCache;
    private List<FolderSimilarityPair> _sortedFolderPairsCache;
    private SortMode _lastSortMode;
    private bool _lastSortAscending;
    private bool _lastPerFolderMode;

    // Performance settings
    private const float PAIR_ROW_HEIGHT = 60f;
    private const float FOLDER_PAIR_ROW_HEIGHT = 80f;
    private const int MAX_SIMILARITY_PAIRS = 200; // Only keep top N pairs for performance
    private const float MIN_SIMILARITY_THRESHOLD = 1f; // Skip pairs below this similarity %

    private void OnEnable()
    {
        m_title = "Atlas Similarity";
    }

    public override void Draw(OptimiserReport report, OptimiserReportEditor editor)
    {
        if (report != _cachedReport || _lastPerFolderMode != _perFolderMode)
        {
            GatherAtlasData(report);
            if (_perFolderMode)
                CalculateFolderSimilarities(report);
            else
                CalculateSimilarities();
            _cachedReport = report;
            _lastPerFolderMode = _perFolderMode;
            InvalidateCaches();
        }

        // Check if we have data to display
        bool hasData = _perFolderMode ?
            (_folderSimilarityPairs != null && _folderSimilarityPairs.Count > 0) :
            (_similarityPairs != null && _similarityPairs.Count > 0);

        if (!hasData)
        {
            var message = _perFolderMode ?
                "No folder similarity data found. Need report configuration with input/output folder pairs." :
                "No atlas similarity data found. Need at least 2 atlases to compare.";
            EditorGUILayout.HelpBox(message, MessageType.Info);
            return;
        }

        DrawSummary();
        DrawToolbar();
        if (_perFolderMode)
            DrawFolderSimilarityList();
        else
            DrawSimilarityList();
    }

    private void InvalidateCaches()
    {
        _sortedPairsCache = null;
        _sortedFolderPairsCache = null;
    }

    private void DrawSummary()
    {
        var title = _perFolderMode ? "Folder Similarity Analysis" : "Atlas Similarity Analysis";
        EditorGUILayout.LabelField(title, EditorStyles.boldLabel);
        EditorGUILayout.Space(4);

        if (_perFolderMode && _folderSimilarityPairs != null)
        {
            var totalFolderPairs = _folderSimilarityPairs.Count;
            var highSimilarityPairs = _folderSimilarityPairs.Count(p => p.weightedSimilarityPercentage >= 50f);
            var avgSimilarity = _folderSimilarityPairs.Count > 0 ? _folderSimilarityPairs.Average(p => p.weightedSimilarityPercentage) : 0f;
            var totalShaderVariants = _folderSimilarityPairs.Sum(p => p.totalShaderVariants);

            DrawDoubleLabel("Total Folder Pairs:", totalFolderPairs.ToString(), EditorStyles.label);
            DrawDoubleLabel("Total Shader Variants:", totalShaderVariants.ToString("N0"), EditorStyles.label);
            DrawDoubleLabel("High Similarity (≥50%):", highSimilarityPairs.ToString("N0"), EditorStyles.label);
            DrawDoubleLabel("Average Similarity:", $"{avgSimilarity:F1}%", EditorStyles.label);
        }
        else if (!_perFolderMode && _atlasFootprints != null && _similarityPairs != null)
        {
            var totalAtlases = _atlasFootprints.Count;
            var totalPairs = _similarityPairs.Count;
            var highSimilarityPairs = _similarityPairs.Count(p => p.similarityPercentage >= 50f);
            var avgSimilarity = _similarityPairs.Count > 0 ? _similarityPairs.Average(p => p.similarityPercentage) : 0f;

            DrawDoubleLabel("Total Atlases:", totalAtlases.ToString(), EditorStyles.label);
            DrawDoubleLabel("Total Pairs:", totalPairs.ToString("N0"), EditorStyles.label);
            DrawDoubleLabel("High Similarity (≥50%):", highSimilarityPairs.ToString("N0"), EditorStyles.label);
            DrawDoubleLabel("Average Similarity:", $"{avgSimilarity:F1}%", EditorStyles.label);
        }

        EditorGUILayout.Space(8);
    }

    private void DrawToolbar()
    {
        EditorGUILayout.BeginHorizontal(EditorStyles.toolbar);

        // Per-folder mode checkbox
        var newPerFolderMode = GUILayout.Toggle(_perFolderMode, "Per Folder", EditorStyles.toolbarButton, GUILayout.Width(80));
        if (newPerFolderMode != _perFolderMode)
        {
            _perFolderMode = newPerFolderMode;
            // Will trigger recalculation in Draw method
        }

        GUILayout.Space(10);

        // Sort mode dropdown
        var newSortMode = (SortMode)EditorGUILayout.EnumPopup("Sort by:", _sortMode, EditorStyles.toolbarDropDown, GUILayout.Width(200));
        if (newSortMode != _sortMode)
        {
            _sortMode = newSortMode;
            InvalidateCaches();
        }

        // Sort direction toggle
        var newSortAscending = GUILayout.Toggle(_sortAscending, _sortAscending ? "↑" : "↓", EditorStyles.toolbarButton, GUILayout.Width(20));
        if (newSortAscending != _sortAscending)
        {
            _sortAscending = newSortAscending;
            InvalidateCaches();
        }

        GUILayout.FlexibleSpace();

        EditorGUILayout.EndHorizontal();
    }

    private void DrawSimilarityList()
    {
        var sortedPairs = GetSortedPairs();
        var totalHeight = sortedPairs.Count * PAIR_ROW_HEIGHT;
        var viewRect = GUILayoutUtility.GetRect(0, Mathf.Min(totalHeight, 400));
        var scrollRect = new Rect(viewRect.x, viewRect.y, viewRect.width, totalHeight);

        _scrollPosition = GUI.BeginScrollView(viewRect, _scrollPosition, scrollRect);

        // Draw header
        DrawPairHeader();

        // Draw pairs
        for (int i = 0; i < sortedPairs.Count; i++)
        {
            var pair = sortedPairs[i];
            var pairRect = new Rect(0, 25 + i * PAIR_ROW_HEIGHT, scrollRect.width - 20, PAIR_ROW_HEIGHT);
            DrawSimilarityPair(pair, pairRect, i % 2 == 0);
        }

        GUI.EndScrollView();
    }

    private void DrawPairHeader()
    {
        var headerRect = new Rect(0, 0, Screen.width - 40, 25);
        GUI.Box(headerRect, "", EditorStyles.toolbar);

        float x = 5;
        GUI.Label(new Rect(x, 2, 150, 20), "Atlas 1", EditorStyles.toolbarButton);
        x += 155;
        GUI.Label(new Rect(x, 2, 150, 20), "Atlas 2", EditorStyles.toolbarButton);
        x += 155;
        GUI.Label(new Rect(x, 2, 80, 20), "Similarity", EditorStyles.toolbarButton);
        x += 85;
        GUI.Label(new Rect(x, 2, 80, 20), "Shared Tex", EditorStyles.toolbarButton);
        x += 85;
        GUI.Label(new Rect(x, 2, 100, 20), "Overlap Area", EditorStyles.toolbarButton);
        x += 105;
        GUI.Label(new Rect(x, 2, 200, 20), "Actions", EditorStyles.toolbarButton);
    }

    private void DrawSimilarityPair(AtlasSimilarityPair pair, Rect rect, bool isEven)
    {
        // Background
        var bgColor = isEven ? new Color(0.8f, 0.8f, 0.8f, 0.1f) : new Color(0.9f, 0.9f, 0.9f, 0.1f);
        EditorGUI.DrawRect(rect, bgColor);

        float x = rect.x + 5;
        float y = rect.y + 5;

        // Atlas 1 name
        GUI.Label(new Rect(x, y, 150, 20), pair.atlas1.atlasName, EditorStyles.label);
        GUI.Label(new Rect(x, y + 20, 150, 16), pair.atlas1.shaderName, EditorStyles.miniLabel);
        x += 155;

        // Atlas 2 name
        GUI.Label(new Rect(x, y, 150, 20), pair.atlas2.atlasName, EditorStyles.label);
        GUI.Label(new Rect(x, y + 20, 150, 16), pair.atlas2.shaderName, EditorStyles.miniLabel);
        x += 155;

        // Similarity percentage
        var similarityColor = GetSimilarityColor(pair.similarityPercentage);
        var oldColor = GUI.color;
        GUI.color = similarityColor;
        GUI.Label(new Rect(x, y + 10, 80, 20), $"{pair.similarityPercentage:F1}%", EditorStyles.boldLabel);
        GUI.color = oldColor;
        x += 85;

        // Shared texture count
        GUI.Label(new Rect(x, y + 10, 80, 20), pair.sharedTextureCount.ToString(), EditorStyles.label);
        x += 85;

        // Total footprint overlap
        GUI.Label(new Rect(x, y + 10, 100, 20), $"{pair.totalFootprintOverlap:F0} px²", EditorStyles.label);
        x += 105;

        // Action buttons
        if (GUI.Button(new Rect(x, y + 5, 90, 18), "View Atlas 1", EditorStyles.miniButton))
        {
            OpenAtlasVisualizer(pair.atlas1);
        }
        if (GUI.Button(new Rect(x, y + 25, 90, 18), "View Atlas 2", EditorStyles.miniButton))
        {
            OpenAtlasVisualizer(pair.atlas2);
        }
    }

    private Color GetSimilarityColor(float percentage)
    {
        // Green for high similarity, yellow for medium, red for low
        return percentage switch
        {
            >= 75f => Color.green,
            >= 50f => Color.yellow,
            >= 25f => new Color(1f, 0.5f, 0f), // Orange
            _ => Color.red
        };
    }

    private void OpenAtlasVisualizer(AtlasFootprint atlas)
    {
        var window = EditorWindow.GetWindow<AtlasVisualiserWindow>("Atlas Visualiser");
        window.SetVisData(atlas.visData);
        window.Show();
        window.Focus();
    }

    private List<AtlasSimilarityPair> GetSortedPairs()
    {
        // Use cached result if sort parameters haven't changed
        if (_sortedPairsCache != null && _lastSortMode == _sortMode && _lastSortAscending == _sortAscending)
            return _sortedPairsCache;

        var sortedList = _similarityPairs.ToList();

        sortedList.Sort((a, b) =>
        {
            int comparison = _sortMode switch
            {
                SortMode.SimilarityPercentage => a.similarityPercentage.CompareTo(b.similarityPercentage),
                SortMode.AtlasName1 => string.Compare(a.atlas1.atlasName, b.atlas1.atlasName, StringComparison.OrdinalIgnoreCase),
                SortMode.AtlasName2 => string.Compare(a.atlas2.atlasName, b.atlas2.atlasName, StringComparison.OrdinalIgnoreCase),
                SortMode.SharedTextureCount => a.sharedTextureCount.CompareTo(b.sharedTextureCount),
                SortMode.TotalFootprintOverlap => a.totalFootprintOverlap.CompareTo(b.totalFootprintOverlap),
                _ => 0
            };

            return _sortAscending ? comparison : -comparison;
        });

        // Cache the sorted result
        _sortedPairsCache = sortedList;
        _lastSortMode = _sortMode;
        _lastSortAscending = _sortAscending;

        return sortedList;
    }

    private void GatherAtlasData(OptimiserReport report)
    {
        _atlasFootprints = new List<AtlasFootprint>();

        // Get relevant folders from the report
        var relevantFolders = new HashSet<string>();
        foreach (var folderInfo in report.folderInfos)
        {
            relevantFolders.Add(folderInfo.folderTo.Replace("\\", "/"));
        }

        // Find all AtlasVisualisationData assets in the project
        var visDataGuids = AssetDatabase.FindAssets("t:AtlasVisualisationData");

        foreach (var guid in visDataGuids)
        {
            var assetPath = AssetDatabase.GUIDToAssetPath(guid);
            var visData = AssetDatabase.LoadAssetAtPath<AtlasVisualisationData>(assetPath);

            // Check if this atlas is relevant to the current report
            bool isRelevant = relevantFolders.Count == 0 || // If no relevant folders found, show all
                             relevantFolders.Any(folder => assetPath.StartsWith(folder.Replace("\\", "/")));

            if (!isRelevant || visData?.mappings is not { Count: > 0 }) continue;

            var atlasFootprint = CreateAtlasFootprint(visData, assetPath);
            if (atlasFootprint != null)
            {
                _atlasFootprints.Add(atlasFootprint);
            }
        }
    }

    private AtlasFootprint CreateAtlasFootprint(AtlasVisualisationData visData, string assetPath)
    {
        if (visData.mappings == null || visData.mappings.Count == 0)
            return null;

        // Extract atlas name and shader name from the asset path
        var fileName = Path.GetFileNameWithoutExtension(assetPath);
        var parts = fileName.Replace("_VisData", "").Split('_');

        string atlasName = "Unknown";
        string shaderName = "Unknown";

        if (parts.Length >= 2)
        {
            atlasName = parts[0];
            shaderName = string.Join("_", parts.Skip(1));
        }
        else if (parts.Length == 1)
        {
            atlasName = parts[0];
        }

        var footprint = new AtlasFootprint
        {
            atlasName = atlasName,
            shaderName = shaderName,
            visData = visData,
            assetPath = assetPath
        };

        // Calculate texture footprints
        CalculateTextureFootprints(footprint);

        return footprint;
    }

    private void CalculateTextureFootprints(AtlasFootprint footprint)
    {
        footprint.textureFootprints.Clear();
        footprint.texturePathsSet.Clear();
        footprint.totalFootprintArea = 0f;

        // Group mappings by original texture path to handle overlaps
        var textureGroups = footprint.visData.mappings.GroupBy(m => m.originalTexturePath);

        foreach (var group in textureGroups)
        {
            var texturePath = group.Key;
            if (string.IsNullOrEmpty(texturePath)) continue;

            // Calculate the total area used from this texture, avoiding double-counting overlaps
            var totalArea = CalculateNonOverlappingArea(group.ToList());

            if (totalArea > 0)
            {
                footprint.textureFootprints[texturePath] = totalArea;
                footprint.texturePathsSet.Add(texturePath);
                footprint.totalFootprintArea += totalArea;
            }
        }
    }

    private float CalculateNonOverlappingArea(List<AtlasRectMapping> mappings)
    {
        if (mappings.Count == 0) return 0f;
        if (mappings.Count == 1)
        {
            var rect = mappings[0].originalPixelRect;
            return rect.width * rect.height;
        }

        // For multiple mappings from the same texture, we need to calculate the union area
        // to avoid double-counting overlapping regions
        var rects = mappings.Select(m => m.originalPixelRect).ToList();
        return CalculateUnionArea(rects);
    }

    private float CalculateUnionArea(List<Rect> rects)
    {
        if (rects.Count == 0) return 0f;
        if (rects.Count == 1) return rects[0].width * rects[0].height;

        // For performance, use a simplified approach for multiple rects
        // This is an approximation but much faster than exact union calculation

        // Find the bounding box of all rectangles
        var minX = rects.Min(r => r.xMin);
        var maxX = rects.Max(r => r.xMax);
        var minY = rects.Min(r => r.yMin);
        var maxY = rects.Max(r => r.yMax);

        var boundingArea = (maxX - minX) * (maxY - minY);
        var totalArea = rects.Sum(r => r.width * r.height);

        // If total area is less than bounding area, there's no overlap
        if (totalArea <= boundingArea)
            return totalArea;

        // For overlapping cases, use a fast sampling approach with fewer samples
        const int maxSamples = 200; // Reduced for speed
        var width = maxX - minX;
        var height = maxY - minY;
        var samplesX = Mathf.Min(maxSamples, Mathf.Max(10, Mathf.CeilToInt(width / 50f)));
        var samplesY = Mathf.Min(maxSamples, Mathf.Max(10, Mathf.CeilToInt(height / 50f)));

        if (samplesX <= 0 || samplesY <= 0) return totalArea;

        var stepX = width / samplesX;
        var stepY = height / samplesY;
        var coveredSamples = 0;

        for (int x = 0; x < samplesX; x++)
        {
            for (int y = 0; y < samplesY; y++)
            {
                var sampleX = minX + x * stepX + stepX * 0.5f;
                var sampleY = minY + y * stepY + stepY * 0.5f;
                var samplePoint = new Vector2(sampleX, sampleY);

                if (rects.Any(rect => rect.Contains(samplePoint)))
                {
                    coveredSamples++;
                }
            }
        }

        var coverageRatio = (float)coveredSamples / (samplesX * samplesY);
        return width * height * coverageRatio;
    }

    private void CalculateSimilarities()
    {
        _similarityPairs = new List<AtlasSimilarityPair>();

        if (_atlasFootprints == null || _atlasFootprints.Count < 2)
            return;

        // Use a priority queue to keep only the top N similarities
        var topSimilarities = new SortedList<float, AtlasSimilarityPair>(new DescendingComparer());

        // Compare each pair of atlases
        for (int i = 0; i < _atlasFootprints.Count; i++)
        {
            for (int j = i + 1; j < _atlasFootprints.Count; j++)
            {
                var atlas1 = _atlasFootprints[i];
                var atlas2 = _atlasFootprints[j];

                // Fast pre-check: do they share any textures at all?
                if (!HasSharedTextures(atlas1, atlas2))
                    continue;

                var similarity = CalculateAtlasSimilarity(atlas1, atlas2);
                if (similarity != null && similarity.similarityPercentage >= MIN_SIMILARITY_THRESHOLD)
                {
                    // Add to top similarities, maintaining only the best ones
                    var key = similarity.similarityPercentage + UnityEngine.Random.value * 0.001f; // Small random to handle duplicates
                    topSimilarities[key] = similarity;

                    // Keep only top N results
                    if (topSimilarities.Count > MAX_SIMILARITY_PAIRS)
                    {
                        topSimilarities.RemoveAt(topSimilarities.Count - 1);
                    }
                }
            }
        }
        _similarityPairs = topSimilarities.Values.ToList();
    }

    private bool HasSharedTextures(AtlasFootprint atlas1, AtlasFootprint atlas2)
    {
        // Fast check using HashSet intersection
        return atlas1.texturePathsSet.Overlaps(atlas2.texturePathsSet);
    }

    // Custom comparer for descending order in SortedList
    private class DescendingComparer : IComparer<float>
    {
        public int Compare(float x, float y) => y.CompareTo(x);
    }

    private void CalculateFolderSimilarities(OptimiserReport report)
    {
        _folderSimilarityPairs = new List<FolderSimilarityPair>();

        if (report.folderInfos == null || report.folderInfos.Count == 0)
            return;

        // Group atlases by folder
        var folderAtlases = new Dictionary<string, List<AtlasFootprint>>();

        foreach (var atlas in _atlasFootprints)
        {
            // Find which folder this atlas belongs to
            string folderPath = null;
            foreach (var folderInfo in report.folderInfos)
            {
                if (atlas.assetPath.StartsWith(folderInfo.folderTo.Replace("\\", "/")))
                {
                    folderPath = folderInfo.folderTo;
                    break;
                }
            }

            if (folderPath != null)
            {
                if (!folderAtlases.ContainsKey(folderPath))
                    folderAtlases[folderPath] = new List<AtlasFootprint>();
                folderAtlases[folderPath].Add(atlas);
            }
        }

        // Compare folder pairs from the report configuration
        for (int i = 0; i < report.folderInfos.Count; i++)
        {
            var folder1 = report.folderInfos[i].folderTo;
            for (int j = i + 1; j < report.folderInfos.Count; j++)
            {
                var folder2 = report.folderInfos[j].folderTo;

                if (!folderAtlases.ContainsKey(folder1) || !folderAtlases.TryGetValue(folder2, out var atlas))
                    continue;

                var similarity = CalculateFolderSimilarity(
                    folderAtlases[folder1], atlas,
                    folder1, folder2);

                if (similarity != null)
                    _folderSimilarityPairs.Add(similarity);
            }
        }

        _folderSimilarityPairs.Sort((a, b) => b.weightedSimilarityPercentage.CompareTo(a.weightedSimilarityPercentage));
    }

    private FolderSimilarityPair CalculateFolderSimilarity(List<AtlasFootprint> atlases1, List<AtlasFootprint> atlases2, string folder1Path, string folder2Path)
    {
        // Group atlases by shader variant
        var shaderVariants1 = atlases1.GroupBy(a => a.shaderName).ToDictionary(g => g.Key, g => g.First());
        var shaderVariants2 = atlases2.GroupBy(a => a.shaderName).ToDictionary(g => g.Key, g => g.First());

        var allShaderVariants = new HashSet<string>(shaderVariants1.Keys);
        allShaderVariants.UnionWith(shaderVariants2.Keys);

        var comparisons = new List<ShaderVariantComparison>();
        float totalWeightedSimilarity = 0f;
        float totalWeight = 0f;
        float totalAtlasSize = 0f;

        foreach (var shaderVariant in allShaderVariants)
        {
            var atlas1 = shaderVariants1.GetValueOrDefault(shaderVariant);
            var atlas2 = shaderVariants2.GetValueOrDefault(shaderVariant);

            float similarity = 0f;
            float weight = 0f;

            if (atlas1 != null && atlas2 != null)
            {
                // Both folders have this shader variant - calculate similarity
                var similarityPair = CalculateAtlasSimilarity(atlas1, atlas2);
                similarity = similarityPair?.similarityPercentage ?? 0f;
                weight = atlas1.totalFootprintArea + atlas2.totalFootprintArea;
                totalAtlasSize += weight;
            }
            else if (atlas1 != null)
            {
                // Only folder1 has this shader variant
                weight = atlas1.totalFootprintArea;
                totalAtlasSize += weight;
                similarity = 0f; // No match
            }
            else if (atlas2 != null)
            {
                // Only folder2 has this shader variant
                weight = atlas2.totalFootprintArea;
                totalAtlasSize += weight;
                similarity = 0f; // No match
            }

            comparisons.Add(new ShaderVariantComparison
            {
                shaderVariant = shaderVariant,
                atlas1 = atlas1,
                atlas2 = atlas2,
                similarityPercentage = similarity,
                weightContribution = weight
            });

            totalWeightedSimilarity += similarity * weight;
            totalWeight += weight;
        }

        var weightedSimilarity = totalWeight > 0 ? totalWeightedSimilarity / totalWeight : 0f;
        var matchingVariants = comparisons.Count(c => c.atlas1 != null && c.atlas2 != null);

        return new FolderSimilarityPair
        {
            folder1Path = folder1Path,
            folder2Path = folder2Path,
            folder1Name = System.IO.Path.GetFileName(folder1Path),
            folder2Name = System.IO.Path.GetFileName(folder2Path),
            weightedSimilarityPercentage = weightedSimilarity,
            totalShaderVariants = allShaderVariants.Count,
            matchingShaderVariants = matchingVariants,
            shaderComparisons = comparisons,
            totalAtlasSize = totalAtlasSize
        };
    }

    private void DrawFolderSimilarityList()
    {
        var sortedPairs = GetSortedFolderPairs();
        var totalHeight = sortedPairs.Count * FOLDER_PAIR_ROW_HEIGHT;
        var viewRect = GUILayoutUtility.GetRect(0, Mathf.Min(totalHeight, 400));
        var scrollRect = new Rect(viewRect.x, viewRect.y, viewRect.width, totalHeight);

        _scrollPosition = GUI.BeginScrollView(viewRect, _scrollPosition, scrollRect);

        // Draw header
        DrawFolderPairHeader();

        // Draw pairs
        for (int i = 0; i < sortedPairs.Count; i++)
        {
            var pair = sortedPairs[i];
            var pairRect = new Rect(0, 25 + i * FOLDER_PAIR_ROW_HEIGHT, scrollRect.width - 20, FOLDER_PAIR_ROW_HEIGHT);
            DrawFolderSimilarityPair(pair, pairRect, i % 2 == 0);
        }

        GUI.EndScrollView();
    }

    private void DrawFolderPairHeader()
    {
        var headerRect = new Rect(0, 0, Screen.width - 40, 25);
        GUI.Box(headerRect, "", EditorStyles.toolbar);

        float x = 5;
        GUI.Label(new Rect(x, 2, 120, 20), "Folder 1", EditorStyles.toolbarButton);
        x += 125;
        GUI.Label(new Rect(x, 2, 120, 20), "Folder 2", EditorStyles.toolbarButton);
        x += 125;
        GUI.Label(new Rect(x, 2, 80, 20), "Similarity", EditorStyles.toolbarButton);
        x += 85;
        GUI.Label(new Rect(x, 2, 80, 20), "Matching", EditorStyles.toolbarButton);
        x += 85;
        GUI.Label(new Rect(x, 2, 80, 20), "Total Size", EditorStyles.toolbarButton);
        x += 85;
        GUI.Label(new Rect(x, 2, 150, 20), "Actions", EditorStyles.toolbarButton);
    }

    private void DrawFolderSimilarityPair(FolderSimilarityPair pair, Rect rect, bool isEven)
    {
        // Background
        var bgColor = isEven ? new Color(0.8f, 0.8f, 0.8f, 0.1f) : new Color(0.9f, 0.9f, 0.9f, 0.1f);
        EditorGUI.DrawRect(rect, bgColor);

        float x = rect.x + 5;
        float y = rect.y + 5;

        // Folder 1 name
        GUI.Label(new Rect(x, y, 120, 20), pair.folder1Name, EditorStyles.label);
        GUI.Label(new Rect(x, y + 20, 120, 16), $"({pair.folder1Path})", EditorStyles.miniLabel);
        x += 125;

        // Folder 2 name
        GUI.Label(new Rect(x, y, 120, 20), pair.folder2Name, EditorStyles.label);
        GUI.Label(new Rect(x, y + 20, 120, 16), $"({pair.folder2Path})", EditorStyles.miniLabel);
        x += 125;

        // Weighted similarity percentage
        var similarityColor = GetSimilarityColor(pair.weightedSimilarityPercentage);
        var oldColor = GUI.color;
        GUI.color = similarityColor;
        GUI.Label(new Rect(x, y + 10, 80, 20), $"{pair.weightedSimilarityPercentage:F1}%", EditorStyles.boldLabel);
        GUI.color = oldColor;
        x += 85;

        // Matching shader variants
        GUI.Label(new Rect(x, y + 5, 80, 16), $"{pair.matchingShaderVariants}/{pair.totalShaderVariants}", EditorStyles.label);
        GUI.Label(new Rect(x, y + 20, 80, 16), "variants", EditorStyles.miniLabel);
        x += 85;

        // Total atlas size
        var sizeText = pair.totalAtlasSize > 1000000 ? $"{pair.totalAtlasSize / 1000000:F1}M px²" : $"{pair.totalAtlasSize / 1000:F0}K px²";
        GUI.Label(new Rect(x, y + 10, 80, 20), sizeText, EditorStyles.label);
        x += 85;

        // Action buttons - show details of shader variant comparisons
        if (GUI.Button(new Rect(x, y + 5, 140, 18), "Show Shader Details", EditorStyles.miniButton))
        {
            ShowFolderComparisonDetails(pair);
        }
    }

    private void ShowFolderComparisonDetails(FolderSimilarityPair pair)
    {
        var message = $"Folder Comparison: {pair.folder1Name} vs {pair.folder2Name}\n\n";
        message += $"Weighted Similarity: {pair.weightedSimilarityPercentage:F1}%\n";
        message += $"Matching Shader Variants: {pair.matchingShaderVariants}/{pair.totalShaderVariants}\n\n";
        message += "Shader Variant Details:\n";

        foreach (var comparison in pair.shaderComparisons.OrderByDescending(c => c.similarityPercentage))
        {
            var status = comparison.atlas1 != null && comparison.atlas2 != null ?
                $"{comparison.similarityPercentage:F1}% similarity" :
                comparison.atlas1 != null ? "Only in Folder 1" : "Only in Folder 2";

            message += $"• {comparison.shaderVariant}: {status}\n";
        }

        EditorUtility.DisplayDialog("Folder Comparison Details", message, "OK");
    }

    private List<FolderSimilarityPair> GetSortedFolderPairs()
    {
        if (_sortedFolderPairsCache != null && _lastSortMode == _sortMode && _lastSortAscending == _sortAscending)
            return _sortedFolderPairsCache;

        var sortedList = _folderSimilarityPairs.ToList();

        sortedList.Sort((a, b) =>
        {
            int comparison = _sortMode switch
            {
                SortMode.SimilarityPercentage => a.weightedSimilarityPercentage.CompareTo(b.weightedSimilarityPercentage),
                SortMode.AtlasName1 => string.Compare(a.folder1Name, b.folder1Name, StringComparison.OrdinalIgnoreCase),
                SortMode.AtlasName2 => string.Compare(a.folder2Name, b.folder2Name, StringComparison.OrdinalIgnoreCase),
                SortMode.SharedTextureCount => a.matchingShaderVariants.CompareTo(b.matchingShaderVariants),
                SortMode.TotalFootprintOverlap => a.totalAtlasSize.CompareTo(b.totalAtlasSize),
                _ => 0
            };

            return _sortAscending ? comparison : -comparison;
        });

        // Cache the sorted result
        _sortedFolderPairsCache = sortedList;
        _lastSortMode = _sortMode;
        _lastSortAscending = _sortAscending;

        return sortedList;
    }

    private AtlasSimilarityPair CalculateAtlasSimilarity(AtlasFootprint atlas1, AtlasFootprint atlas2)
    {
        // Find shared textures between the two atlases
        var sharedTextures = new List<string>();
        var totalOverlapArea = 0f;

        foreach (var texture1 in atlas1.textureFootprints)
        {
            if (atlas2.textureFootprints.TryGetValue(texture1.Key, out var area2))
            {
                sharedTextures.Add(texture1.Key);
                // The overlap area is the minimum of the two areas used from this texture
                totalOverlapArea += Mathf.Min(texture1.Value, area2);
            }
        }

        // Calculate similarity percentage based on overlap relative to total footprint
        var totalFootprintArea = atlas1.totalFootprintArea + atlas2.totalFootprintArea;
        var similarityPercentage = 0f;

        if (totalFootprintArea > 0)
        {
            // Similarity is the ratio of overlapping area to the union of both footprints
            // Union = Total - Overlap (to avoid double counting)
            var unionArea = totalFootprintArea - totalOverlapArea;
            if (unionArea > 0)
            {
                similarityPercentage = (totalOverlapArea * 2f / totalFootprintArea) * 100f;
            }
        }

        return new AtlasSimilarityPair
        {
            atlas1 = atlas1,
            atlas2 = atlas2,
            similarityPercentage = similarityPercentage,
            sharedTextureCount = sharedTextures.Count,
            totalFootprintOverlap = totalOverlapArea,
            sharedTextures = sharedTextures
        };
    }
}
