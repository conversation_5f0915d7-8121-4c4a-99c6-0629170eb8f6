using UnityEngine;
using UnityEditor;
using UnityEditor.SceneManagement;
using System.Collections.Generic;

public class PrefabVertexCounterWindow : EditorWindow
{
    private DefaultAsset folderAsset;

    [MenuItem("Art Tools/Prefab Vertex Counter")]
    public static void ShowWindow()
    {
        GetWindow<PrefabVertexCounterWindow>("Prefab Vertex Counter");
    }

    private void OnGUI()
    {
        GUILayout.Label("Select Folder with Prefabs", EditorStyles.boldLabel);
        folderAsset = (DefaultAsset)EditorGUILayout.ObjectField("Folder", folderAsset, typeof(DefaultAsset), false);

        if (folderAsset != null)
        {
            if (GUILayout.Button("Count Vertices"))
            {
                string folderPath = AssetDatabase.GetAssetPath(folderAsset);
                if (!AssetDatabase.IsValidFolder(folderPath))
                {
                    EditorUtility.DisplayDialog("Error", "Selected asset is not a folder.", "OK");
                }
                else
                {
                    CountVerticesInPrefabs(folderPath);
                }
            }
        }
    }

    private void CountVerticesInPrefabs(string folderPath)
    {
        // Find all prefab asset GUIDs in the folder
        string[] guids = AssetDatabase.FindAssets("t:Prefab", new[] { folderPath });
        long totalVertices = 0;
        int prefabCount = guids.Length;

        for (int i = 0; i < guids.Length; i++)
        {
            string assetPath = AssetDatabase.GUIDToAssetPath(guids[i]);
            GameObject prefab = AssetDatabase.LoadAssetAtPath<GameObject>(assetPath);
            if (prefab == null)
                continue;

            // Instantiate prefab temporarily to count mesh data
            GameObject tempInstance = (GameObject)PrefabUtility.InstantiatePrefab(prefab);
            if (tempInstance == null)
                continue;

            // Get all MeshFilter and SkinnedMeshRenderer components
            MeshFilter[] meshFilters = tempInstance.GetComponentsInChildren<MeshFilter>(true);
            foreach (var mf in meshFilters)
            {
                if (mf.sharedMesh != null)
                    totalVertices += mf.sharedMesh.vertexCount;
            }

            SkinnedMeshRenderer[] skinnedRenderers = tempInstance.GetComponentsInChildren<SkinnedMeshRenderer>(true);
            foreach (var smr in skinnedRenderers)
            {
                if (smr.sharedMesh != null)
                    totalVertices += smr.sharedMesh.vertexCount;
            }

            // Destroy the temporary instance
            DestroyImmediate(tempInstance);
        }

        // Display result
        EditorUtility.DisplayDialog(
            "Vertex Count Result",
            string.Format("Processed {0} prefabs.\nTotal Vertices (including duplicates): {1}", prefabCount, totalVertices),
            "OK"
        );
    }
}