using UnityEngine;

public class AkRTPCController : MonoBehaviour
{
    public AkRTPCHolder m_rtpc;
    public enum ERTPCEffector
    {
        PlantCovered,
        HasOccupants,
    }
    public ERTPCEffector m_effect;
    
    private int m_currentState = -1;
    private System.Func<bool> m_isActiveCb = null;
    
    private PlantController m_plantController;
    private MABuilding m_building;

    #if UNITY_EDITOR
    void OnValidate() => Setup();
    #endif
    
    void Start() => Setup();
    
    void Setup()
    {
        switch (m_effect)
        {
            case ERTPCEffector.PlantCovered:
                m_plantController = GetComponentInParent<PlantController>();
                if (m_plantController != null)
                    m_isActiveCb = () => m_plantController.m_plantLevel < .001f;
                else
                    m_isActiveCb = null;
                break;
            case ERTPCEffector.HasOccupants:
                m_building = GetComponentInParent<MABuilding>();
                if (m_building != null)
                    m_isActiveCb = () => m_building.GetCharactersPresent().Count > 0;
                else
                    m_isActiveCb = null;
                break;
        }
    }

    void Update()
    {
        int newState = -1;
        if (m_isActiveCb != null) newState = m_isActiveCb() ? 1 : 0;
        if (newState != m_currentState)
        {
            m_currentState = newState;
            m_rtpc.Set(m_currentState, gameObject);
        }
    }
}
