using UnityEngine;

public class TestingTargetStructure : MABuilding
{
    [SerializeField]
    private Transform door = null;
    [SerializeField]
    private int priority = 8;
    [SerializeField]
    private float health = 100f;

    public override bool CanBeTargeted => true;

    public override int TargetPriority
    {
        get { return priority; }
        set { }
    }

    public override Vector3 DoorPosOuter
    {
        get { return door.position; }
        set { }
    }

    protected override void Start()
    {
        name = "TargetStructure";
        m_stateData = new GameState_Building() {m_healthNorm = health};
        InitialiseBuildingData(m_stateData, null, gameObject);
    }

    override protected void Update()
	{
        
	}

    public override void ApplyDamageEffect(IDamageReceiver.DamageSource source, float _damageDone, Vector3 _sourceOfDamage, MAAttackInstance attack = null, MAHandPowerInfo handPower = null)
    {
        float prevHealth = Health;
        base.ApplyDamageEffect(source, _damageDone, _sourceOfDamage, attack, handPower);
        float newHealth = Health;

        Debug.LogError("TestingTargetStructure - DAMAGE=" + _damageDone + " PREV HEALTH=" + prevHealth + " NEW HEALTH=" + newHealth);

        if (newHealth <= 0f)
            gameObject.SetActive(false);
    }

    public override MAMovingInfoBase GetMovingInfo()
    {
        return null;
    }
}
