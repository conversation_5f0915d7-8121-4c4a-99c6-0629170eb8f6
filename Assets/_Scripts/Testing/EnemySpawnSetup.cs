using System;
using System.Collections.Generic;
using UnityEngine;

public class EnemySpawnSetup : MonoBehaviour
{
    [Serializable]
    public class EnemySpawnInfo
    {
        public string enemyType = "";
        public int spawnAmount = 0;
    };

    [SerializeField]
    private KeyCode hotKey = KeyCode.None;
    public KeyCode HotKey => hotKey;

    [SerializeField]
    private List<EnemySpawnInfo> enemySpawnInfos = null;
    public List<EnemySpawnInfo> EnemySpawnInfos => enemySpawnInfos;
}
