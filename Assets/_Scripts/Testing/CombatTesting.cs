using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;

public class CombatTesting : MonoBehaviour, IImpactDataSupplier
{
    private enum CombatSettingOption
    {
        EnemyCount = 0,
        EnemyDistance,
        EnemyName,
        ComboName,
        RuneName,
        ComboMode
    };

    private enum ComboMode
    {
        Normal = 0,
        Branching,
        Count
    };

    private bool sceneReady = false;

    private MACharacterBase hero = null;
    
    private string heroTargetsStr = "";
    private List<string> heroTargets = null;
    private List<string> HeroTargets
    {
        get
        {
            if (hero == null)
                return null;
            
            if (heroTargets == null)
            {
                heroTargetsStr = hero.CreatureInfo.m_creatureTargets;
                var splits = hero.CreatureInfo.m_creatureTargets.Split('|');
                heroTargets = new List<string>(splits);
            }

            return heroTargets;
        }
    }

    private List<string> comboNames = null;
    private List<string> ComboNames
    {
        get
        {
            if (comboNames == null)
            {
                comboNames = new List<string>();
                foreach (var combo in MAAttackCombo.s_combos)
                {
                    if (string.IsNullOrEmpty(combo.AttackSkills[0].RuneEffect))
                        comboNames.Add(combo.ID);
                }
            }

            return comboNames;
        }
    }

    private List<string> runeNames = null;
    private List<string> RuneNames
    {
        get
        {
            if (runeNames == null)
            {
                runeNames = new List<string>();
                foreach (var combo in MAAttackCombo.s_combos)
                {
                    if (!string.IsNullOrEmpty(combo.AttackSkills[0].RuneEffect))
                        runeNames.Add(combo.ID);
                }
            }

            return runeNames;
        }
    }

    private float countElapsedTime = 0f;
    private int selectedOption = 0;
    private List<int> combatSettingOptions = null;
    private List<int> CombatSettingOptions
    {
        get
        {
            if (combatSettingOptions == null)
            {
                combatSettingOptions = new List<int>
                {
                    1, //CombatSettingOption.EnemyCount
                    20, //CombatSettingOption.EnemyDistance
                    0, //CombatSettingOption.EnemyName
                    ComboNames.IndexOf(hero.m_meleeComboState.GetComboID()), //CombatSettingOption.ComboName
                    RuneNames.IndexOf(hero.m_runeComboState.GetComboID()), //CombatSettingOption.RuneName
                    (int)comboMode, //CombatSettingOption.ComboMode
                };
            }

            return combatSettingOptions;
        }
    }

    [SerializeField]
    private GameObject enemySpawnUI = null;
    [SerializeField]
    private TextMeshProUGUI enemyCountText = null;
    [SerializeField]
    private TextMeshProUGUI enemyDistanceText = null;
    [SerializeField]
    private TextMeshProUGUI enemyPrefabNameText = null;
    [SerializeField]
    private TextMeshProUGUI comboNameText = null;
    [SerializeField]
    private TextMeshProUGUI runeIdText = null;
    [SerializeField]
    private TextMeshProUGUI comboModeText = null;

    [SerializeField]
    private Transform characterRoot = null;
    [SerializeField]
    private Transform heroSpawnPoint = null;
    private bool showHealthBar = true;
    private bool refreshHealthBars = false;

    [SerializeField]
    private GameObject slopesRoot = null;

    [SerializeField]
    private GameObject zombieSetupsRoot = null;
    private List<EnemySpawnSetup> zombieSetups = null;
    private List<EnemySpawnSetup> ZombieSetups
    {
        get
        {
            if (zombieSetups == null)
            {
                var setups = zombieSetupsRoot.GetComponentsInChildren<EnemySpawnSetup>();
                zombieSetups = new List<EnemySpawnSetup>(setups);
            }

            return zombieSetups;
        }
    }

    [SerializeField]
    private GameObject hotKeysUI = null;
    [SerializeField]
    private TextMeshProUGUI hotKeysText = null;

    private bool uiNeedsRefresh = false;

    private ComboMode comboMode = ComboMode.Normal;
    private string comboBranchingName = "HeroBranchingCombo";
    private int lastNonBranchingCombo = -1;

    private float masterVolume = 0.4f;

    [SerializeField]
    private HealthBar possessedHealthBar = null;

    [SerializeField]
    private Transform guardTransform = null;
    [SerializeField]
    private float guardRadius = 10000f;

		public static float MaxMana => 1000f;

    private bool targetsDisabled = false;

    [SerializeField]
    private GameObject wall = null;

    [SerializeField]
    private Transform spawnTransform = null;
    
    public void Start()
    {
        CreateCheckerboard(null);
        
        StartCoroutine(Co_InitTestingScene());
    }

    public void Update()
    {
        if (!sceneReady)
            return;
        
        if (Input.GetKeyDown(KeyCode.RightShift))
        {
            enemySpawnUI.SetActive(!enemySpawnUI.activeSelf);
            hotKeysUI.SetActive(!hotKeysUI.activeSelf);
        }

        UpdateOptions();
        UpdateUITexts();
        UpdateUIAction();

        UpdateHotkeys();
        
        if (Input.GetKeyDown(KeyCode.Semicolon))
        {
            if (!GameManager.Me.IsPossessing)
            {
                OnPossessHero();
            }
            else
            {
                OnUnpossessHero();
            }
        }

        if (Input.GetKeyDown(KeyCode.H) || refreshHealthBars)
        {
            if (!refreshHealthBars)
                showHealthBar = !showHealthBar;
            refreshHealthBars = false;
            
            foreach (Transform child in characterRoot.GetComponentsInChildren<Transform>(true))
            {
                if (child.gameObject.name.Contains("MA_HealthBar"))
                {
                    child.gameObject.SetActive(showHealthBar);
                }
            }
        }

        if (Input.GetKeyDown(KeyCode.Comma))
        {
            slopesRoot.SetActive(!slopesRoot.activeSelf);
        }

        if (Input.GetKeyDown(KeyCode.M))
        {
            GameManager.Me.m_state.m_powerMana = MaxMana;
        }

        if (Input.GetKeyDown(KeyCode.Minus))
        {
            masterVolume = Mathf.Clamp01(masterVolume - 0.1f);
            SetAudioVolume(masterVolume);
        }
        else if (Input.GetKeyDown(KeyCode.Equals))
        {
            masterVolume = Mathf.Clamp01(masterVolume + 0.1f);
            SetAudioVolume(masterVolume);
        }

        if (Input.GetKeyDown(KeyCode.Backslash))
        {
            targetsDisabled = !targetsDisabled;
            RefreshTargets();
        }

        if (Input.GetKeyDown(KeyCode.Slash))
        {
            foreach (var enemy in characterRoot.GetComponentsInChildren<MACreatureBase>())
            {
                enemy.SetupStaggerBackAnim(new Vector3(-10f, 0f, 20f));
            }
        }

        if (Input.GetKeyDown(KeyCode.Quote))
        {
            foreach (var enemy in characterRoot.GetComponentsInChildren<MACreatureBase>())
            {
                MAPowerEffectBase.CreateExplosionAtPoint(IDamageReceiver.DamageSource.Debug, enemy.transform.position, 2f, 5000f, this, Vector3.up);
            }
        }

        if ((hero == null) || hero.InState(CharacterStates.Dying) || hero.InState(CharacterStates.Dead))
        {
            sceneReady = false;
            OnUnpossessHero();
            hero = null;
            
            StartCoroutine(Co_RespawnHero());
        }
    }

    private void UpdateOptions()
    {
        if (Input.GetKeyDown(KeyCode.UpArrow))
        {
            if (--selectedOption < 0)
                selectedOption = CombatSettingOptions.Count - 1;
            
            uiNeedsRefresh = true;
        }
        if (Input.GetKeyDown(KeyCode.DownArrow))
        {
            if (++selectedOption >= CombatSettingOptions.Count)
                selectedOption = 0;
            
            uiNeedsRefresh = true;
        }

        int first = 0;
        int length = 0;
        int bigOffset = 0;
        var option = (CombatSettingOption)selectedOption;
        switch (option)
        {
            case CombatSettingOption.EnemyCount:
                first = 1;
                length = 101;
                bigOffset = 10;
                break;
            case CombatSettingOption.EnemyDistance:
                first = 20;
                length = 51;
                bigOffset = 5;
                break;
            case CombatSettingOption.EnemyName:
                first = 0;
                length = HeroTargets.Count;
                bigOffset = 1;
                break;
            case CombatSettingOption.ComboName:
                first = 0;
                length = ComboNames.Count;
                bigOffset = 1;
                break;
            case CombatSettingOption.RuneName:
                first = 0;
                length = RuneNames.Count;
                bigOffset = 1;
                break;
            case CombatSettingOption.ComboMode:
                first = 0;
                length = (int)ComboMode.Count;
                bigOffset = 1;
                break;
        }
        if (Input.GetKeyDown(KeyCode.LeftArrow))
        {
            countElapsedTime = 0f;
            if (--CombatSettingOptions[selectedOption] < first)
                CombatSettingOptions[selectedOption] = length - 1;
            
            uiNeedsRefresh = true;
        }
        if (Input.GetKey(KeyCode.LeftArrow))
        {
            if (countElapsedTime >= 0.25f)
            {
                countElapsedTime = 0f;
                CombatSettingOptions[selectedOption] -= bigOffset;
                if (CombatSettingOptions[selectedOption] < first)
                    CombatSettingOptions[selectedOption] = length - 1;
            }
            else
            {
                countElapsedTime += Time.deltaTime;
            }
            
            uiNeedsRefresh = true;
        }
        if (Input.GetKeyDown(KeyCode.RightArrow))
        {
            countElapsedTime = 0f;
            if (++CombatSettingOptions[selectedOption] >= length)
                CombatSettingOptions[selectedOption] = first;
            
            uiNeedsRefresh = true;
        }
        if (Input.GetKey(KeyCode.RightArrow))
        {
            if (countElapsedTime >= 0.25f)
            {
                countElapsedTime = 0f;
                CombatSettingOptions[selectedOption] += bigOffset;
                if (CombatSettingOptions[selectedOption] >= length)
                    CombatSettingOptions[selectedOption] = first;
            }
            else
            {
                countElapsedTime += Time.deltaTime;
            }
            
            uiNeedsRefresh = true;
        }
    }

    private void UpdateUITexts()
    {
        if (!uiNeedsRefresh)
            return;
        
        uiNeedsRefresh = false;
        
        int option = (int)CombatSettingOption.EnemyCount;
        enemyCountText.color = (selectedOption == option) ? Color.red : Color.black;
        enemyCountText.fontStyle = (selectedOption == option) ? FontStyles.Bold : FontStyles.Normal;
        int index = CombatSettingOptions[option];
        enemyCountText.text = $"Spawn Count: {index}";

        option = (int)CombatSettingOption.EnemyDistance;
        enemyDistanceText.color = (selectedOption == option) ? Color.red : Color.black;
        enemyDistanceText.fontStyle = (selectedOption == option) ? FontStyles.Bold : FontStyles.Normal;
        index = CombatSettingOptions[option];
        enemyDistanceText.text = $"Spawn Distance: {index}";

        option = (int)CombatSettingOption.EnemyName;
        enemyPrefabNameText.color = (selectedOption == option) ? Color.red : Color.black;
        enemyPrefabNameText.fontStyle = (selectedOption == option) ? FontStyles.Bold : FontStyles.Normal;
        index = CombatSettingOptions[option];
        enemyPrefabNameText.text = HeroTargets[index];

        option = (int)CombatSettingOption.ComboName;
        comboNameText.color = (selectedOption == option) ? Color.red : Color.black;
        comboNameText.fontStyle = (selectedOption == option) ? FontStyles.Bold : FontStyles.Normal;
        index = CombatSettingOptions[option];
        comboNameText.text = ComboNames[index];
        bool isDifferent = index != ComboNames.IndexOf(hero.m_meleeComboState.GetComboID());
        if (isDifferent)
        {
            comboNameText.fontStyle |= FontStyles.Italic;
            comboNameText.text += "*";
        }

        option = (int)CombatSettingOption.RuneName;
        runeIdText.color = (selectedOption == option) ? Color.red : Color.black;
        runeIdText.fontStyle = (selectedOption == option) ? FontStyles.Bold : FontStyles.Normal;
        index = CombatSettingOptions[option];
        runeIdText.text = RuneNames[index];
        isDifferent = index != RuneNames.IndexOf(hero.m_runeComboState.GetComboID());
        if (isDifferent)
        {
            runeIdText.fontStyle |= FontStyles.Italic;
            runeIdText.text += "*";
        }

        option = (int)CombatSettingOption.ComboMode;
        comboModeText.color = (selectedOption == option) ? Color.red : Color.black;
        comboModeText.fontStyle = (selectedOption == option) ? FontStyles.Bold : FontStyles.Normal;
        index = CombatSettingOptions[option];
        comboModeText.text = $"Combo Mode: {(ComboMode)index}";
        isDifferent = index != (int)comboMode;
        if (isDifferent)
        {
            comboModeText.fontStyle |= FontStyles.Italic;
            comboModeText.text += "*";
        }
    }

    private void UpdateHotkeys()
    {
        string text = "";
        foreach (var setup in ZombieSetups)
        {
            if (!string.IsNullOrEmpty(text))
                text += " - ";
            var keyName = GetHotKeyNameFromKeyCode(setup.HotKey);
            text += keyName + " " + setup.name;
            if (Input.GetKeyDown(setup.HotKey))
            {
                foreach (var info in setup.EnemySpawnInfos)
                {
                    float distance = UnityEngine.Random.Range(20f, 30f);
                    SpawnEnemiesWithName(info.enemyType, info.spawnAmount, distance);
                }
            }
        }

        hotKeysText.text = text;
    }

    private string GetHotKeyNameFromKeyCode(KeyCode key)
    {
        return key switch
        {
            KeyCode.Alpha1 => "1",
            KeyCode.Alpha2 => "2",
            KeyCode.Alpha3 => "3",
            KeyCode.Alpha4 => "4",
            KeyCode.Alpha5 => "5",
            KeyCode.Alpha6 => "6",
            KeyCode.Alpha7 => "7",
            KeyCode.Alpha8 => "8",
            KeyCode.Alpha9 => "9",
            KeyCode.Alpha0 => "0",
            _ => "",
        };
    }

    private void UpdateUIAction(bool forceUpdate = false)
    {
        if (!Input.GetKeyDown(KeyCode.RightAlt) && !forceUpdate)
            return;
        
        var option = (CombatSettingOption)selectedOption;
        switch (option)
        {
            case CombatSettingOption.EnemyCount:
            case CombatSettingOption.EnemyDistance:
            case CombatSettingOption.EnemyName:
            {
                int index = CombatSettingOptions[(int)CombatSettingOption.EnemyName];
                int enemyCount = CombatSettingOptions[(int)CombatSettingOption.EnemyCount];
                float spawnDist = (float)CombatSettingOptions[(int)CombatSettingOption.EnemyDistance];
                SpawnEnemiesWithName(HeroTargets[index], enemyCount, spawnDist);
                break;
            }
            case CombatSettingOption.ComboName:
            {
                bool shouldResetCurrentCombo = hero.CurrentComboState == hero.m_meleeComboState;
                int index = CombatSettingOptions[(int)CombatSettingOption.ComboName];
                var comboName = ComboNames[index];
                bool isBranching = comboName == comboBranchingName;
                hero.m_meleeComboState = new MAAttackComboState(hero, MAAttackCombo.GetByID(comboName));
                if (!isBranching)
                    lastNonBranchingCombo = index;
                if (shouldResetCurrentCombo)
                    hero.CurrentComboState = hero.m_meleeComboState;
                comboMode = isBranching ? ComboMode.Branching : ComboMode.Normal;
                CombatSettingOptions[(int)CombatSettingOption.ComboMode] = (int)comboMode;
                SetupWeaponRune();
                GameManager.BranchingCombosEnabled = isBranching;
                break;
            }
            case CombatSettingOption.RuneName:
            {
                bool shouldResetCurrentCombo = hero.CurrentComboState == hero.m_runeComboState;
                int index = CombatSettingOptions[(int)CombatSettingOption.RuneName];
                hero.m_runeComboState = new MAAttackComboState(hero, MAAttackCombo.GetByID(RuneNames[index]));
                if (shouldResetCurrentCombo)
                    hero.CurrentComboState = hero.m_runeComboState;
                SetupWeaponRune();
                break;
            }
            case CombatSettingOption.ComboMode:
            {
                comboMode = (ComboMode)CombatSettingOptions[(int)CombatSettingOption.ComboMode];
                var nonBranchingComboName = (lastNonBranchingCombo > -1) ? ComboNames[lastNonBranchingCombo] : "Hero3HitComboRootmotion";
                int idx = ComboNames.IndexOf(comboMode == ComboMode.Branching ? "HeroBranchingCombo" : nonBranchingComboName);
                hero.m_meleeComboState = new MAAttackComboState(hero, MAAttackCombo.GetByID(ComboNames[idx]));
                hero.CurrentComboState = hero.m_meleeComboState;
                CombatSettingOptions[(int)CombatSettingOption.ComboName] = idx;
                SetupWeaponRune();
                GameManager.BranchingCombosEnabled = comboMode == ComboMode.Branching;
                break;
            }
        }
        
        uiNeedsRefresh = true;
    }

    public void SpawnEnemiesWithName(string enemyName, int enemyCount, float spawnDist)
    {
        if (!sceneReady)
            return;
        
        var group = new List<MACharacterGroupBehaviour.GroupCharacterInfo>();
        bool canGroupPatrol = false;
        var spawnDir = Quaternion.Euler(0f, UnityEngine.Random.Range(0f, 360f), 0f) * Vector3.forward;
        var info = MACreatureInfo.GetInfo(enemyName);
        var centrePos = info.m_isGroup ? guardTransform.position : hero.transform.position;
        spawnDist = info.m_isGroup ? 1f : spawnDist;
        var startPos = centrePos + (spawnDir * spawnDist);
        int spawned = 0;
        int maxSpawn = 1;
        float radius = 0f;
        string creatureType = "";
        for (int i = 0; i < enemyCount; ++i)
        {
            var dir = Quaternion.Euler(0f, UnityEngine.Random.Range(0f, 360f), 0f) * Vector3.forward;
            var pos = startPos + (dir * radius);
            var lookAt = hero.transform.position - pos;
            if (enemyCount == 1)
                pos = spawnTransform.position;
            var enemy = SpawnEnemy(enemyName, pos.AboveGround(1f));
            enemy.transform.rotation = Quaternion.LookRotation(lookAt.normalized, Vector3.up);
            if (info.m_isGroup)
            {
                var stationary = (Random.Range(0, 100) < 50) ? enemy.transform.position : Vector3.zero;
                group.Add(new MACharacterGroupBehaviour.GroupCharacterInfo
                {
                    character = enemy,
                    stationaryPos = stationary
                });
                canGroupPatrol = enemy.CreatureInfo.m_groupPatrolRadius > 0f;
                creatureType = enemy.CreatureInfo.m_creatureType;
            }
            ++spawned;

            if (spawned >= maxSpawn)
            {
                spawned = 0;
                maxSpawn *= 2;
                radius += 2f;
            }
        }

        if (group.Count > 0)
        {
            var prefab = Resources.Load<GameObject>("_Prefabs/Characters/CharacterGroupBehaviour");
            var go = Instantiate(prefab, Vector3.zero, Quaternion.identity, GlobalData.Me.m_characterHolder);
            var cgb = go.GetComponent<MACharacterGroupBehaviour>();
            cgb.SetupGroup(creatureType);
            if (canGroupPatrol)
            {
                var patrollingInfo = new MACharacterGroupBehaviour.GroupPatrollingInfo { centerPos = guardTransform.position, radius = guardRadius };
                cgb.SetPatrollingInfo(patrollingInfo);
            }
			cgb.AddCharacters(group);
			cgb.StartGroup();
        }

        RefreshTargets();
        refreshHealthBars = true;
    }

    private MACharacterBase SpawnEnemy(string enemyPrefabName, Vector3 pos)
    {
        var info = MACreatureInfo.GetInfo(enemyPrefabName);

        return MACharacterBase.Create(info, pos);
    }

    private void SetupWeaponRune()
    {
        if (comboMode == ComboMode.Normal)
        {
            var runeEffect = hero.m_runeComboState.GetAttackInstanceFromIndex(0).RuneEffect;
            int id = StickerData.s_entries.FindIndex(o => o.m_name == runeEffect);
            hero.m_weaponRunes.Clear();
            var weaponRune = new MACharacterBase.WeaponRune();
            weaponRune.stickerDataId = id;
            weaponRune.runeName = runeEffect;
            weaponRune.fraction = 0f;
            weaponRune.owner = hero.Weapon.transform;
            weaponRune.localPos = Vector3.zero;
            hero.m_weaponRunes.Add(weaponRune);
        }
        else
        {
            hero.m_weaponRunes.Clear();
            string[] runeEffects = { "Lightning Rune", "Fire Rune" };
            for (int i=0; i<runeEffects.Length; i++)
            {
                int id = StickerData.s_entries.FindIndex(o => o.m_name == runeEffects[i]);
                var weaponRune = new MACharacterBase.WeaponRune();
                weaponRune.stickerDataId = id;
                weaponRune.runeName = runeEffects[i];
                weaponRune.fraction = 0f;
                weaponRune.owner = hero.Weapon.transform;
                weaponRune.localPos = Vector3.zero;
                hero.m_weaponRunes.Add(weaponRune);
            }
        }
    }

    public void OnPossessHero()
    {
        if (hero == null)
            return;
        
        GameManager.Me.PossessObject(hero, true, true);
        GameManager.Me.SnapPossessionCamera();

        hero.SetExternalHealthBar(possessedHealthBar);
    }

    public void OnUnpossessHero()
    {
        GameManager.Me.Unpossess();

        if (hero != null)
        {
            hero.SetExternalHealthBar(null);
        }
    }

    private void SetAudioVolume(float volume)
    {
        AudioClipManager.Me.SetMusicVolume(volume);
        AudioClipManager.Me.SetSFXVolume(volume);
        AudioClipManager.Me.SetVOVolume(volume);
    }

    private void RefreshTargets()
    {
        if (string.IsNullOrEmpty(heroTargetsStr))
            return;
        
        var info = hero.CreatureInfo;
        if (!targetsDisabled)
            info.m_creatureTargets = heroTargetsStr;
        else
            info.m_creatureTargets = "";
        info.CreatureTargets.Clear();
        hero.SetTargetObj(null);

        foreach (var enemy in characterRoot.GetComponentsInChildren<MACreatureBase>())
        {
            var infoEnemy = enemy.CreatureInfo;
            if (!targetsDisabled)
                infoEnemy.m_creatureTargets = "Giant";
            else
                infoEnemy.m_creatureTargets = "";
            infoEnemy.CreatureTargets.Clear();
            enemy.SetTargetObj(null);
            if (enemy.GroupBehaviour != null)
                enemy.GroupBehaviour.SetupNewBestTarget(null, null, null);
        }
    }

    public IEnumerator Co_InitTestingScene()
    {
        NGBlockInfo.LoadInfo();
        MAAttackSkill.LoadInfo();
        MAAttackCombo.LoadInfo();
        MACreatureInfo.LoadInfo();
        StickerData.LoadInfo();
        MACameraControl.LoadInfo();
        GameManager.Me.enabled = true;
        GameManager.Me.DataReady = true;
        GameManager.Me.LoadComplete = true;

        yield return null;

        var rsl = wall.AddComponent<RoadSetLink>();
        rsl.Set(wall.GetComponent<RoadSet>());

        GameManager.Me.m_state.m_powerMana = MaxMana;
        
        SpawnHero();
        
        selectedOption = 0;
        countElapsedTime = 0f;
        lastNonBranchingCombo = -1;
        int index = CombatSettingOptions[(int)CombatSettingOption.ComboName];
        if (ComboNames[index] != comboBranchingName)
            lastNonBranchingCombo = index;

        yield return null;
        
        SetupWeaponRune();

        masterVolume = 0.4f;
        SetAudioVolume(masterVolume);

        OnPossessHero();

        RefreshTargets();
        uiNeedsRefresh = true;
        refreshHealthBars = true;
        sceneReady = true;

        // var assembly = Assembly.GetAssembly(typeof(UnityEditor.Editor));
        // var type = assembly.GetType("UnityEditor.LogEntries");
        // var method = type.GetMethod("Clear");
        // method.Invoke(new object(), null);
    }

    public IEnumerator Co_RespawnHero()
    {
        yield return null;

        SpawnHero();

        yield return null;
        
        SetupWeaponRune();

        yield return null;

        int prevOption = selectedOption;
        selectedOption = (int)CombatSettingOption.ComboName;
        UpdateUIAction(true);
        selectedOption = (int)CombatSettingOption.RuneName;
        UpdateUIAction(true);
        selectedOption = (int)CombatSettingOption.ComboMode;
        UpdateUIAction(true);
        selectedOption = prevOption;

        GameManager.Me.m_state.m_powerMana = MaxMana;
        
        OnPossessHero();

        RefreshTargets();
        uiNeedsRefresh = true;
        refreshHealthBars = true;
        sceneReady = true;
    }

    public void SpawnHero()
    {
        var info = MACreatureInfo.GetInfo("Giant");
        hero = MACharacterBase.Create(info, heroSpawnPoint.position.AboveGround(1f));
        var heroBase = hero as MAHeroBase;
        heroBase.HeroGameState.m_characterExperience.m_level = 1;
    }

    private void CreateCheckerboard(MeshRenderer mesh)
    {
        if (mesh == null)
            return;
        
        var tex = new Texture2D(64, 64, TextureFormat.RGBA32, true, true);
        tex.wrapMode = TextureWrapMode.Repeat;
        tex.filterMode = FilterMode.Bilinear;
        int length = 2;
        int lengthDouble = length * length;
        int i = 0;
        for (int y = 0; y < tex.height; ++y)
        {
            for (int x = 0; x < tex.width; ++x)
            {
                float valX = (float)(x % lengthDouble) / (float)lengthDouble;
                int vX = (valX < 0.5f) ? 0 : 1;

                float valY = (float)(y % lengthDouble) / (float)lengthDouble;
                int vY = (valY < 0.5f) ? 0 : 1;

                float val = (vX == vY) ? 1f: 0.8f;

                var col = new Color(val, val, val, 1f);
                tex.SetPixel(x, y, col);
                ++i;
            }
        }
        tex.Apply();

        mesh.material.mainTexture = tex;
    }

    public void PlayImpactAudioOnObject(NGMovingObject _obj)
    {
        
    }

    public void ApplyDamage(IDamageReceiver.DamageSource _source, NGMovingObject _obj, bool _isContinuous, float _multiplier = 1)
    {
		_obj.ApplyDamageEffect(_source, 1f, _obj.transform.position + Vector3.up * 1.5f);
    }
}
