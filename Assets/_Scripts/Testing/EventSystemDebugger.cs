using UnityEngine;
using UnityEngine.EventSystems;

public class EventSystemDebugger : MonoBehaviour
{
    void Update()
    {
        PointerEventData pointerData = new PointerEventData(EventSystem.current)
        {
            position = Input.mousePosition
        };
        RaycastResult raycastResult = PerformRaycast(pointerData);
        if (raycastResult.gameObject != null)
            Debug.LogError("AAAAAAA - Currently over GameObject: " + raycastResult.gameObject.name);
        else
            Debug.LogError("AAAAAAA - No GameObject hit by the EventSystem.");
    }

    private RaycastResult PerformRaycast(PointerEventData pointerData)
    {
        var results = new System.Collections.Generic.List<RaycastResult>();
        EventSystem.current.RaycastAll(pointerData, results);

        return (results.Count > 0) ? results[0] : new RaycastResult();
    }
}

