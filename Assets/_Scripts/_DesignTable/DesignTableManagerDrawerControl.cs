using System;
using System.Collections.Generic;
using UnityEngine;

public partial class DesignTableManager
{
	Dictionary<string, DrawerSetContainer> m_designInPlaceDrawers = new();
	
	
	private const float c_drawerRowDepth = 1.35f;
	private const float c_newDrawerOffset = -20;
	private const float c_drawerItemSide = 1.5f, c_drawerItemFwd = -c_drawerRowDepth, c_drawerItemUp = -.65f;

	void AddToDrawer(GameObject _obj, int _drawerIndex, bool _twoPerRow, DTDragDrawer[] _set)
	{
		var dp = _obj.gameObject.AddComponent<DTDragPalette>();
		_obj.transform.parent = _set[_drawerIndex].m_contents.transform;
		float sideOffs, orientation, fwdOffs;
		_twoPerRow = false;
		sideOffs = .1f;
		fwdOffs = c_newDrawerOffset;
		orientation = 90;
		
		if (_twoPerRow)
		{
			_obj.transform.localPosition = new Vector3((m_nextDrawerItemPos[_drawerIndex] & 1) * c_drawerItemSide + sideOffs, c_drawerItemUp, (m_nextDrawerItemPos[_drawerIndex] / 2) * c_drawerItemFwd + fwdOffs);
		}
		else
		{
			_obj.transform.localPosition = new Vector3(.5f * c_drawerItemSide + sideOffs, c_drawerItemUp, m_nextDrawerItemPos[_drawerIndex] * c_drawerItemFwd + fwdOffs * .5f);
		}
		_obj.transform.localEulerAngles = Vector3.up * orientation;
		//_obj.transform.LookAt(_obj.transform.position + _set[_drawerIndex].transform.forward, Vector3.up);
		_obj.SetLayerRecursively(c_useLocalCamera ? 0 : GameManager.c_layerDesignTable);
		++m_nextDrawerItemPos[_drawerIndex];
		int rowsUsed = m_nextDrawerItemPos[_drawerIndex];
		if (_twoPerRow) rowsUsed = (rowsUsed + 1) / 2;
		--rowsUsed;
		_set[_drawerIndex].m_maxOpenFraction = (rowsUsed * -c_drawerItemFwd) * .01f;
	}

	void PopulateDrawerSet(DTDragDrawer[] _set = null, string _cat = null, string[] _labels = null)
	{
		if (_cat == null) _cat = CategoryID; 
		var category = _cat;
		if (IsInDesignMode) {
			var toAdd = new List<NGBlockInfo>[_labels?.Length ?? 4];
			for (int i = 0; i < toAdd.Length; ++i) toAdd[i] = new List<NGBlockInfo>();
			if (false) {
				/*foreach (var p in s_tutorialTeddyProductBlockUnlocks) {
					var blk = NGBlockInfo.GetInfo(p);
					if (blk == null) continue;
					toAdd[blk.m_drawer].Add(blk);
				}*/
			} else {
				bool allUnlocked = DEBUG_IsAllUnlocked;
				foreach (var kvp in NGBlockInfo.s_allBlocks)
				{
					var p = kvp.Key;
					var blk = kvp.Value;
					if (allUnlocked || GameManager.IsUnlocked(p))
					{
						var infos = MADrawerInfo.GetInfos(blk.m_mADrawerInfos);
						foreach (var info in infos)
						{
							if (info.m_drawerName == category)
							{
								int index = Array.IndexOf(_labels, info.m_drawerTitle);
								if (index == -1) index = 0;
								toAdd[index].Add(blk);
								break;
							}
						}
					}
				}
			}
			for (int drawer = 0; drawer < toAdd.Length; ++drawer)
				toAdd[drawer].Sort((_a, _b) => _a.m_drawOrderPriority - _b.m_drawOrderPriority);
			m_nextDrawerItemPos = new int[toAdd.Length];
			for (int drawer = 0; drawer < toAdd.Length; ++drawer)
			{
				int existingCount = _set[drawer].m_contents.transform.childCount;
				bool contentsDifferent = toAdd[drawer].Count != existingCount;
				for (int o = 0; !contentsDifferent && o < existingCount; ++o)
				{
					var item = _set[drawer].m_contents.transform.GetChild(o);
					var block = item.GetComponentInChildren<ManagedBlock>();
					if (block.m_blockID != toAdd[drawer][o].m_prefabName)
						contentsDifferent = true;
				}
				if (!contentsDifferent) continue;
				m_nextDrawerItemPos[drawer] = 0;
				_set[drawer].m_contents.transform.DestroyChildren();

				for (int i = 0; i < toAdd[drawer].Count; ++i)
				{
					var blk = toAdd[drawer][i];
					var p = blk.m_prefabName;
					var scale = DrawerScale(blk) * s_catScale;
					var o = ManagedBlock.Create(p, scale, DrawerCamera, _o =>
					{
						SetDesignInPlaceMaterialParameters(_o);
					});
					AddOwnedCountObject(_set, o.gameObject, drawer, blk);
					AddBuffIcon(_set, o.gameObject, drawer, 1.2f);
					AddRarityObject(o.gameObject, p);
					AddToDrawer(o.gameObject, drawer, true, _set);
				}
			}
		} else {
			// populate price/tag/product items
			// string m_selectedProductLine = ProductLineManager.OwnedProductLines[0];
			// NGProductManager.NGProductInfo productLineData = NGProductManager.NGProductInfo.GetProductInfo(m_selectedProductLine);
			// not used anymore, use rarity instead
			// var adjectives = productLineData.Adjectives;
			// for (int i = 0; i < adjectives.Count; i++) {
			// 	GenerateProductLabelTag(adjectives[i], true);
			// }
			// if (adjectives.Count == 0) GenerateProductLabelTag("Basic", true);
			//
			// for (int j = 0; j < 2; ++j) {
			// 	int mod = j * 25;
			// 	GenerateProductLabelPrice(mod.ToString(), true);
			// }
		}
	}
	
	
		const float c_maxDrawerOpenBeforeCollision = .5f;
	private float CheckDesiredDrawerOpen(string _cat, int _drawerIndex, float _desiredOpen)
	{
		if (m_designInPlaceDrawers.TryGetValue(_cat, out var drawers) == false) return _desiredOpen;
		var catIndex = m_totalCategoryList.IndexOf(_cat);
		for (int i = -1; i <= 1; i += 2)
		{
			var neighbour = (catIndex + i + m_totalCategoryList.Count) % m_totalCategoryList.Count;
			var neighbourStr = m_totalCategoryList[neighbour];
			if (m_designInPlaceDrawers.TryGetValue(neighbourStr, out var neighbourDrawers))
			{
				var dy = (drawers.m_visibilityTarget * drawers.VisibilityRaise - neighbourDrawers.m_visibilityTarget * neighbourDrawers.VisibilityRaise) / DrawerSetContainer.c_drawerHeight;
				for (int j = -1; j <= 1; j += 2)
				{
					var ndrawer = Mathf.FloorToInt(_drawerIndex + .5f + .5f * j - dy);
					if (ndrawer >= 0 && ndrawer < neighbourDrawers.m_drawers.Length)
					{
						float pos = neighbourDrawers.m_drawers[ndrawer].Pos;
						if (pos > c_maxDrawerOpenBeforeCollision)
							_desiredOpen = Mathf.Min(_desiredOpen, c_maxDrawerOpenBeforeCollision);
					}
				}
			}
		}
		return _desiredOpen;
	}
	
	void UpdateDesignInPlaceCatLabels()
	{
		var liveCats = new HashSet<string>();
		for (int i = 0; i < m_designInPlaceCatLabels.Count; ++i)
		{
			var cat = m_designInPlaceCatLabels[i];
			var label = cat.GetComponentInChildren<TMPro.TextMeshProUGUI>();
			//int index = (m_currentDrawerSet + m_totalCategoryList.Count - 2 + i) % m_totalCategoryList.Count;
			int index = m_currentDrawerSet - 2 + i;
			var catStr = index < 0 || index > m_totalCategoryList.Count - 1 ? "" : m_totalCategoryList[index];
			label.text = catStr;
			if (m_designInPlaceDrawers.TryGetValue(catStr, out var drawers))
				drawers.Parent(cat.transform);
			liveCats.Add(catStr);

			var catRnd = cat.transform.GetChild(0).GetComponent<MeshRenderer>();
			for (int m = 0; m < catRnd.materials.Length; ++m)
			{
				var catBackgroundMat = catRnd.materials[m];
				Decoration.SetTintProperty(catBackgroundMat, 0, index < m_categoryList.Count ? Color.clear : Color.grey);
				label.color = index < m_categoryList.Count ? Color.white : new Color(.65f, .65f, .65f);
			}
		}
		var deadCats = new List<string>();
		foreach (var kvp in m_designInPlaceDrawers)
		{
			if (liveCats.Contains(kvp.Key) == false)
			{
				kvp.Value.StartDestroy(true);
				deadCats.Add(kvp.Key);
			}
		}
		foreach (var rem in deadCats)
			m_designInPlaceDrawers.Remove(rem);
	}
	
	public static bool ValidToShow(MADrawerInfo _info)
	{
		if (_info == null) return false;
		bool debugUnlock = Me.DEBUG_IsAllUnlocked;
		foreach (var block in NGBlockInfo.s_allBlocks)
			if (block.Value.m_mADrawerInfos.Contains(_info.m_drawerIndex))
				if (GameManager.IsUnlocked(block.Key) || debugUnlock)
					return true;
		return false;
	}
	public static string[] GetDrawerLabels(string _category)
	{
		if (string.IsNullOrEmpty(_category)) return new [] { "" };
		if (_category == c_decorationCategory)
		{
			var list = new List<string>();
			if (MAUnlocks.CanUsePaints) list.Add("Paints");
			if (MAUnlocks.CanUsePatterns) list.Add("Patterns");
			if (MAUnlocks.CanUseStickers) list.Add("Runes");
			if (MAUnlocks.CanUsePaints || MAUnlocks.CanUsePatterns || MAUnlocks.CanUseStickers) list.Add("Eraser");
			return list.ToArray();
		}
		int count = 0;
		List<(string, int)> labels = new();
		foreach (var info in MADrawerInfo.s_drawerInfos)
		{
			if (info.m_drawerName == _category && ValidToShow(info))
			{
				labels.Add((info.m_drawerTitle, info.m_number));
				Me.AddFinalLabel(info.m_drawerTitle, info.m_displayTitle);
			}
		}
		labels.Sort((a, b) => a.Item2 - b.Item2);
		var labelsOnly = new string[labels.Count];
		for (int i = 0; i < labels.Count; ++i)
			labelsOnly[i] = labels[i].Item1;
		return labelsOnly;
	}

	private DTDragDrawer m_unlockShelf = null;
	public void ShowUnlockShelf()
	{
		if (m_unlockShelf != null) return;
		CreateCatHolder();
		DesignHarness.Me.SetupLights(false);
		var copyFrom = m_newStyleDrawerPrefab;
		var shelf = Instantiate(copyFrom, m_designInPlaceCatHolder.transform);
		shelf.m_sliderOffset = Vector3.zero;
		shelf.transform.localPosition = Vector3.zero;
		shelf.transform.localEulerAngles = Vector3.up * 180;
		shelf.m_allowClick = false;
		shelf.m_checkMaxOpen = _ => 0;
		SetDesignInPlaceMaterialParameters(shelf.gameObject, true);
		m_unlockShelf = shelf;
	}

	public void HideUnlockShelf()
	{
		DestroyCatHolder();
		m_unlockShelf = null;
		m_designInPlaceInterface.SetActive(false);
	}
	
	public bool IsShowingUnlockShelf => m_unlockShelf != null;

	private const float c_timeToShowShelf = .25f;
	private const float c_timeToDrop = 2;
	private const float c_timeToStay = .5f;
	private const float c_timeToRemove = .25f;
	private const float c_totalStagger = c_timeToDrop * .2f;
	private const float c_distanceToDrop = 3;
	private const int c_maxItemsInShelf = 7;
	private const float c_shelfBlockScale = 0.175f;
	private List<(string, Vector3)> m_unlockIDsToShow = new();
	private List<GameObject> m_unlockShelfItems = new();
	private float m_unlockShelfStartTime = 0;
	public void AddToUnlockShelf(string _id, Vector3 _v)
	{
		if (m_unlockShelfItems.Count >= c_maxItemsInShelf)
		{
			m_unlockIDsToShow.Add((_id, _v));
			return;
		}
		if (m_unlockShelfItems.Count == 0) m_unlockShelfStartTime = Time.time;
		ShowUnlockShelf();

		int index = m_unlockShelfItems.Count + 1;

		var blk = NGBlockInfo.GetInfo(_id);
		var p = blk.m_prefabName;
		var holder = ManagedBlock.Create(p, 1, DrawerCamera, _o => {
			SetDesignInPlaceMaterialParameters(_o);
			_o.GetComponent<Block>()?.m_visualSwitcher?.SwitchTo(0);
		});
		var orientation = 90;
		var sideOffs = .1f;
		var fwdOffs = c_newDrawerOffset;
		var pos = (index / 2) * ((index & 1) * 2 - 1);
		holder.transform.SetParent(m_unlockShelf.m_contents.transform);
		holder.transform.localPosition = new Vector3(.5f * c_drawerItemSide + sideOffs, c_drawerItemUp + c_distanceToDrop * 3, pos * c_drawerItemFwd + fwdOffs * .5f);
		holder.transform.localEulerAngles = Vector3.up * orientation;
		holder.transform.localScale = Vector3.one * c_shelfBlockScale;
		m_unlockShelfItems.Add(holder.gameObject);
	}
	
	public AnimationCurve m_bounceAnimation;

	private void UpdateUnlockShelf()
	{
		if (m_unlockShelfItems.Count == 0) return;
		
		var shelfTimeElapsed = Time.time - m_unlockShelfStartTime;
		if (shelfTimeElapsed < c_timeToShowShelf)
		{
			float tShelf = Mathf.Clamp01(shelfTimeElapsed / c_timeToShowShelf);
			m_designInPlaceCatHolder.transform.localPosition = Vector3.Lerp(s_catOffPos, s_catOnPos, tShelf);
		}
		else if (shelfTimeElapsed < c_timeToShowShelf + c_timeToDrop)
		{
			m_designInPlaceCatHolder.transform.localPosition = s_catOnPos;
			
			for (int i = 0; i < m_unlockShelfItems.Count; ++i)
			{
				float thisStagger = i * c_totalStagger / m_unlockShelfItems.Count;
				var t = Mathf.Clamp01((shelfTimeElapsed - thisStagger - c_timeToShowShelf) / (c_timeToDrop - c_totalStagger));
				var ty = m_bounceAnimation.Evaluate(t) * c_distanceToDrop;
				var obj = m_unlockShelfItems[i];
				if (obj == null) continue;
				var lPos = obj.transform.localPosition;
				lPos.y = c_drawerItemUp + c_distanceToDrop * ty;
				obj.transform.localPosition = lPos;
				obj.transform.localEulerAngles = Vector3.up * (90 + 360 * t);
			}
		}
		else if (shelfTimeElapsed > c_timeToShowShelf + c_timeToDrop + c_timeToStay)
		{
			float tShelf = Mathf.Clamp01((shelfTimeElapsed - c_timeToShowShelf - c_timeToDrop - c_timeToStay) / c_timeToRemove);
			m_designInPlaceCatHolder.transform.localPosition = Vector3.Lerp(s_catOnPos, s_catOffPos, tShelf);

			if (shelfTimeElapsed > c_timeToShowShelf + c_timeToDrop + c_timeToStay + c_timeToRemove)
			{
				foreach (var obj in m_unlockShelfItems)
				{
					if (obj == null) continue;
					Destroy(obj);
				}
				m_unlockShelfItems.Clear();
				if (m_unlockIDsToShow.Count == 0)
				{
					HideUnlockShelf();
				}
				else
				{
					while (m_unlockShelfItems.Count < c_maxItemsInShelf && m_unlockIDsToShow.Count > 0)
					{
						var id = m_unlockIDsToShow[0];
						m_unlockIDsToShow.RemoveAt(0);
						AddToUnlockShelf(id.Item1, id.Item2);
					}
				}
			}
		}
	}
	
	public DTDragDrawer m_newStyleDrawerPrefab;

	const string c_decorationCategory = "Decorations";
	public class DrawerSetContainer
	{
		public DTDragDrawer[] m_drawers;
		public float m_visibility, m_visibilityTarget;
		public string m_category;
		
		public DrawerSetContainer(string _cat, Transform _parent, float _target = 1, Vector3 _dragOffset = default)
		{
			m_category = _cat;
			var drawerHolder = _parent.Find("DrawerHolder");
			var copyFrom = Me.m_newStyleDrawerPrefab;
			bool isDecorations = m_category == c_decorationCategory;
			var labels = GetDrawerLabels(m_category);
			m_drawers = new DTDragDrawer[labels.Length];
			for (int i = 0; i < labels.Length; ++i)
			{
				int index = i;
				m_drawers[i] = Instantiate(copyFrom);
				m_drawers[i].m_sliderOffset = _dragOffset;
				m_drawers[i].transform.SetParent(drawerHolder, false);
				m_drawers[i].transform.localPosition = Vector3.zero;
				m_drawers[i].transform.localEulerAngles = Vector3.up * 180;
				m_drawers[i].m_allowClick = false;
				m_drawers[i].m_checkMaxOpen = _desiredOpen => Me.CheckDesiredDrawerOpen(_cat, index, _desiredOpen);
				m_drawers[i].CheckPositionAtStart();
				Me.SetDesignInPlaceMaterialParameters(m_drawers[i].gameObject);
				m_drawers[i].enabled = i < labels.Length;
			}
			if (isDecorations)
				Me.PopulateDecorations(m_drawers);
			else
				Me.PopulateDrawerSet(m_drawers, m_category, labels);
			m_visibility = 0;
			m_visibilityTarget = _target;
			Update();
		}
		
		public void Parent(Transform _parent)
		{
			var drawerHolder = _parent.Find("DrawerHolder");
			for (int i = 0; i < m_drawers.Length; ++i) m_drawers[i].transform.SetParent(drawerHolder, false);
		}

		public void ChangeVisibility(float _change)
		{
			m_visibilityTarget = Mathf.Clamp01(m_visibilityTarget + _change);
		}
		
		public void StartDestroy(bool _instant = false)
		{
			m_visibilityTarget = 0;
			for (int i = 0; i < m_drawers.Length; ++i)
				m_drawers[i].Close();
			if (_instant == true) CompleteDestroy();
		}
		public void CompleteDestroy()
		{
			for (int i = 0; i < m_drawers.Length; ++i)
				Destroy(m_drawers[i].gameObject);
		}

		public const float c_drawerHeight = 1;
		public const float c_drawerSetBaseHeight = 0.1f;
		public float VisibilityRaise => c_drawerHeight * m_drawers.Length;

		public bool Update()
		{
			return false;
		}
	}
	
	
	GameObject m_designInPlaceCatHolder;
	Transform m_designInPlaceInfoSheet;
	const float c_designInPlaceCategoryPush = 24;
	const float c_designInPlaceCategoryTurn = 8;

	float m_designInPlaceCatSpin = 0;
	float m_designInPlaceCatSpinVelocity = 0;

	public void TurnDesignInPlaceCats(float _dx)
	{
		m_designInPlaceCatSpinVelocity = Mathf.Lerp(m_designInPlaceCatSpinVelocity, _dx, .5f.TCLerp());
	}

	public void DragDesignInPlaceCats(GameObject _which, float _dy)
	{
		var catIndex = m_designInPlaceCatLabels.IndexOf(_which);
		//int index = (m_currentDrawerSet + m_totalCategoryList.Count - 2 + catIndex) % m_totalCategoryList.Count;
		int index = m_currentDrawerSet + catIndex - 2;
		if (index < 0 || index >= m_categoryList.Count) return; // locked
		var catStr = m_totalCategoryList[index];
		if (m_designInPlaceDrawers.TryGetValue(catStr, out var drawers))
		{
			drawers.ChangeVisibility(-_dy);
		}
		else if (_dy < 0)
		{
			m_designInPlaceDrawers[catStr] = new DrawerSetContainer(catStr, _which.transform, -_dy);
		}

	}

	void UpdateTurnDesignInPlaceCats()
	{
	}

	public void ToggleDesignInPlaceCat(GameObject _which)
	{
		if (m_designInPlaceExiting) return;
		var catIndex = m_designInPlaceCatLabels.IndexOf(_which);
		//int index = (m_currentDrawerSet + m_totalCategoryList.Count - 2 + catIndex) % m_totalCategoryList.Count;
		int index = m_currentDrawerSet + catIndex - 2;
		if (index < 0 || index >= m_categoryList.Count) return; // locked
		var catStr = m_totalCategoryList[index];
		if (m_designInPlaceDrawers.TryGetValue(catStr, out var drawers))
			drawers.StartDestroy();
		else
			m_designInPlaceDrawers[catStr] = new DrawerSetContainer(catStr, _which.transform);
	}
	
	static Vector3 s_catOnPos => new Vector3(c_newDrawerOffset, -6f, -7f);
	static Vector3 s_catOffPos => new Vector3(c_newDrawerOffset, -6f, -12);
	static Vector3 s_catRot = new Vector3(0, 90, 30);
	public static float s_catScale => 2;
	
	float m_targetCanvasX = 0;
	
	bool UpdateDesignInPlaceCatPosition(bool _moveOn)
	{
		var infoPos = DesignUIManager.Me.m_infoDrawer.Pos;
		var pricePos = DesignUIManager.Me.m_priceDrawer.gameObject.activeSelf ? DesignUIManager.Me.m_priceDrawer.Pos : 0;
		var marketPos = DesignUIManager.Me.m_marketPanel.gameObject.activeSelf ? DesignUIManager.Me.m_marketPanel.Pos : 0;
		var detailsPos = DesignUIManager.Me.m_detailsPanel.gameObject.activeSelf ? DesignUIManager.Me.m_detailsPanel.Pos : 0;
		var rightPos = Mathf.Max(infoPos, marketPos, detailsPos);
		var xOffset = (rightPos - pricePos) * (.3f / 500.0f);
		m_designCameraHorizontalOffset = xOffset;

		bool res = true;
		m_targetCanvasX = _moveOn ? 0 : -7;
		var pos = _moveOn ? s_catOnPos : s_catOffPos;
		pos.y = -9.0f + (m_designInPlaceCatHolder.transform.localEulerAngles.z - 10) * .15f;
		pos.y += GameManager.Me.CurrentCameraOffsetVertical * (2.0f/.3f);
		pos.z += GameManager.Me.CurrentCameraOffsetVertical * (1.2f/.3f);
		if ((m_designInPlaceCatHolder.transform.localPosition - pos).sqrMagnitude > .004f * .004f)
		{
			var oldPos = m_designInPlaceCatHolder.transform.localPosition;
			m_designInPlaceCatHolder.transform.localPosition = Vector3.Lerp(oldPos, pos, .25f.TCLerp());
			res = false;
		}
		var rot = s_catRot;
		rot.z += GameManager.Me.CurrentCameraOffsetVertical * (-6 / .3f);
		m_designInPlaceCatHolder.transform.localEulerAngles = rot;

		UpdateDrawerSelectCardPositions();
		UpdateDeleteMargins();
		CheckShortcuts();
		
		return res;
	}

	void CheckShortcuts()
	{
		bool alt = Input.GetKey(KeyCode.LeftAlt) || Input.GetKey(KeyCode.RightAlt);
		bool control = Input.GetKey(KeyCode.LeftControl) || Input.GetKey(KeyCode.RightControl);
		bool shift = Input.GetKey(KeyCode.LeftShift) || Input.GetKey(KeyCode.RightShift);
		if (!alt)
		{
			for (KeyCode k = KeyCode.Alpha0; k <= KeyCode.Alpha9; ++k)
			{
				if (Input.GetKeyDown(k))
				{
					int id = (((int) k - (int) KeyCode.Alpha0) + 9) % 10;
					RectTransform content;
					if (control) content = DesignUIManager.Me.m_drawerSetScroller.content;
					else if (shift) content = DesignUIManager.Me.m_drawerTypeScroller.content;
					else content = DesignUIManager.Me.m_drawerScroller.content;
					if (id >= 0 && id < content.childCount)
						content.GetChild(id).GetComponent<UnityEngine.UI.Button>().onClick.Invoke();
				}
			}
		}
	}

	float m_deleteBlockIndicatorMargin = .25f;
	float m_deleteBlockIndicatorTarget = 0;
	public void SetDeleteBlockIndicator(float _margin, bool _on)
	{
		if (_margin >= 0)
			m_deleteBlockIndicatorMargin = _margin;
		else
			m_deleteBlockIndicatorMargin *= .75f; // move slightly outwards when switching off
		m_deleteBlockIndicatorTarget = _on ? 1 : 0;
	}
	void UpdateDeleteMargins()
	{
		var virtualW = Screen.width * 1080 / Screen.height;
		float margin = (.5f - m_deleteBlockIndicatorMargin) * virtualW; 
		
		var indicatorLeft = DesignUIManager.Me.m_deleteIndicatorLeft;
		var indicatorRight = DesignUIManager.Me.m_deleteIndicatorRight;
		var indicator = DesignUIManager.Me.m_deleteIndicator;
		indicatorLeft.localPosition = Vector3.Lerp(indicatorLeft.localPosition, new Vector3(-margin, 0, 0), .2f);
		indicatorRight.localPosition = Vector3.Lerp(indicatorRight.localPosition, new Vector3(margin, 0, 0), .2f);
		indicator.alpha = Mathf.Lerp(indicator.alpha, m_deleteBlockIndicatorTarget, .2f);
	}

	int m_currentDrawerIndex = 4;
	void UpdateDesignInPlaceDrawerSets()
	{
		if(m_blockPalette == null || m_blockPalette.m_drawers == null)
			return;
		for (int i = 0; i < m_blockPalette.m_drawers.Length; ++i)
		{
			var drawer = m_blockPalette.m_drawers[i];
			var active = i == m_currentDrawerIndex;
			if (drawer.gameObject.activeSelf != active)
				drawer.gameObject.SetActive(active);
		}
		m_blockPalette.Update();
	}

	static Vector3 s_drawerDragOffset = new Vector3(0, -.25f, 0);

	void SetDrawerPosition()
	{
		if(m_currentDrawerSet >= m_categoryList.Count) return;
		
		var cat = m_categoryList[m_currentDrawerSet];
		
		if(MAMarketInfoSheet.s_current != null) MAMarketInfoSheet.s_current.ScrollToProductLine(cat);
		
		float currentDrawerPos = 0;
		if (!TableState.m_lastDrawerPositionByCategoryAndIndex.TryGetValue($"{cat}_{m_currentDrawerIndex}", out currentDrawerPos))
			currentDrawerPos = 0;
		if(m_blockPalette != null && m_blockPalette.m_drawers.Length > m_currentDrawerIndex)
			m_blockPalette.m_drawers[m_currentDrawerIndex].SetPos(currentDrawerPos, false);
	}

	void SaveDrawerPosition()
	{
		if (m_currentDrawerSet < 0 || m_currentDrawerSet >= m_categoryList.Count) return;
		if (m_currentDrawerIndex < 0 || m_currentDrawerIndex >= m_blockPalette.m_drawers.Length) return;
		var cat = m_categoryList[m_currentDrawerSet];
		TableState.m_lastDrawerPositionByCategoryAndIndex[$"{cat}_{m_currentDrawerIndex}"] = m_blockPalette.m_drawers[m_currentDrawerIndex].Pos;
	}

	void StoreCurrentDrawerState()
	{
		if(m_categoryList.Count <= m_currentDrawerSet)
			return;
			
		var set = m_categoryList[m_currentDrawerSet];
		var labels = GetDrawerLabels(set);
		
		if(labels.Length <= m_currentDrawerIndex)
			return;
			
		var drawer = labels[m_currentDrawerIndex];
		GameManager.Me.m_state.m_designTableDetails.m_drawerSelection = $"{set}|{drawer}";
	}

	void FillDrawerDropdowns(string _cat, string _setDrawerSet = "")
	{
		if(m_designInPlaceCatHolder == null) return;
		if (m_blockPalette != null)
		{
			m_blockPalette.CompleteDestroy();
			m_blockPalette = null;
		}
		m_blockPalette = new DrawerSetContainer(_cat, m_designInPlaceCatHolder.transform, 0, s_drawerDragOffset);
		m_blockPalette.ChangeVisibility(1);
		if (!TableState.m_lastDrawerIndexByCategory.TryGetValue(_cat, out m_currentDrawerIndex))
			m_currentDrawerIndex = 0;

		var content = DesignUIManager.Me.m_drawerScroller.content;

		if (string.IsNullOrEmpty(_cat))
		{
			content.DestroyChildren(true, 1);
			content.GetChild(0).gameObject.SetActive(false);
			return;
		}

		var labels = GetDrawerLabels(_cat);
		if (string.IsNullOrEmpty(_setDrawerSet) == false)
		{
			for (int i = 0; i < labels.Length; ++i)
			{
				if (labels[i] == _setDrawerSet)
				{
					m_currentDrawerIndex = i;
					break;
				}
			}
		}
		SetDrawerPosition();
		StoreCurrentDrawerState();

		bool isDecoration = _cat == c_decorationCategory;
		
		content.DestroyChildren(true, 1);
		if (labels.Length > 0)
		{
			content.GetChild(0).gameObject.SetActive(true);
			for (int i = 0; i < labels.Length; ++i)
			{
				int index = i;
				var info = MADrawerInfo.GetInfo($"{_cat}:{labels[i]}");
				bool inactive = !isDecoration && (info == null || (info.IsUnlocked() == false && DEBUG_IsAllUnlocked == false));
				InstantiateChild(content, i, labels[i], () =>
				{
					SaveDrawerPosition();
					TableState.m_lastDrawerIndexByCategory[_cat] = index;
					m_currentDrawerIndex = index;
					SetDrawerPosition();
					StoreCurrentDrawerState();
					DesignUIManager.Me.m_drawerScroller.GetComponentInChildren<ButtonGroup>().Select(m_currentDrawerIndex);
				}, !inactive);
			}
			DesignUIManager.Me.m_drawerScroller.GetComponentInChildren<ButtonGroup>().Select(m_currentDrawerIndex);
		}
		else
			content.GetChild(0).gameObject.SetActive(false);
	}

	private bool IsValidForProduct()
	{
		if (MAUnlocks.Me.m_weirdDesigns) return true;
		return IsInBuildingMode == false;
	}

	private bool IsValidForBuilding()
	{
		if (MAUnlocks.Me.m_weirdDesigns) return true;
		return IsInBuildingMode;
	}
	
	private bool IsValidForBuildingType(string _cat)
	{
		if (MAUnlocks.Me.m_weirdDesigns) return true;
		return true; // all building types are valid for building mode
	}

	private bool IsValidGender(string _cat)
	{
		int maleProductType = m_currentDesignCategory == NGProductInfo.c_clothingMale || m_currentDesignCategory == NGProductInfo.c_armourMale ? 1 : 0;
		int femaleProductType = m_currentDesignCategory == NGProductInfo.c_clothingFemale || m_currentDesignCategory == NGProductInfo.c_armourFemale ? 2 : 0;
		int productType = maleProductType | femaleProductType;

		int maleDrawerType = _cat == NGProductInfo.c_clothingMale || _cat == NGProductInfo.c_armourMale ? 1 : 0;
		int femaleDrawerType = _cat == NGProductInfo.c_clothingFemale || _cat == NGProductInfo.c_armourFemale ? 2 : 0;
		int drawerType = maleDrawerType | femaleDrawerType;
		
		return productType == 0 || drawerType == 0 || productType == drawerType;
	}

	private bool IsValidForProductType(string _cat)
	{
		if (IsValidGender(_cat) == false) return false;
		if (MAUnlocks.Me.m_weirdDesigns) return true;
		return _cat == m_currentDesignCategory;
	}

	private bool IsValidForType(string _cat)
	{
		if (IsInBuildingMode) return IsValidForBuildingType(_cat);
		return IsValidForProductType(_cat);
	}

	void FillDrawerSetDropdowns(string _setDrawerSet = "")
	{
		if(m_categoryList.Count == 0) return;
		
		if (m_currentDrawerSet < 0) m_currentDrawerSet = 0;
		if (m_currentDrawerSet > m_categoryList.Count - 1) m_currentDrawerSet = m_categoryList.Count - 1;
		
		FillDrawerDropdowns(m_categoryList.Count > 0 ? m_categoryList[m_currentDrawerSet] : "", _setDrawerSet);
		var typeContent = DesignUIManager.Me.m_drawerTypeScroller.content;
		typeContent.DestroyChildren(true, 1);
		InstantiateChild(typeContent, 0, "Products", () => {
			SetDrawerSetType("Product");
			DesignUIManager.Me.m_drawerTypeScroller.GetComponentInChildren<ButtonGroup>().Select(0);
		}, _isEnabled: IsValidForProduct());
		InstantiateChild(typeContent, 1, "Buildings", () => {
			SetDrawerSetType("Building");
			DesignUIManager.Me.m_drawerTypeScroller.GetComponentInChildren<ButtonGroup>().Select(1);
		}, _isEnabled: IsValidForBuilding());
		bool haveTweaks = MAUnlocks.CanUsePaints || MAUnlocks.CanUsePatterns || MAUnlocks.CanUseStickers;
		InstantiateChild(typeContent, 2, "Tweaks", () => {
			SetDrawerSetType("Tweaks");
			DesignUIManager.Me.m_drawerTypeScroller.GetComponentInChildren<ButtonGroup>().Select(2);
		}, haveTweaks);
		int type;
		switch (m_currentDrawerSetType)
		{
			default: case "Product": type = 0; break;
			case "Building": type = 1; break;
			case "Tweak": type = 2; break;
		}
		DesignUIManager.Me.m_drawerTypeScroller.GetComponentInChildren<ButtonGroup>().Select(type);
		
		var content = DesignUIManager.Me.m_drawerSetScroller.content;
		content.DestroyChildren(true, 1);
		content.GetChild(0).gameObject.SetActive(m_categoryList.Count > 0);
		for (int i = 0; i < m_categoryList.Count; ++i)
		{
			int index = i;
			InstantiateChild(content, i, m_categoryList[i], () => {
				SaveDrawerPosition();
				m_currentDrawerSet = index;
				FillDrawerDropdowns(m_categoryList[m_currentDrawerSet]);
				DesignUIManager.Me.m_drawerSetScroller.GetComponentInChildren<ButtonGroup>().Select(m_currentDrawerSet);
			}, _isEnabled:IsValidForType(m_categoryList[i]));
		}
		DesignUIManager.Me.m_drawerSetScroller.GetComponentInChildren<ButtonGroup>().Select(m_currentDrawerSet);
	}

	Dictionary<string, string> m_labelLookup = new ();

	void AddFinalLabel(string _label, string _display)
	{
#if UNITY_EDITOR
		if (m_labelLookup.TryGetValue(_label, out var existing) && existing != _display)
			Debug.LogError($"DesignTable Drawer Label {_label} already exists with value {existing}, trying to overwrite with {_display}");
#endif
		m_labelLookup[_label] = _display;
	}

	string GetFinalLabel(string _label)
	{
		if (m_labelLookup.TryGetValue(_label, out var display))
			return display;
		return _label;
	}

	UnityEngine.UI.Button InstantiateChild(Transform _container, int _index, string _label, Action _cb, bool _isVisible = true, bool _isEnabled = true)
	{
		_label = GetFinalLabel(_label);
		var buttonGO = (_index == 0) ? _container.GetChild(0).gameObject : Instantiate(_container.GetChild(0).gameObject, _container);
		var button = buttonGO.GetComponent<UnityEngine.UI.Button>();
		var label = buttonGO.GetComponentInChildren<TMPro.TextMeshProUGUI>();
		button.onClick.RemoveAllListeners();
		button.onClick.AddListener(() => {
			AudioClipManager.Me.PlayUISound("PlaySound_DesignModeSelectMenuItem");
			_cb();
		});
		var eventTrigger = buttonGO.GetComponent<UnityEngine.EventSystems.EventTrigger>();
		if (eventTrigger == null)
			eventTrigger = buttonGO.AddComponent<UnityEngine.EventSystems.EventTrigger>();
		eventTrigger.triggers = new();
		var entry = new UnityEngine.EventSystems.EventTrigger.Entry() {
			eventID = UnityEngine.EventSystems.EventTriggerType.PointerEnter,
		};
		entry.callback.AddListener((_) => AudioClipManager.Me.PlayUISound("PlaySound_DesignModeHighlightMenuItem"));
		eventTrigger.triggers.Add(entry);
		label.text = _label;
		buttonGO.SetActive(_isVisible);
		buttonGO.GetOrAddComponent<CanvasGroup>().alpha = _isEnabled ? 1 : .5f;
		button.interactable = _isEnabled;
		return button;
	}

	private static float s_visibilityEdge = -.36f;//-.43f @ 3
	public float GetPaletteVisibility(DTDragPalette _item)
	{
		var drawer = _item.GetComponentInParent<DTDragDrawer>();
		if (drawer == null) return 1.0f;
		var pos = drawer.Pos + _item.transform.localPosition.z * _item.transform.parent.lossyScale.x;
		var cut = s_visibilityEdge - m_canvas3DCut * (.07f / 3.0f);
		float c_visibilityEdgeStart = cut;
		float c_visibilityEdgeEnd = c_visibilityEdgeStart + .02f;
		return Mathf.Clamp01((c_visibilityEdgeEnd - pos) / (c_visibilityEdgeEnd - c_visibilityEdgeStart));
	}

	DrawerSetContainer m_blockPalette;

	void DestroyDesignInPlaceDrawerFramework()
	{
		if (m_blockPalette != null)
		{
			m_blockPalette.CompleteDestroy();
			m_blockPalette = null;
		}
		DestroyCatHolder();
		m_designInPlaceCatLabels.Clear();
		m_designInPlaceDrawers.Clear();

		EnableDesignInPlaceUI(false);
	}

	void RefreshPriceResourceButtonLabels()
	{
		DesignUIManager.Me.m_showInfoButton.GetComponent<ButtonGroup>().Select(m_blockLabelMode == BlockLabelMode.Info ? 0 : 1);
		DesignUIManager.Me.m_showHelpersButton.GetComponent<ButtonGroup>().Select(m_blockLabelMode == BlockLabelMode.Helpers ? 0 : 1);
		DesignUIManager.Me.m_showResourceButton.GetComponent<ButtonGroup>().Select(m_blockLabelMode == BlockLabelMode.Material ? 0 : 1);
	}

	static float s_canvasOffset = -.4f;
	float m_canvas3DCut = 0;
	void UpdateDrawerSelectCardPositions()
	{
		if (!IsInDesignInPlace && !m_isInDesignGlobally) return;
		var drawerSelectCanvas = DesignUIManager.Me.m_drawerSetScroller.GetComponentInParent<Canvas>();
		if (drawerSelectCanvas == null) return; // closing but not closed
		var aspectBase = (float) Screen.width / (float) Screen.height - 1.6f;
		float x = -5 * aspectBase + s_canvasOffset + m_targetCanvasX; // based on 1674x1048
		m_canvas3DCut = x;
		if (DesignUIManager.Me.m_drawerScrollersIn3D)
			drawerSelectCanvas.transform.localPosition = new Vector3(Mathf.Lerp(drawerSelectCanvas.transform.localPosition.x, x, .25f), DrawerSelectCanvasY, DrawerSelectCanvasZ);

		if(m_blockPalette != null && m_blockPalette.m_drawers.Length > m_currentDrawerIndex)
		{
			var drawer = m_blockPalette.m_drawers[m_currentDrawerIndex];
			DesignUIManager.Me.UpdateArrows(drawer.Pos > .01f, drawer.Pos < drawer.MaxPos - .01f);
		}

		var drawerHolder = m_designInPlaceCatHolder.transform.GetChild(0);
		drawerHolder.transform.localPosition = Vector3.forward * (aspectBase * -3f);
	}

	void OnBlockLabelModeSelected(BlockLabelMode _mode)
	{
		/*if (_mode == BlockLabelMode.Cost)
		{
			if (m_blockLabelMode == BlockLabelMode.Cost)
				m_blockLabelMode = BlockLabelMode.Value;
			else if (m_blockLabelMode == BlockLabelMode.Value)
				m_blockLabelMode = BlockLabelMode.None;
			else
				m_blockLabelMode = BlockLabelMode.Cost;
		}
		else*/
		
		m_blockLabelMode = m_blockLabelMode == _mode ? BlockLabelMode.None : _mode;
		if (m_blockLabelMode != BlockLabelMode.None)
			AudioClipManager.Me.PlayUISound($"PlaySound_DesignModeShow{_mode}");
		if(m_blockPalette != null)
		{
			for (int i = 0; i < m_blockPalette.m_drawers.Length; ++i)
				RefreshDrawerBlockLabels(m_blockPalette.m_drawers[i]);
		}
		RefreshPriceResourceButtonLabels();
		
		if(m_blockLabelMode == BlockLabelMode.Helpers)
		{
			ToolTipHolder.DisplayAll(ToolTipHolder.ToolTipCategory.DesignTable);
		}
		else if(m_blockLabelMode == BlockLabelMode.None)
		{
			ToolTipHolder.DestroyAll(ToolTipHolder.ToolTipCategory.DesignTable);
		}
	}

	private float DrawerSelectCanvasY => -6f + GameManager.Me.CurrentCameraOffsetVertical * (1.1f/.3f);
	private float DrawerSelectCanvasZ => -7.5f + GameManager.Me.CurrentCameraOffsetVertical * (1.24f / .3f);

	void CreateCatHolder()
	{
		CheckDesignInPlaceInterfaceExists();
		m_designInPlaceInterface.SetActive(true);
		m_designInPlaceCatHolder = new GameObject("DesignInPlaceCatHolder");
		m_designInPlaceCatHolder.transform.SetParent(m_designInPlaceInterface.transform);
		m_designInPlaceCatHolder.transform.localPosition = s_catOffPos;
		m_designInPlaceCatHolder.transform.localEulerAngles = s_catRot;
		m_designInPlaceCatHolder.transform.localScale = Vector3.one * s_catScale;
	}

	private void DestroyCatHolder()
	{
		Destroy(m_designInPlaceCatHolder);
		m_designInPlaceCatHolder = null;
	}

	void CreateDesignInPlaceDrawerFramework(string _setDrawerSet = null)
	{
		CreateCatHolder();

		//DesignUIManager.Me.m_showPriceButton.transform.parent.gameObject.SetActive(!IsInBuildingMode);
		//DesignUIManager.Me.m_showHelpersButton.transform.parent.gameObject.SetActive(IsInBuildingMode);
		DesignUIManager.Me.m_showResourceButton.transform.parent.gameObject.SetActive(false);
		DesignUIManager.Me.m_priceDrawer.gameObject.SetActive(!IsInBuildingMode);
		
		DesignUIManager.Me.ShowDropdowns(true);
		var holder = new GameObject("DrawerHolder");
		holder.transform.SetParent(m_designInPlaceCatHolder.transform);
		holder.transform.localPosition = Vector3.zero;
		holder.transform.localScale = Vector3.one;
		holder.transform.localRotation = Quaternion.identity;
		FillDrawerSetDropdowns(_setDrawerSet);

		DesignUIManager.Me.m_showInfoButton.onClick.RemoveAllListeners();
		DesignUIManager.Me.m_showInfoButton.onClick.AddListener(() => OnBlockLabelModeSelected(BlockLabelMode.Info));
		DesignUIManager.Me.m_showHelpersButton.onClick.RemoveAllListeners();
		DesignUIManager.Me.m_showHelpersButton.onClick.AddListener(() => OnBlockLabelModeSelected(BlockLabelMode.Helpers));
		DesignUIManager.Me.m_showResourceButton.onClick.RemoveAllListeners();
		DesignUIManager.Me.m_showResourceButton.onClick.AddListener(() => OnBlockLabelModeSelected(BlockLabelMode.None));
		RefreshPriceResourceButtonLabels();
		EnableDesignInPlaceUI(true);
	}

	Canvas m_drawerSelectCanvas;
	public void EnableDesignInPlaceUI(bool _enable)
	{
		if (m_drawerSelectCanvas.gameObject.activeSelf != _enable)
			m_drawerSelectCanvas.gameObject.SetActive(_enable);
	}
	
	public void MoveDesignInPlace3DElements()
	{
		if (DesignUIManager.Me.m_drawerScrollersIn3D)
		{
			CheckDesignInPlaceInterfaceExists();
			
			var drawerSelectCanvas = DesignUIManager.Me.m_drawerSetScroller.GetComponentInParent<Canvas>();
			if (drawerSelectCanvas.transform.IsChildOf(m_designInPlaceInterface.transform) == false)
			{
				drawerSelectCanvas.transform.SetParent(m_designInPlaceInterface.transform);
				drawerSelectCanvas.transform.localScale = Vector3.one * .01f;
				drawerSelectCanvas.transform.localEulerAngles = new Vector3(90, 0, 0);
				(drawerSelectCanvas.transform as RectTransform).sizeDelta = new Vector2(1650, 0);
				drawerSelectCanvas.renderMode = RenderMode.WorldSpace;
				drawerSelectCanvas.worldCamera = GameManager.Me.m_camera;
			}
			drawerSelectCanvas.transform.localPosition = new Vector3(-10, DrawerSelectCanvasY, DrawerSelectCanvasZ); // prep for slide-on
			m_drawerSelectCanvas = drawerSelectCanvas;
			m_designInPlaceInterface.gameObject.SetLayerRecursively(GameManager.c_layerDesignHarness);
		}
	}
}
