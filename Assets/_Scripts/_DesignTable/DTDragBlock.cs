using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.EventSystems;

public class DTDragBlock : DragBase {
	
	private bool m_isKinematicBackup = false;
	private List<GameObject> m_snapVisuals = new();
	
	void OnEnable()
	{
		CreateSnaps();
	}

	void Awake() {
		var boxCll = gameObject.GetComponent<BoxCollider>();
		var meshCll = gameObject.GetComponentInChildren<MeshCollider>();
		if (meshCll != null)
		{
			meshCll.enabled = true;
			if (boxCll != null) boxCll.enabled = false;
		}
		else if (boxCll != null && boxCll.enabled)
		{
			var block = GetComponent<Block>();
			var newBoxCll = block.m_toVisuals.gameObject.AddComponent<BoxCollider>();
			newBoxCll.center = boxCll.center;
			newBoxCll.isTrigger = true;
			newBoxCll.size = boxCll.size;
			boxCll.enabled = false;
		}

		m_plantController = GetComponentInParent<PlantController>();
	}
	override public bool CanDrag => DesignTableManager.Me.IsInDesignModeActively && DesignTableManager.Me.m_onboardingRemovePartsAllowed && IsDraggable();
	override public Camera Cam => DesignTableManager.Me.m_designCamera ?? base.Cam;
	override public Vector3 DragPlaneNormal => -Cam.transform.forward.GetXZ().normalized;
	override public Vector3 DragPlaneOrigin => DesignTableManager.Me.m_dragPlaneOrigin;
	const float c_planePull = .25f;
	public bool m_dragPlaneOnPosition = true;
	//override public Vector3 DragPlaneOrigin => m_dragPlaneOnPosition ? transform.position : DesignTableManager.Me.m_turntable.transform.position - Cam.transform.forward * c_planePull;

	protected override bool DragWithoutMove => false;
	
	override public bool AcceptsClicks => true;
	override public bool SupportsLongPress => false;
	
	public bool m_rightClickOnly = false;
	
	private DTPriceVisual m_priceVisual;
	
	public bool m_isFromPalette = false; // whether this block was dragged from the palette/drawers or a design/wild/scattered

	private bool TryShowBlockInfo()
	{
		var block = GetBlock(); // normal drawer item
		if (block != null) {
			DesignUtilities.ShowBlockInfo(gameObject, block.m_blockInfoID);
			return true;
		}
		return false;
	}
	
	private bool TrySetNewBuildingFocus()
	{
		var rcb = GetComponentInParent<NGCommanderBase>();
		if (rcb != null && rcb.IsLocked == false)
		{
			if (MAGameInterface.CanLeaveFocusDesignMode() && DesignTableManager.Me.m_designGloballyFocusBuilding != rcb)
			{
				DesignTableManager.Me.TrySetDesignGloballyFocus(rcb);
				return true;
			}
		}
		return false;
	}
	
	override public void OnClick() {
		if (IsOKToClick() == false) return;
		if (IsRightButton)
		{
			if (DesignTableManager.Me.m_isInDesignGlobally)
			{
				if(TrySetNewBuildingFocus()) return;
			}
			
			if(TryShowBlockInfo()) return;
		}

		var btn = PointerXRayButton();
		if(btn != null)
        {
			var _eventData = new PointerEventData(FindObjectOfType<EventSystem>());
			btn?.OnPointerDown(_eventData);
			btn?.OnPointerUp(_eventData);
			return;
		}

		// var block = GetBlock(); // normal drawer item
		// if (block != null) {
		// 	DesignUtilities.ShowBlockInfo(gameObject, block.m_blockInfoID);
		// }
	}

	bool IsInDistrict
	{
		get
		{
			var wild = GetComponentInParent<MAWildBlock>();
			if (wild == null) return true;
			return wild.m_wildBlockState.m_hasEverBeenUnlocked || DistrictManager.Me.IsWithinDistrictBounds(transform.position, true);
		}
	}

	bool IsDraggable() {
		return !DesignTableManager.IsBlending && DesignTableManager.Me.IsBlockEditable(GetBlock().BlockID) && !DesignTableManager.IsRestoring && IsInDistrict;
	}
	
	List<GameObject> m_grabbed;
	public bool IsDraggingGrabbed => m_grabbed != null && m_grabbed.Count > 0;

	private bool m_isLastReleaseValid = false;
	public bool IsLastReleaseValid => m_isLastReleaseValid;

	private DragObjectState m_objectStateOnDragStart = new DragObjectState();
	public DragObjectState ObjectStateOnDragStart => m_objectStateOnDragStart;
	
	private bool m_canDelete = true;

	private bool IsTempHidden()
	{
		var bld = GetComponentInParent<MABuilding>();
		if (bld == null) return false;
		return bld.IsTemporarilyHidden;
	}
	private PlantController m_plantController = null;
	private bool IsOKToDrag()
	{
		if (DesignTableManager.Me.m_lockBlocksOnBuildings && GetComponentInParent<NGCommanderBase>() != null) return false;
		if(GameManager.Me.CameraExceededMaxInteractionDistance()) return false;
		if (IsTempHidden()) return false;
		if (m_plantController == null) return true;
		return m_plantController.IsDraggable;
	}

	private bool IsOKToClick()
	{
		if(GameManager.Me.CameraExceededMaxInteractionDistance()) return false;
		if (IsTempHidden()) return false;
		if (m_plantController == null) return true;
		return m_plantController.IsClickable;
	}
	
	public static Block m_lastGrabbedBlock;
	override public void OnDragStart()
	{
		if (m_rightClickOnly) return;
		Block block = GetBlock();
		m_lastGrabbedBlock = block;
		if (MAGameInterface.CheckSpecialPickupBlock(block) || IsOKToDrag() == false)
		{
			m_grabbed = null;
			EndDrag();
			return;
		}
		
		// Always drag previous designs in whole
		var parentScatterBlock = transform.parent.GetComponentInParent<DesignTableManager.ScatteredBlockNoSave>();
		if(parentScatterBlock != null)
		{
			TransferDrag(parentScatterBlock.GetComponent<DragBase>());
			return;
		}
		
		var hinges = GetComponentsInChildren<SnapHinge>();
		foreach(var hinge in hinges)
		{
			hinge.m_isOnAmmoHolder = false;
		}
		
		DesignTableManager.Me.StartDesignSingly(gameObject);
		m_objectStateOnDragStart = new DragObjectState();
		m_objectStateOnDragStart.m_GlobalPosition = gameObject.transform.position;
		m_objectStateOnDragStart.m_GlobalRotation = gameObject.transform.rotation;
		
		if (block != null)
		{
			NGBlockInfo blockInfo = NGBlockInfo.GetInfo(block.BlockID);
			m_objectStateOnDragStart.m_name = blockInfo.m_displayName;
		}
		
		DesignTableManager.SetWorldTriPlanar(gameObject, false);
		(m_grabbed, m_objectStateOnDragStart.m_isFromTable) = DesignTableManager.Me.Grab(gameObject);
		foreach (var g in m_grabbed)
			foreach (var gdrag in g.GetComponentsInChildren<GlobuleDrag>())
				gdrag.m_swingTop = false;
		
		float priceChange = 0;
		var includedBlock = new HashSet<Block>();
		foreach (var grab in m_grabbed)
		{
			foreach (var blk in grab.GetComponentsInChildren<Block>())
			{
				if (includedBlock.Add(blk) == false) continue;
				var info = NGBlockInfo.GetInfo(blk.BlockID);
				if (m_isFromPalette)
					priceChange += NGDesignInterface.Get().GetPriceOfBlock(info);
				else
					priceChange -= NGDesignInterface.Get().GetPriceOfRemovingBlock(info);
			}
		}
		if (priceChange * priceChange > .001f * .001f)
			m_priceVisual = DTPriceVisual.Create(priceChange, m_isFromPalette == false, transform);
		
		DesignTableManager.Me.m_dragInProgress = true;
		m_holdOffset = Vector3.zero;

		EnableTurntableClip(false);

		KeyboardShortcutManager.Me.PushShortcuts(KeyboardShortcutManager.EShortcutType.DragBlock);
		
		DesignTableManager.Me.PlayPickupAudio(gameObject);
		
		m_canDelete = DesignTableManager.Me.m_designSingly == false;

		foreach (var g in m_grabbed)
			if (g.GetComponent<Block>().DoNotAllowDeleteAny || g.GetComponent<BuildHelper>() != null || g.GetComponent<DesignTableManager.ScatteredBlockNoSave>())
				m_canDelete = false;
		if (m_canDelete)
			DesignTableManager.Me.SetDeleteBlockIndicator(DesignTableManager.Me.m_isInDesignGlobally ? c_leftDragBoundFractionDG : c_leftDragBoundFraction, true);
	}

	private Vector3 m_holdOffset = Vector3.zero;
	float m_currentScaleFactor = 0, m_targetScaleFactor = 1; // 0 = drawer scale, 1 = design scale
	const float c_leftDragBoundFraction = .25f, c_rightDragBoundFraction = .75f;
	const float c_leftDragBoundFractionDG = .1666f, c_rightDragBoundFractionDG = .8333f;
	const float c_bottomDragBoundFraction = .2f;

	private Transform m_currentTargetObject = null;
	public void ClearLastSnapped() => m_currentTargetObject = null;

	private bool InputPosIsValid {
		get {
			if (m_canDelete == false) return true;
			var x = InputPosition.x / Screen.width;
			var y = InputPosition.y / Screen.height;
			if (DesignTableManager.Me.m_isInDesignGlobally)
				return x > c_leftDragBoundFractionDG && x < c_rightDragBoundFractionDG && y > c_bottomDragBoundFraction;
			return x > c_leftDragBoundFraction && x < c_rightDragBoundFraction && y > c_bottomDragBoundFraction;
		}
	}
	static bool IsBuilding => DesignTableManager.Me.IsInBuildingMode;
	private bool m_isValidSnap = true;
	override public void OnDragUpdate(Vector3 _dragPoint, Vector3 _totalScreenDrag) {
		if (m_rightClickOnly) return;
		
		var pos = DesignTableManager.Me.PositionInFront(RayAtInputPosition(true), m_grabbed) + m_holdOffset * DesignTableManager.CurrentBlockBuildScale;;
		var blockOffset = m_cachedBlock.BlockOffset;
		if (blockOffset == null)
			transform.position = pos;
		else
			transform.position += pos - blockOffset.position;

		if (DesignTableManager.Me.m_isOverHarness)
			transform.rotation = DesignTableManager.Me.BlockCage.CurrentRotation;
		
		SnapHinge bestSnap = null, bestHeld = null;
		var inputPosIsValid = InputPosIsValid;
		bool isValid = false;
		bool nullAction = false;
		if (inputPosIsValid) {
			DesignTableManager.Me.SetPositiveMaterial(m_grabbed, true);
			(bestSnap, bestHeld, isValid) = DesignTableManager.Me.SnapBlock(gameObject, m_grabbed, false, ref m_currentTargetObject, out nullAction);
			m_targetScaleFactor = 1;
		}
		if (!isValid) {
			DesignTableManager.Me.SetPositiveMaterial(m_grabbed, false);
			DesignTableManager.Me.RemoveSnapLine();
			//m_targetScaleFactor = 0;
		}
		m_isValidSnap = isValid;
		
		if (m_priceVisual != null)
			m_priceVisual.SetToBeDeleted(isValid == false || nullAction);

		if (bestSnap != null) {
			float rotate = CalculateRotation();

			if (rotate * rotate > 0) {
				var fwd = m_grabbed[0].transform.forward;
				var up = m_grabbed[0].transform.up;
				var axis = IsBuilding ? Vector3.up : bestSnap.transform.forward;
				var oldPivotPos = bestHeld.transform.position;
				fwd = fwd.RotateAbout(axis, rotate);
				up = up.RotateAbout(axis, rotate);
				m_grabbed[0].transform.LookAt(m_grabbed[0].transform.position + fwd, up);
				if (IsBuilding == false)
				{
					var offset = oldPivotPos - bestHeld.transform.position;
					m_grabbed[0].transform.position += offset;
					m_holdOffset += offset;
				}
			}
		}
	}

	private float m_leftArrowDownTime, m_rightArrowDownTime;

	private float GetKeyRotation(KeyCode _key, ref float _downTime)
	{
		const float c_heldKeypressRotateDelay = .2f;
		if (Input.GetKeyDown(_key))
		{
			_downTime = Time.time;
		}
		else if (Input.GetKey(_key))
		{
			if (Time.time - _downTime >= c_heldKeypressRotateDelay)
				return NGManager.Me.m_heldKeypressRotateAmount * .1f * Time.deltaTime;
		}
		else if (Input.GetKeyUp(_key))
		{
			if (Time.time - _downTime < c_heldKeypressRotateDelay)
				return NGManager.Me.m_singleKeypressRotateAmount;
		}
		return 0;
	}

	private float CalculateRotation()
    {
		float rot = 0;

		if (EKeyboardFunction.Cancel.IsHeld())
		{
			if (DesignTableManager.Me.m_isInDesignGlobally && m_currentTargetObject != null)
				m_grabbed[0].transform.rotation = m_currentTargetObject.rotation;
			else
				m_grabbed[0].transform.rotation = DesignTableManager.Me.TurntableVisual.transform.rotation;
			m_holdOffset = Vector3.zero;
		}

		if (IsBuilding)
		{
			rot = GetKeyRotation(KeyCode.LeftArrow, ref m_leftArrowDownTime) - GetKeyRotation(KeyCode.RightArrow, ref m_rightArrowDownTime);

			//if (Input.GetKeyDown(KeyCode.LeftArrow))
			//	rot = NGManager.Me.m_singleKeypressRotateAmount;

			//if (Input.GetKeyDown(KeyCode.RightArrow))
			//	rot = -NGManager.Me.m_singleKeypressRotateAmount;
		}
		else
		{
			if (Input.GetKey(KeyCode.LeftArrow))
				rot = NGManager.Me.m_heldKeypressRotateAmount;

			if (Input.GetKey(KeyCode.RightArrow))
				rot = -NGManager.Me.m_heldKeypressRotateAmount;

			rot *= Time.deltaTime;
		}
		return rot * Mathf.Deg2Rad;
	}

	override public void OnDragEnd(bool _undo) {
		if (m_priceVisual != null) m_priceVisual.Finish();
		m_priceVisual = null;
		m_isFromPalette = false;
		
		if (m_rightClickOnly) return;
		if (m_grabbed == null) return;
		
		foreach (var g in m_grabbed)
			foreach (var gdrag in g.GetComponentsInChildren<GlobuleDrag>())
				gdrag.m_swingTop = true;

		DesignTableManager.Me.SetDeleteBlockIndicator(-1, false);

		KeyboardShortcutManager.Me.PopShortcuts();

		m_targetScaleFactor = 1;
		var inputPosIsValid = InputPosIsValid;
		DesignTableManager.Me.m_dragInProgress = false;
		bool isValid = false;
		DesignTableManager.Me.SetPositiveMaterial(m_grabbed, true);
		EnableTurntableClip(true);
		DesignTableManager.SetWorldTriPlanar(gameObject, DesignTableManager.Me.IsInDesignInPlace == false);
		if (inputPosIsValid)
		{
			// reset scale instantly
			m_currentScaleFactor = 1;
			Update();
			(_, _, isValid) = DesignTableManager.Me.SnapBlock(gameObject, m_grabbed, true, ref m_currentTargetObject, out _);
		}

		m_isLastReleaseValid = isValid;

		if (!isValid)
		{
			if (m_grabbed != null)
			{
				DesignTableManager.Me.PlayDeleteAudio(gameObject);
				
				foreach (var g in m_grabbed)
				{
					DesignTableManager.ReturnBlock(g);
				}

				DesignTableManager.Me.CullOrphanBlocks();
			}

			if (m_objectStateOnDragStart.m_isFromTable)
			{
				DesignTableManager.Me.RefreshSave();
			}
			else
			{
				DesignTableManager.Me.ClearGrabbedBlock();
			}
			DesignTableManager.Me.EndDesignSingly();
		}
		DesignTableManager.Me.RemoveSnapLine();
		m_grabbed = null;
	}

    public DesignTableButtonScript PointerXRayButton()
    {
		Ray ray = Camera.main.ScreenPointToRay(InputPosition);
		RaycastHit[] hits;
		hits = Physics.RaycastAll(ray);
        for (int i = 0; i < hits.Length; i++)
        {
			if (hits[i].transform.GetComponentInParent<DesignTableButtonScript>() != null)
				return hits[i].transform.GetComponentInParent<DesignTableButtonScript>();
        }

		return null;
	}

    void OnDisable()
	{
		if (Utility.IsShuttingDown) return;
		if (IsHeld) // in case the GO gets destroyed before ending the drag cleanly
			OnDragEnd(true);
		if (transform.IsChildOf(DesignTableManager.Me.m_wildBlockHolder) == false)
		{
			Rigidbody body = gameObject.GetComponent<Rigidbody>();
			if(body) body.isKinematic = m_isKinematicBackup;
		}
		
		foreach(var visual in m_snapVisuals)
		{
			Destroy(visual);
		}
		m_snapVisuals.Clear();
		base.OnDisable();
	}
	
	void EnableTurntableClip(bool _enable)
	{
		if (DesignTableManager.IsDesignInPlace) return;
		foreach (var r in GetComponentsInChildren<Renderer>(true))
		{
			foreach (var m in r.materials)
			{
				if (_enable)
				{
					m.EnableKeyword("_CIRCLECLIP");
					if (m.renderQueue <= (int) UnityEngine.Rendering.RenderQueue.GeometryLast)
						m.SetInt("_Cull", (int)UnityEngine.Rendering.CullMode.Off);
				}
				else
				{
					m.DisableKeyword("_CIRCLECLIP");
					m.SetInt("_Cull", (int)UnityEngine.Rendering.CullMode.Back);
				}
			}
		}
	}
	
	private Block m_cachedBlock = null;
	private Block GetBlock()
	{
		if (m_cachedBlock == null)
			m_cachedBlock = GetComponent<Block>();
		return m_cachedBlock;
	}
	
	private bool m_wasInDesignInPlace = false;
	void Update() {
		var isInDesignInPlace = DesignTableManager.IsDesignInPlace; 
		if (isInDesignInPlace == false && m_wasInDesignInPlace == false) return;
		m_wasInDesignInPlace = isInDesignInPlace;
		m_currentScaleFactor = Mathf.Lerp(m_currentScaleFactor, m_targetScaleFactor, .15f);
		AnimateHinges(DesignTableManager.Me.m_dragInProgress ? m_currentScaleFactor : 0.0f);
		SetScale(m_currentScaleFactor);
	}
	float m_cachedDrawerScale = -1;
	void SetScale(float _factor) {
		var blockData = GetBlock();
		if (m_cachedDrawerScale < 0) m_cachedDrawerScale = ManagedBlock.GetDrawerScale(blockData.BlockID, gameObject, false);
		if (_factor < 0) _factor = m_currentScaleFactor;
		var info = NGBlockInfo.GetInfo(blockData.BlockID);
		bool isWildBlock = transform.IsChildOf(DesignTableManager.Me.m_wildBlockHolder);
		float inDesignScale = DesignTableManager.DesignScale(info, isWildBlock);
		bool isInCage = DesignTableManager.Me.IsInCage(transform);
		if ((DesignTableManager.Me.m_isOverHarness && DesignTableManager.Me.IsGrabbed(gameObject)) || isInCage)
			inDesignScale *= DesignTableManager.OverHarnessScaleBase;
		float inDrawerScale = m_cachedDrawerScale;//DesignTableManager.DrawerScale(info);
		float currentScale = Mathf.Lerp(inDrawerScale, inDesignScale, _factor);
		if (isInCage)
		{
			transform.localScale = Vector3.one * (currentScale / transform.parent.lossyScale.x);
			blockData.m_toVisuals.localScale = Vector3.one;
			blockData.m_toHinges.localScale = Vector3.one;
		}
		else
		{
			if (transform.localScale.AlmostEquals(Vector3.one) == false)
				transform.localScale = Vector3.one;
			var parent = blockData.m_toVisuals.parent;
			var visualScale = new Vector3(currentScale / parent.lossyScale.x, currentScale / parent.lossyScale.y, currentScale / parent.lossyScale.z);
			var hingeScale = new Vector3(inDesignScale / parent.lossyScale.x, inDesignScale / parent.lossyScale.y, inDesignScale / parent.lossyScale.z);
			if (blockData.m_toVisuals.localScale.AlmostEquals(visualScale) == false)
				blockData.m_toVisuals.localScale = visualScale;
			if (blockData.m_toHinges.localScale.AlmostEquals(hingeScale) == false)
				blockData.m_toHinges.localScale = hingeScale;
		}
	}
	public DTDragBlock InitialiseScale(float _factor) {
		m_targetScaleFactor = m_currentScaleFactor = _factor;
		SetScale(_factor);
		return this;
	}

	Dictionary<Transform, (bool, Transform)> m_hingeStateCache = new();
	public void ResetCache()
	{
		m_hingeStateCache.Clear();
	}

	private (bool, Transform) GetHingeState(Transform _t, Block _block)
	{
		if (m_hingeStateCache.TryGetValue(_t, out var state))
			return state;
		bool ignoreExtent = NGBlockInfo.GetInfo(_block.m_blockInfoID)?.m_extentType == BuildingPlacementManager.c_extentTypeNone;
		bool isInvalid = DesignTableManager.Me.CheckForBlockers(_t, ignoreExtent) || DesignTableManager.Me.IsHingeUsed(_block.transform, _t);
		state = (isInvalid, _t.GetComponentInChildren<Hinge>(true)?.transform);
		m_hingeStateCache[_t] = state;
		return state;
	}

	float m_snapScaleIn = 0;
	void AnimateHinges(float _isDraggingScale) {
		if (Cam == null) return; // shutting down
		
		bool isHeld = DesignTableManager.Me.IsGrabbed(gameObject);
		bool isHidden = isHeld == false && DesignTableManager.Me.IsSnapActive(transform) == false;
		
		if (DesignTableManager.Me.m_isOverHarness) m_snapScaleIn = 0;
		else m_snapScaleIn = Mathf.Lerp(m_snapScaleIn, 1, .2f);
		var snapScaleIn = Mathf.SmoothStep(0f, 1f, Mathf.Max(0f, m_snapScaleIn - .5f) * 2);

		var targetForDistanceCheck = (DesignTableManager.Me.GrabbedBlock?.transform ?? transform).position;
		const float c_innerFadeRadiusSqrd = 20 * 20;
		const float c_outerFadeRadiusSqrd = 30 * 30;
		
		var hingeScale = NGManager.Me.m_snapVisualScale;
		var camPos = Cam.transform.position;
		float baseScale = DesignTableManager.CurrentBlockBuildScale;
		var block = GetBlock();
		if (block != null) {
			foreach (Transform t in block.m_toHinges) {
				var scaleMultiplierDistanceSqrd = (t.position - targetForDistanceCheck).xzSqrMagnitude();
				var scaleMultipler = Mathf.Clamp01((c_outerFadeRadiusSqrd - scaleMultiplierDistanceSqrd) / (c_outerFadeRadiusSqrd - c_innerFadeRadiusSqrd));
				var (isInvalidBase, hingeTransform) = GetHingeState(t, block);
				bool isIllegal = isHidden || (isHeld == false && isInvalidBase) || m_isValidSnap == false;
				var c = hingeTransform;
				if (_isDraggingScale < .0001f || isIllegal || DesignTableManager.Me.m_isOverHarness) {
					c.gameObject.SetActive(false);
				} else {
					var parentScale = c.parent.lossyScale;
					if (parentScale.sqrMagnitude < .0001f*.0001f) parentScale = Vector3.one;
					var camScale = (camPos - c.position).magnitude * .02f;
					if (DesignTableManager.IsMakingBuilding == false) camScale = 1;
					float combinedScale = baseScale * _isDraggingScale * snapScaleIn * camScale * scaleMultipler;
					c.gameObject.SetActive(true);
					c.localScale = new Vector3(hingeScale.x / parentScale.x, hingeScale.y / parentScale.y, hingeScale.z / parentScale.z) * combinedScale;
					c.localPosition = Vector3.forward * (.5f * baseScale / parentScale.z);//Mathf.Abs(Mathf.Sin(Time.time * 3f) * combinedScale * .1f / parentScale.z);
				}
			}
		}
	}

	void CreateSnaps() {
		var block = GetBlock();
		if (block != null) {
			var hingeScale = NGManager.Me.m_snapVisualScale;
			foreach (Transform t in block.m_toHinges) {
				var go = Instantiate(GlobalData.Me.m_snapPointPrefab);
				go.transform.SetParent(t);
				go.transform.localPosition = Vector3.zero;
				go.transform.localRotation = Quaternion.Euler(-90, 0, 0);//Quaternion.identity;
				var parentScale = go.transform.parent.localScale;
				if (parentScale.sqrMagnitude < .0001f*.0001f) parentScale = Vector3.one;
				go.transform.localScale = new Vector3(hingeScale.x / parentScale.x, hingeScale.y / parentScale.y, hingeScale.z / parentScale.z) * .001f;
				go.SetLayerRecursively(gameObject.layer);
				go.AddComponent<Hinge>();
				m_snapVisuals.Add(go);
			}
		}
	}

	public static void Create(int _inputID, string _id, Vector3 _pos, System.Action<GameObject> _onComplete, bool _startDrag = true, Transform _parent = null, int _setLayer = 31, float _initialScale = 0, bool _isFromPalette = false) {
		Block.Load(_id, (_g) => {
			Attach(_inputID, _g, _id, _pos, _startDrag, _parent, _setLayer, _initialScale, _isFromPalette);
			_onComplete(_g);
		});
	}
	public static void Attach(int _inputID, GameObject _g, string _id, Vector3 _pos, bool _startDrag = true, Transform _parent = null, int _setLayer = 31, float _initialScale = 0, bool _isFromPalette = false) {
		var block = _g;
		var drag = block.gameObject.AddComponent<DTDragBlock>();
		drag.DisableLights();
		drag.InitialiseScale(_initialScale);
		block.transform.position = _pos;
		drag.EnableTurntableClip(!_startDrag);
		drag.m_isFromPalette = _isFromPalette;
		if (_parent == null) _parent = DesignTableManager.Me.BlockHolder;
		block.transform.SetParent(_parent, true);
		drag.ClearLastSnapped();
		DesignTableManager.Me.AddBuffIcon(null, _g);
		if (_setLayer != -1)
			block.SetLayerRecursively(_setLayer);
		if (_startDrag) {
			drag.OnDragStart();
			drag.SetInputId(_inputID);
			drag.StartDrag(true);
			drag.m_dragPlaneOnPosition = false;
		}
	}

	public class DragObjectState
	{
		public bool m_isFromTable;
		public bool m_isValidResult;
		public Quaternion m_GlobalRotation;
		public Vector3 m_GlobalPosition;
		public string m_name = string.Empty;
		public DragObjectState() { }
		public DragObjectState(DragObjectState dragObjectStateCopy)
		{
			m_isFromTable = dragObjectStateCopy.m_isFromTable;
			m_isValidResult = dragObjectStateCopy.m_isValidResult;
			m_GlobalRotation = dragObjectStateCopy.m_GlobalRotation;
			m_GlobalPosition = dragObjectStateCopy.m_GlobalPosition;
			m_name = dragObjectStateCopy.m_name;
		}
	}
}

public class Hinge : MonoBehaviour {}
